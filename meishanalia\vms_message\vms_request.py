import json
from dataclasses import dataclass
#from dataclasses_json import dataclass_json
import datetime
import time
import json
from enum import Enum
from meishanalia.common.constant import VMS_TRUCK_REQUEST_TYPE
from common.logger import logger
from meishanalia.vms_message.msgs import TruckRequestMsg, MsgHead, make_msg
from meishanalia.cache.vms_request_cache import VmsRequestCache
from cache import vehicle_cache


class VmsReqeustEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj,datetime.datetime):
            return obj.strftime("datetime.datetime(%Y, %m, %d, %H, %M, %S)")
        else:
            return json.JSONEncoder.default(self,obj)

def vms_request_handler():
    msgs = list()
    online_trucks = vehicle_cache.get_online_trucks()
    # logger.info(f"shaowanjia-add: online_trucks:{online_trucks},{len(online_trucks)}")
    for truck in online_trucks:
        requests = VmsRequestCache().pop_all_request(truck)
        # logger.info(f"shaowanjia-add: requests:,{requests},{len(requests)}")
        if len(requests) > 0:
            logger.info(f'vehicle_name:{truck} type:{type(truck)} get vms requests:{requests}')
            msg_body = TruckRequestMsg()
            msg_body.TRUCK_NO = truck
            msg_body.REQUEST_TYPE = 'PATH'
            # msg_body.GEN_TIME = requests[-1]['timestamp']
            msg_body.GEN_TIME = eval(requests[-1])['timestamp']  # 2022.01.18 shaowanjia modified
            msg = make_msg(VMS_TRUCK_REQUEST_TYPE,msg_body.__dict__)
            msg_json = json.dumps(msg.__dict__,cls=VmsReqeustEncoder).encode('utf-8')
            key_json = truck.encode('utf-8')
            # logger.debug(f'key_json:{key_json},msg_json:{msg_json},truck:{truck.__dict__}')
            logger.debug(f'key_json:{key_json},msg_json:{msg_json},truck:{truck}')
            msgs.append((key_json,msg_json))
    return msgs



#if __name__ == '__main__':
