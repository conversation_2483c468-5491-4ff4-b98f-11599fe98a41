import socket
import struct
import time,datetime
import traceback

from common.logger import logger
from proto.cache_pb2 import GantryStoreInfo
from model.tcp_client import PersistentTcpClient
from plc.model import GANTRY_STORE_PORT

class GantryStorer(object):
    def __init__(self, gantry_no, ip = '', port = GANTRY_STORE_PORT,interval = 0.2):
        self._gantry_no = gantry_no
        self._msg_no = 0
        self._ip = ip
        self._port = port
        self._sock = None
        self._interval = interval
        if len(self._ip) > 0:
            self._sock = PersistentTcpClient(server_ip = self._ip, server_port = port, timeout = 0.1, message_header = f"{self._gantry_no}")

    @property
    def interval(self):
        return self._interval

    @property
    def msg_no(self):
        return self._msg_no

    @interval.setter
    def interval(self, interval):
        self._interval = interval

    def gantry_no(self):
        return self._gantry_no

    def increase_msg_no(self):
        self._msg_no = self._msg_no + 1
        return self._msg_no

    def store(self, info):
        if not self._sock:
            logger.warning(f"gantry_no:{self._gantry_no}({self._ip}:{self._port} socket invalid)")
            return False
        data = struct.pack('!HHHHHHHHHH',info.header,info.msg_no,info.gantry_no,info.truck_no,\
                            info.ctrl_mode,info.warning_level,info.res2,info.res3,info.res4,info.end)
        return self._sock.send_data(data)



if __name__ == '__main__':
    #python -m ,for No module named 'model.tcp_client'
    #python -m plc.gantry_storer
    from google.protobuf.json_format import MessageToDict
    from plc.model import STORE_GANTRY_IP_MAP
    gantry_no = 184
    truck_no = 500
    info = GantryStoreInfo()
    info.msg_no  = 0
    info.truck_no = truck_no
    info.ctrl_mode = 1
    info.gantry_no = gantry_no
    (ip,port) = STORE_GANTRY_IP_MAP.get(gantry_no,('localhost',GANTRY_STORE_PORT))
    gantry_storer = GantryStorer(gantry_no, ip = ip, port = port)
    while True:
        print(f"#################################")
        try:
            info.msg_no = info.msg_no + 1
            info.timestamp_ns = int(time.time() * 1000000000)
            print(f'send info:{MessageToDict(info, including_default_value_fields=True,preserving_proto_field_name=True)}')
            gantry_storer.store(info)
        except Exception as e:
            print(f"server error: {e}, trace:{traceback.format_exc()}")
        time.sleep(1)
