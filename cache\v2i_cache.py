from cache.client import <PERSON>ache<PERSON>lient
from cache.vehicle_cache import get_online_trucks

VEHICLE_INFO_INTEGRATION_KEY_PREFIX = 'vehicle-info-integration-key'
V2V_COMMAND_KEY_PREFIX = 'v2v-command-key'
V2I_LOCALIZATION_KEY_PREFIX = 'v2i-localization-key'


def set_vehicle_integration_info(type, truck_no, content):
    key = f'{VEHICLE_INFO_INTEGRATION_KEY_PREFIX}:{type}:{truck_no}'
    CacheClient().client().set(key, content, px=500)


def get_vehicle_integration_info(type):
    keys = []
    truck_nos = []
    online_truck_nos = get_online_trucks()
    for online_truck_no in online_truck_nos:
        truck_nos.append(online_truck_no)
        key = f'{VEHICLE_INFO_INTEGRATION_KEY_PREFIX}:{type}:{online_truck_no}'
        keys.append(key)
    return truck_nos, CacheClient().client().mget(keys)


def set_v2v_command(push_v2v_command_request):
    key = f'{V2V_COMMAND_KEY_PREFIX}:{push_v2v_command_request.type}'
    CacheClient().client().set(key, push_v2v_command_request.content, px=2000)


def get_v2v_command(type):
    key = f'{V2V_COMMAND_KEY_PREFIX}:{type}'
    return CacheClient().client().get(key)


def set_v2i_localization(truck_no, v2i_localization):
    key = f'{V2I_LOCALIZATION_KEY_PREFIX}:{truck_no}'
    CacheClient().client().set(key, v2i_localization, px=500)


def get_v2i_localization():
    keys = []
    truck_nos = []
    online_truck_nos = get_online_trucks()
    for online_truck_no in online_truck_nos:
        truck_nos.append(online_truck_no)
        key = f'{V2I_LOCALIZATION_KEY_PREFIX}:{online_truck_no}'
        keys.append(key)
    return CacheClient().client().mget(keys)
