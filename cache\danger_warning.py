from common.singleton import Singleton
from proto.fabupilot.planning import planning_info_pb2
from cache.client import CacheClient

DANGER_WARNING_ = "DANGER_WARNING_"

class DangerWarningCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def set_danger_warning(self, vehicle_name_at,  danger_warning_info):
        key = f"{DANGER_WARNING_}{vehicle_name_at}"
        return self._redis.set(key, danger_warning_info, ex=2)


if __name__ == '__main__':
    # DangerWarningCache().set_danger_warning()
    print(DangerWarningCache().get_planning_inifo("AT801"))
