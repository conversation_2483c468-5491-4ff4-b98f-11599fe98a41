import datetime, time
import json
import math
import re
import inspect
import numpy as np
import utm
from google.protobuf import json_format
from google.protobuf.message import Message
from shapely import geometry
from shapely.geometry import Point, Polygon
from common.logger import logger
from common.constant import STACKER_VERTICAL_YARDS,STACKER_VERTICAL_BAYS_E2W,STACKER_HORIZONTAL_YARDS, CRANE_LANE_THETA
from typing import List

import shapely

def get_point_to_line_distance(pointX, pointY, lineX1, lineY1, lineX2, lineY2):
    a = lineY2 - lineY1
    b = lineX1 - lineX2
    c = lineX2 * lineY1 - lineX1 * lineY2
    dis = (math.fabs(a * pointX + b * pointY + c)) / (math.pow(a * a + b * b, 0.5))
    return dis


def is_in_width_ploygon(pointX, pointY, border_centerX1, border_centerY1, border_centerX2, border_centerY2, width):
    border1_array = np.asarray([border_centerX1, border_centerY1])
    border2_array = np.asarray([border_centerX2, border_centerY2])
    point_array = np.asarray([pointX, pointY])
    '''
    logger.debug(
        f'is in ploygon:point:({pointX},{pointY}),point_array:{point_array};border1:({border_centerX1},{border_centerY1}),border1_array:{border1_array};border2:({border_centerX2},{border_centerY2}),border2_array:{border2_array}')
    '''
    point_to_border2 = point_array - border2_array
    border1_to_border2 = border1_array - border2_array
    mul = np.dot(point_to_border2, border1_to_border2)
    if mul < 0:
        # logger.debug('is in ploygon:point:{} out of border2:{}'.format(point_array, border2_array))
        return False

    point_to_border1 = point_array - border1_array
    border2_to_border1 = border2_array - border1_array
    mul = np.dot(point_to_border1, border2_to_border1)
    if mul < 0:
        # logger.debug('is in ploygon:point:{} out of border1:{}'.format(point_array, border1_array))
        return False

    distance = get_point_to_line_distance(pointX, pointY, border_centerX1, border_centerY1, border_centerX2,
                                          border_centerY2)
    # logger.debug('is in ploygon:distance:{}, width:{}'.format(distance, width))
    if distance >= width / 2:
        # logger.debug('is in ploygon:distance:{} out of width/2:{}'.format(distance, width / 2))
        return False
    return True


def from_lonlat_to_utm(lng, lat):
    try:
        r = utm.from_latlon(lat, lng)
        return r[0], r[1]
    except Exception as e:
        logger.warning("to_utm error {}".format(e))
    return -1, -1


def from_utm_to_latlon(utm_x, utm_y, mesg = ''):
    try:
        r = utm.to_latlon(utm_x, utm_y, 51, 'R')
        return r[0], r[1]
    except Exception as e:
        logger.warning(f"{mesg} to_utm error:{e}")
    return -1, -1


def end_num(string):
    if string is None:
        return False
    text = re.compile(r".*[0-9]$")
    if text.match(string):
        return True
    else:
        return False


def to_dict(self):  # c.name.lower(),or value will None
    return {c.name: getattr(self, c.name, None) for c in self.__table__.columns}


def from_dict(self, dict_context):
    for key, value in dict_context.items():
        setattr(self, key, value)


class MyJosnEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Message):
            return json.loads(json_format.MessageToJson(o))
        if isinstance(o, datetime.datetime):
            return (o.timestamp())
        else:
            return super().default(o)

class MyJosnEncoder2(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Message):
            return json.loads(json_format.MessageToJson(o))
        if isinstance(o,datetime.datetime):
            return o.strftime("datetime.datetime(%Y, %m, %d, %H, %M, %S)")
        else:
            return json.JSONEncoder.default(self,o)

class MyJosnEncoder3(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Message):
            return json.loads(json_format.MessageToJson(o))
        elif isinstance(o, (datetime.datetime, datetime.date, datetime.time)):
            return str(o)
        return super(MyJosnEncoder3, self).default(o)

class MyJosnEncoder4(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Message):
            return json.loads(json_format.MessageToJson(o,preserving_proto_field_name=True))#字段名称不发生变化保留下划线
        if isinstance(o, datetime.datetime):
            return (o.timestamp())
        else:
            return super().default(o)

def is_gantry_yard_wi(wi):
    if (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'UNLOAD' and wi.get('YC_KIND') != 1 and wi.get('CTN_NO') != 'S') \
            or (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'LOAD') \
            or wi.get('WI_TYPE') == 'YARD':
        return True
    else:
        return False


def is_stacker_vertical_wi(wi):
    if wi.get('YC_KIND') == 1 and wi.get('TO_POS','')[:2] in STACKER_VERTICAL_YARDS:
        return True
    else:
        return False


def is_stacker_vertical_e2w_wi(wi):
    if is_stacker_vertical_wi(wi) and wi.get('TO_POS','')[2:4] in STACKER_VERTICAL_BAYS_E2W:
        return True
    else:
        return False


def is_stacker_horizontal_wi(wi):
    if wi.get('YC_KIND') == 1 and wi.get('TO_POS','')[:2] in STACKER_HORIZONTAL_YARDS:
        return True
    else:
        return False

def is_stacker_wi(wi):
    if is_stacker_vertical_wi(wi) or is_stacker_horizontal_wi(wi):
        return True
    else:
        return False


'''
def is_gantry_yard_wi(wi):
    return is_yard_wi(wi)

def is_stacker_wi(wi):
    return False
'''

def is_yard_wi(wi):
    if (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'UNLOAD') \
            or (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'LOAD') \
            or wi.get('WI_TYPE') == 'YARD':
        return True
    else:
        return False

def is_gantry_yard_entrance_wi(wi):
    if is_gantry_yard_wi(wi) and len(wi.get('TO_POS','')) == 2:
        return True
    else:
        return False

def is_crane_wi(wi):
    if (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'LOAD') \
        or (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'UNLOAD'):
        return True
    else:
        return False

def is_crane_valid_wi(wi):
    if is_crane_wi(wi) and wi.get('TO_POS','').startswith('CR'):
        return True
    else:
        return False

def is_fabu_wi(wi):
    if wi.get('REMARK3') == 'FABU':
        return True
    else:
        return False


def is_container_wi(wi):
    wi_type = str(wi.get('WI_TYPE'))
    if wi_type == 'LOAD' or wi_type == 'DSCH' or wi_type == 'YARD':
        return True
    else:
        return False


def is_may_lock_wi(wi):
    if (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'UNLOAD')\
            or (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'UNLOAD') \
            or (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'LOAD' and wi.get('LOCK_PAVILION')):
        return True
    else:
        return False

def is_off_work_wi(wi):
    wi_act = str(wi.get('WI_ACT'))
    if wi_act == 'STOP_WORK' or wi_act == 'LEAVE_SPACE' or wi_act=='STOP':
        return True
    else:
        return False

def is_dispatch_wi(wi):
    if (wi.get('REMARK2') == 'DISPATCH' and wi.get('WI_STATUS') == 'FINISH') \
      or (wi.get('WI_STATUS') == 'DISPATCH'):
        return True
    else:
        return False

def is_safe_mode_wi(wi):
    return wi.get('SAFE_MODE',True)

def is_driverless_wi(wi):
    if 'driverless' in str(wi.get('BUSINESS_SCENE','')) and not is_safe_mode_wi(wi):
        return True
    else:
        return False

def need_correct_twin(wi):
    if is_yard_wi(wi) and wi.get('WI_ACT') == 'UNLOAD' and \
      (wi.get('TRUCK_POS') == 'F' or wi.get('TRUCK_POS') == 'A') \
      and wi.get('TWIN_FLAG') == 'N':
        return True
    return False

def is_yard_bay_to_enter(wi):
    if wi is not None and is_driverless_wi(wi) and is_yard_wi(wi) and \
      (t_to_pos:=wi.get('T_TO_POS')) is not None and (c_to_pos:=wi.get('C_TO_POS')) is not None and \
      len(t_to_pos) == 2 and len(c_to_pos) == 4 and t_to_pos[:2] == c_to_pos[:2]:
        return True
    return False

def parse_cr_bay_no(to_pos):
    if str(to_pos).startswith('CR') and str(to_pos)[2:4].isdigit() and len(pos_split:=str(to_pos).split(' ')) > 1 and pos_split[1][:-1].isdigit():
        return pos_split[1][:-1]
    return None

def parse_cr_bay_no_by_vessel(vessel_pos):
    if pos and pos.isdigit():
        return pos[:2]
    return None

def is_to_cr_may_by_bay_wi(wi):
    if (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'UNLOAD') and parse_cr_bay_no(wi.get('TO_POS')) != None:
        return True
    else:
        return False


def is_to_cr_by_bay_wi(wi):
    if (wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'UNLOAD') and parse_cr_bay_no(wi.get('TO_POS')) != None and wi.get('POS_TYPE') == 'BAY_TYPE':#TODO
        return True
    else:
        return False

def is_cycle_mode_wi(wi):
    if wi.get('WI_TYPE') == 'LOAD' and wi.get('WI_ACT') == 'UNLOAD':
        return True
    else:
        return False

def is_support_tos_wi(wi):
    return True
    #横向堆高机只支持大箱和中箱
    if is_stacker_horizontal_wi(wi) and wi.get('TWIN_FLAG') in ['T','Y']:
        return False
    else:
        return True

def is_set_rest_point_wi(wi):
    #横向堆高机只支持大箱和中箱,否则设定特定休息区
    if is_stacker_horizontal_wi(wi) and wi.get('TWIN_FLAG') in ['T','Y']:
        return True
    else:
        return False

#是否边装边卸模式下符合条件的下条指令:卸船装箱指令或收工离场指令
def is_cycle_mode_next_wi(wi):
    if (wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'LOAD') or is_off_work_wi(wi):
        return True
    else:
        return False

def time_valid(info_time_ns, ex = 60.0, cur_time = time.time()):
    if cur_time is None:
        return True
    return cur_time - info_time_ns / 1000000000.0 < ex


def parse_lock_pavilion(lock_pavilion):
    pavilion_list = []
    if -1 == lock_pavilion.find('-'):
        pavilion_list.append(lock_pavilion)
    else:
        bridge_index = lock_pavilion.split('Y', 1)[0]
        pavilion_index_list = lock_pavilion.split('Y', 1)[1].split('-', -1)
        for pavilion_index in pavilion_index_list:
            pavilion_list.append(bridge_index + "Y" + pavilion_index)
    return pavilion_list

def parse_lock_bridge_index(lock_pavilion):
    if not lock_pavilion:
        return ''
    return lock_pavilion.split('Y', 1)[0]


def parse_map_ship_bridge_index(ship_bridge):
    if not ship_bridge:
        return ''
    #'bridge_10_'
    if (match:=re.match(r'bridge_(\d+)_', ship_bridge)):
        return match.group(1)
    else:
        logger.warning(f"fail to pase ship_bridge:{ship_bridge}")
        return ''


def calc_crc(string):
    data = bytearray.fromhex(string)
    crc = 0xFFFF
    for pos in data:
        crc ^= pos
        for i in range(8):
            if ((crc & 1) != 0):
                crc >>= 1
                crc ^= 0xA001
            else:
                crc >>= 1
    return hex(((crc & 0xff) << 8) + (crc >> 8))


def map_point_to_line(x1, y1, x2, y2, slope2):
    """
    将某点映射到对应直线上
    """

    # 垂线斜率
    slope1 = -1 / slope2

    # 计算第一条直线距离Y轴的截距
    intercept1 = y1 - slope1 * x1

    # 计算第二条直线距离Y轴的截距
    intercept2 = y2 - slope2 * x2

    # y1 = slope1 * x1 + intercept1
    # y2 = slope2 * x2 + intercept2
    # 联立方程求交点的 x 坐标
    x_intersect = (intercept2 - intercept1) / (slope1 - slope2)

    # 使用其中一条直线的方程求交点的 y 坐标（两条直线的 y 坐标是一样的，因为交点在两条直线上）
    y_intersect = slope1 * x_intersect + intercept1

    return x_intersect, y_intersect


def is_in_polygon(square, verify_point):
    line = geometry.LineString(square)
    point = geometry.Point(verify_point)
    polygon = geometry.Polygon(line)
    return polygon.contains(point)



def is_crane_in_vessel(crane_no, crane_pos, ship, dist_from_center_to_offset_x = 0, dist_from_center_to_offset_y = 0):
    crane_lane_theta_cos = math.cos(CRANE_LANE_THETA)
    crane_lane_theta_sin = math.sin(CRANE_LANE_THETA)
    ret = False
    #偏移至基准桥吊小车
    ship_prow_utm_x = ship.system_prow.utm_x if ship.system_prow.HasField('utm_x') else ship.prow.utm_x
    ship_prow_utm_y = ship.system_prow.utm_y if ship.system_prow.HasField('utm_y') else ship.prow.utm_y
    ship_stern_utm_x = ship.system_stern.utm_x if ship.system_stern.HasField('utm_x') else ship.stern.utm_x
    ship_stern_utm_y = ship.system_stern.utm_y if ship.system_stern.HasField('utm_y') else ship.stern.utm_y
    crane_center_pos_utm_x = crane_pos[0] - dist_from_center_to_offset_x
    crane_center_pos_utm_y = crane_pos[1] - dist_from_center_to_offset_y
    prow_map_x = ship_prow_utm_x * crane_lane_theta_cos + ship_prow_utm_y * crane_lane_theta_sin
    stern_map_x = ship_stern_utm_x * crane_lane_theta_cos + ship_stern_utm_y * crane_lane_theta_sin
    crane_centere_map_pos_x = crane_center_pos_utm_x * crane_lane_theta_cos + crane_center_pos_utm_y * crane_lane_theta_sin
    logger.debug(f"[is_crane_in_vessel] crane_no:{crane_no},crane_pos:({crane_pos[0]},{crane_pos[1]})"
                  f",crane_center_pos:({crane_center_pos_utm_x},{crane_center_pos_utm_y}),crane_centere_map_pos_x:{crane_centere_map_pos_x})"
                  f",prow:({ship_prow_utm_x},{ship_prow_utm_y}),prow_map_x:{prow_map_x}"
                  f",stern:({ship_stern_utm_x},{ship_stern_utm_y}),stern_map_x:{stern_map_x}")
    if (crane_centere_map_pos_x >= prow_map_x and crane_centere_map_pos_x <= stern_map_x) or \
        (crane_centere_map_pos_x <= prow_map_x and crane_centere_map_pos_x >= stern_map_x):
        ret = True
    return ret


def FUNCTION_NAME():
    return inspect.currentframe().f_back.f_code.co_name

'''
FUNCTION_TIME_SPAN(func=time_test,span_time=0.1)(1)
'''
def FUNCTION_TIME_SPAN(func,span_time=0.1,mesg=""):
    def wrapper(*arg,**kwarg):
        start_time = time.time()
        ret = func(*arg,**kwarg)
        end_time = time.time()
        if (execeed_time:=(end_time - start_time)) > span_time:
            logger.warning(f"FUNC_NAME:{func.__name__}(arg={arg},kwarg={kwarg}) over cost time,mesg:{mesg}!"
            f"round time:{round(execeed_time)},execeed time:{execeed_time},span_time:{span_time}")
        return ret
    return wrapper


def FUNCTION_TIME_SPAN_LOG(func,span_time=0.1,mesg=""):
    def wrapper(*arg,**kwarg):
        start_time = time.time()
        ret = func(*arg,**kwarg)
        end_time = time.time()
        if (execeed_time:=(end_time - start_time)) > span_time:
            logger.warning(f"FUNC_NAME:{func.__name__}(arg={arg},kwarg={kwarg}) over cost time,mesg:{mesg}!"
            f"round time:{round(execeed_time)},execeed time:{execeed_time},span_time:{span_time}")
        return ret
    return wrapper


def FUNCTION_TIME_SPAN_LOG(log_handler,span_time=0.1):
    last_time = time.time()
    def handler(msg):
        nonlocal last_time
        now_time = time.time()
        if (execeed_time:=(now_time - last_time)) > span_time:
            last_time = now_time
            log_handler(f"{msg}")
    return handler



'''
@FUNC_TIME_SPAN_DECORATEOR(span_time=0.1,mesg='')
def time_test(arg):
    print('before sleep:Function is decorated!')
    time.sleep(1)
    print('after sleep:Function is decorated!')
'''

def FUNC_TIME_SPAN_DECORATEOR(span_time=0.1,mesg=''):
    def decorator(func):
        def wrapper(*arg,**kwarg):
            start_time = time.time()
            ret = func(*arg,**kwarg)
            end_time = time.time()
            if (execeed_time:=(end_time - start_time)) > span_time:
                logger.warning(f"FUNC_NAME:{func.__name__}(arg={arg},kwarg={kwarg}) over cost time,mesg:{mesg}!!"
                f"round time:{round(execeed_time)},execeed time:{execeed_time},span_time:{span_time}")
            return ret
        return wrapper
    return decorator

_arrive_area_map = None
_arrive_area_data_list: List[str] = [
    "data/meishan/arrive/bridge.json",
]
_arrive_area_geom_tree: shapely.STRtree = None
_arrive_area_geom_indices: List = []


def init_arrive_area_data():
    global _arrive_area_map, _arrive_area_geom_tree
    if _arrive_area_map is not None:
        return
    _arrive_area_map = {}
    _arrive_area_geom_tree = None
    arrive_area_polys = []
    for arrive_area_data_file in _arrive_area_data_list:
        arrive_area_data = json.load(open(arrive_area_data_file))
        for arrive_area_name, arrive_area_data in arrive_area_data.items():
            arrive_area_poly = shapely.Polygon(
                [
                    (point["pointX"], point["pointY"])
                    for point in arrive_area_data["boundary"]
                ]
            )
            arrive_area_polys.append(arrive_area_poly)
            _arrive_area_map[arrive_area_name] = arrive_area_data
            _arrive_area_geom_indices.append(arrive_area_data)
    _arrive_area_geom_tree = shapely.STRtree(arrive_area_polys)


init_arrive_area_data()


def get_arrive_areas_by_location(location):
    utm_x, utm_y = location
    ret = _arrive_area_geom_tree.query(
        shapely.Point(utm_x, utm_y), predicate="within"
    ).tolist()
    if ret:
        return [_arrive_area_geom_indices[i] for i in ret]
    return []


def get_arrive_area_by_name(name):
    if _arrive_area_map is None:
        return
    return _arrive_area_map.get(name, None)

def get_ms_change_station_point(change_station_name):
    station_data = {
        "MSBSS02": {"pointX": 404865.83116376947, "pointY": 3295424.5271421247},
    }
    return station_data.get(change_station_name, None)

def distance_from_point_to_polygon(point, block_rect):
    polygon = Polygon(
        [
            (block_rect.left_top_point.utm_x, block_rect.left_top_point.utm_y),
            (block_rect.right_top_point.utm_x, block_rect.right_top_point.utm_y),
            (block_rect.right_bottom_point.utm_x, block_rect.right_bottom_point.utm_y),
            (block_rect.left_bottom_point.utm_x, block_rect.left_bottom_point.utm_y),
        ]
    )

    # 计算点到多边形的最短距离
    return point.distance(polygon)

_yard_overtaking_lanes_map = None
_yard_overtaking_lanes_data_list: List[str] = [
    "data/meishan/map/yard_overtaking_lanes.json",
]
_yard_overtaking_lanes_geom_tree: shapely.STRtree = None
_yard_overtaking_lanes_geom_indices: List = []


def init_yard_overtaking_lanes_data():
    global _yard_overtaking_lanes_map, _yard_overtaking_lanes_geom_tree
    if _yard_overtaking_lanes_map is not None:
        return
    _yard_overtaking_lanes_map = {}
    _yard_overtaking_lanes_geom_tree = None
    yard_overtaking_lanes_polys = []
    for yard_overtaking_lanes_data_file in _yard_overtaking_lanes_data_list:
        yard_overtaking_lanes_data = json.load(open(yard_overtaking_lanes_data_file))
        for yard_overtaking_lanes_name, yard_overtaking_lanes_data in yard_overtaking_lanes_data.items():
            yard_overtaking_lanes_poly = shapely.Polygon(
                [
                    (point["pointX"], point["pointY"])
                    for point in yard_overtaking_lanes_data["boundary"]
                ]
            )
            yard_overtaking_lanes_polys.append(yard_overtaking_lanes_poly)
            _yard_overtaking_lanes_map[yard_overtaking_lanes_name] = yard_overtaking_lanes_data
            _yard_overtaking_lanes_geom_indices.append(yard_overtaking_lanes_data)
    _yard_overtaking_lanes_geom_tree = shapely.STRtree(yard_overtaking_lanes_polys)


init_yard_overtaking_lanes_data()


def get_yard_overtaking_lanes_by_location(location):
    utm_x, utm_y = location
    ret = _yard_overtaking_lanes_geom_tree.query(
        shapely.Point(utm_x, utm_y), predicate="within"
    ).tolist()
    if ret:
        return [_yard_overtaking_lanes_geom_indices[i] for i in ret]
    return []

def parse_road_name(road_name):
    if '&' not in road_name:
        return [road_name, ""]

    left, right = road_name.split('&')

    if len(right) == 2:
        return [left, right]
    elif len(right) == 1:
        return [left, left[0] + right]
    else:
        return [left, ""]

def get_yard_work_mode(yard_dict, yard_name):
    yard = yard_dict.get(yard_name)
    return yard.work_mode if yard else None


if __name__ == '__main__':
    print(parse_lock_pavilion('10Y1-2'))
    print(parse_cr_bay_no('CR34 11A'))