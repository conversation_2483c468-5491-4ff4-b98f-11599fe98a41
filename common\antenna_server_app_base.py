#!/usr/bin/python3

import multiprocessing
from concurrent import futures
import time
import traceback
import os
import grpc
from google.protobuf.text_format import MessageToString
from google.protobuf.json_format import MessageToJson
from common.logger import logger

from cache.server_cache import ServerNode<PERSON>acheBase
from proto.cache_pb2 import ServerNode
from config.config_manage import ConfigManage
from cache import vehicle_cache


def wrap_behavior(old, name):
    def new_behavior(request_or_iterator, servicer_context):
        before = time.time()
        try:
            res = old(request_or_iterator, servicer_context)
        except Exception as e:
            after = time.time()
            logger.warning('grpc_server_fail:' + name +
                         ' duration time:' + str(after - before))
            logger.debug(
                f"grpc_server_fail:{name},err:{e},trace:{traceback.format_exc()}")
            if (after - before) > 0.1:
                logger.warning('fail method:' + name +
                             ' duration overtime:' + str(after - before))
            raise e
        after = time.time()
        '''
        if hasattr(request_or_iterator, 'vehicle_name'):
            logger.debug(
                f'server pid:{os.getpid()},client:{servicer_context.peer()},vehicle_name:{request_or_iterator.vehicle_name},grpc_server_complete:{name},duration time:{(after - before)}')
        else:
            logger.debug(f'server pid:{os.getpid()},client:{servicer_context.peer()},grpc_server_complete:{name},duration time:{(after - before)}')
        '''
        '''
        logger.debug(f"method:{name},request:{MessageToJson(request_or_iterator,preserving_proto_field_name=True)}")
        logger.debug(f"method:{name},response:{MessageToJson(res,preserving_proto_field_name=True)}")
        '''
        if (after - before) > 0.1:
            if hasattr(request_or_iterator, 'vehicle_name'):
                logger.warning(
                    f'server pid:{os.getpid()},client:{servicer_context.peer()},vehicle_name:{request_or_iterator.vehicle_name},ok method:{name},duration overtime:{(after - before)}')
            else:
                logger.warning(
                    f'server pid:{os.getpid()},client:{servicer_context.peer()},ok method:{name},duration overtime:{(after - before)}')
        if hasattr(res, 'base'):
            res.base.response_time_ms = 1000 * (after - before)
        return res

    return new_behavior


class CountServerInterceptor(grpc.ServerInterceptor):
    def intercept_service(self, continuation, handler_call_details):
        original_handler = continuation(handler_call_details)
        return grpc.unary_unary_rpc_method_handler(
            wrap_behavior(original_handler.unary_unary, handler_call_details.method),
            request_deserializer=original_handler.request_deserializer,
            response_serializer=original_handler.response_serializer)


class AntennaServerAppBase(object):
    def __init__(self):
        self._port = 6789
        self._fast_port = 6788
        self._workers = []
        self._rpc_process_num = 12
        self._fast_rpc_process_num = 4
        self._max_workers = 12


    def start_process(self, target, name=None):
        worker = multiprocessing.Process(target=target, name=name)
        worker.start()
        if name != None:
            logger.info(f'start process:{name},process-pid:{worker.pid}')
        else:
            logger.info(f'start process-pid:{worker.pid}')

        self._workers.append(worker)

    def add_rpc_server(self, server, add_web_server=True, add_fast_rpc_server=False):
        pass

    def run_rpc_server(self, max_workers, port, add_web_server=True, add_fast_rpc_server=False):
        # logger.info(f"run_rpc_server:max_workers:{max_workers},port:{port}")
        options = (('grpc.so_reuseport', 1),
                  #1:允许在没有活动 RPC 请求时发送 keep-alive 控制包
                  #若设为0如果在有长时间没有活跃 RPC 的连接，那么这可能会导致不必要的连接关闭。
                  ('grpc.keepalive_permit_without_calls', 1),
                  ('grpc.keepalive_timeout_ms', 5000),
                  ('grpc.keepalive_time_ms', 10000),
                  #('grpc.server_idle_timeout', 60*60) #设置连接空闲超过3600秒断连,不利于链接池、非周期性请求的，或设置大点
                  )
        #maximum_concurrent_rpcs
        #max_workers设置最大连接数
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers, ),
                             interceptors=[CountServerInterceptor()],
                             options=options)
        self.add_rpc_server(server, add_web_server, add_fast_rpc_server)
        server.add_insecure_port('[::]:{}'.format(port))
        server.start()
        server.wait_for_termination()
        # while True:
        #     try:
        #         time.sleep(1)
        #         pr.dump_stats("perf.out")
        #     except KeyboardInterrupt:
        #         server.stop(None)


    def run_web_service(self):
        pass

    def need_start_common_process(self):
        mode_valids = [ServerNode.SINGLE, ServerNode.CLUSTER_MASTER, ServerNode.CLUSTER_BACK, ServerNode.CLUSTER_SLAVE]
        nodes = ConfigManage().get_config_server_nodes()
        for node in nodes:
            if node.mode in mode_valids:
                return True
        return False

    def register_server_node(self):
        servers = ConfigManage().get_config_server_nodes()
        if len(servers) == 0:
            logger.warning(f"ATTENTION:no server node!!!")
            return False
        node_cache = ServerNodeCacheBase()
        for sever in servers:
            if node_cache.check_server_node_in(sever):
                print(f"ATTENTION:node_name:{sever.node_name} already register")
                #return False
            #ToDo 排除DEV
            if not node_cache.check_server_node_valid(sever):
                logger.warning(f"ATTENTION:check server node:{sever.node_name} conflict!!!")
                return False
            node_cache.set_server_node(sever)
        return True


    def check_server_node_white_lists(self):
        ret = True
        all_counter_lists = []
        servers = ConfigManage().get_config_server_nodes()
        servers = sorted(servers, key=lambda server: server.node_name)
        for i in range(0,len(servers)):
            check_server = servers[i]
            check_server_lists = vehicle_cache.get_truck_white_list(check_server.node_name)
            for j in range(i+1,len(servers)):
                server = servers[j]
                server_lists = vehicle_cache.get_truck_white_list(server.node_name)
                counter_lists = set(check_server_lists) & set(server_lists)
                if len(counter_lists) > 0 and (check_server.mode == ServerNode.SINGLE or \
                    (check_server.mode == ServerNode.CLUSTER_MASTER and server.mode != ServerNode.CLUSTER_BACK) or \
                    (check_server.mode == ServerNode.CLUSTER_SLAVE and server.mode != ServerNode.CLUSTER_BACK) or \
                    (check_server.mode == ServerNode.SINGLE_MASTER and server.mode != ServerNode.SINGLE_BACK)):
                    all_counter_lists.append((MessageToString(check_server, as_one_line=True),MessageToString(server, as_one_line=True),sorted(counter_lists)))
        if len(all_counter_lists) > 0:
            ret = False
            info = f"\nATTENTION:check_server:whilte lists conflict with below:"
            for counter in all_counter_lists:
                info = info + "\n" + f"{counter[0]}===={counter[1]} conflict whilte lists:\n" + f"{counter[2]}"
            logger.warning(f"{info}")
        return ret

    def start_tracer(self):
        #redis and logging
        if ConfigManage().get_config_trace_server_enable():
            if (end_point:=ConfigManage().get_config_trace_server_end_point()):
                from skywalking import agent, config
                from common.constant import ENVIRONMENT,DOCKER_NAME
                config.init(agent_collector_backend_services=end_point, agent_name='antenna_server_app', \
                  agent_instance_name=DOCKER_NAME,agent_protocol='grpc',agent_logging_level="INFO",\
                  agent_log_reporter_level='ERROR')
                config.instance_properties = {"env": ENVIRONMENT, "region": "china-east"}
                agent.start()
        return

    def start_init(self):
        if not ConfigManage().check_config_valid():
            logger.warning(f"server start fail to check config")
            return False
        if not self.register_server_node():
            logger.warning(f"server start fail to register server node")
            return False
        if not self.check_server_node_white_lists():
            logger.warning(f"server start fail to check server node whilte lists")
            return False
        #self.start_tracer()
        return True

    def start(self):
        pass

    def join(self):
        for worker in self._workers:
            worker.join()

if __name__ == "__main__":
    app = AntennaServerAppBase()
    app.start()
    logger.info(f'Antenna server pid:{os.getpid()}')
    app.join()
