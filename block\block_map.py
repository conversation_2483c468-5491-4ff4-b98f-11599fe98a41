import math, time, traceback, uuid
import google.protobuf.text_format as text_format
from google.protobuf.text_format import <PERSON><PERSON>oString
import numpy as np
from cache import rspu
from common.periodical_task import PeriodicalTask
from cache.map_cache import MapCache
from common.constant import YARD_BOX_LANE_WIDTH
from common.logger import logger
from common.singleton import Singleton
from common.yard_converter import YardConverter
from plc.gantry_receiver import GantryInfoContainer
from plc.crane_receiver import <PERSON>InfoContainer
from model import system_status
from proto.cache_pb2 import CraneMapAdjustmentConfig
from proto.system_status_cache_pb2 import SystemStatusDetail
from common.common_util import get_point_to_line_distance, is_in_width_ploygon
from proto.antenna_pb2 import Position, MapAdjustment

class BlockMap(metaclass=Singleton):

    def __init__(self):
        self._crane_map_adjustment_config = CraneMapAdjustmentConfig()
        with open('common/crane_map_adjustment.pb.txt') as (f):
            text_format.Merge(f.read(), self._crane_map_adjustment_config)
        '''
        self._gantry_map_adjustment_config = CraneMapAdjustmentConfig()
        with open('common/gantry_map_adjustment.pb.txt') as (f):
            text_format.Merge(f.read(), self._gantry_map_adjustment_config)
        self._yard_line_map_adjustment_config = CraneMapAdjustmentConfig()
        with open('common/yard_line_map_adjustment.pb.txt') as (f):
            text_format.Merge(f.read(), self._yard_line_map_adjustment_config)
        '''
        self._map_cache = MapCache()
        self._gantry_map_adjustment_config = self._map_cache.get_gantry_block_area()
        self._yard_line_map_adjustment_config = self._map_cache.get_yard_line_block_area()
        self._gantry_map_loop_thread = PeriodicalTask(target=(self.gantry_map_loop), interval=1, master_only=True)
        self._yard_box_lane_width = YARD_BOX_LANE_WIDTH
        self._roadside_map_loop_thread = PeriodicalTask(target=(self.roadside_map_loop), interval=1, master_only=True)

    def gantry_map_loop(self):
        start = time.time()
        gantry_adjustments = self.process_gantry_map_adjustment()
        self._map_cache.set_auto_map_adjustment(gantry_adjustments)
        end = time.time()
        logger.debug(f"gantry_map_loop loop cost:{end - start}")


    def roadside_map_loop(self):
        start = time.time()
        roadside_adjustments = self.process_roadside_map_adjustment()
        self._map_cache.set_road_side_map_adjustment(roadside_adjustments)
        end = time.time()
        logger.debug(f"roadside_map_loop loop cost: {end - start}")

    def start(self):
        self._gantry_map_loop_thread.start()
        self._roadside_map_loop_thread.start()

    def process_map_adjustment(self):
        adjustments = []
        config = self._crane_map_adjustment_config
        cranes = CraneInfoContainer().get_all_crane_info()
        for config_area in config.config_area:
            for crane in cranes:
                position = CraneInfoContainer().get_position(crane)
                if position is None:
                    continue
                offset = sorted([
                    (config_area.critical_area_from.utm_x - position[0]) * math.cos(config.approximate_point_theta) +
                    (config_area.critical_area_from.utm_y - position[1]) * math.sin(config.approximate_point_theta),
                    (config_area.critical_area_to.utm_x - position[0]) * math.cos(config.approximate_point_theta) +
                    (config_area.critical_area_to.utm_y - position[1]) * math.sin(config.approximate_point_theta)])
                logger.debug('crane {} at: {}, area: {}, offset: {} '.format(crane.crane_no, position, config_area.description, offset))
                if offset[0] < config.approximate_point_offset_to and offset[1] > config.approximate_point_offset_from:
                    logger.debug('Area {} Blocked by crane {}'.format(config_area.description, crane.crane_no))
                    tba = list(config_area.map_adjustments)
                    for t in tba:
                        t.uuid = str(uuid.uuid1())
                        t.adjustment_reason_description = 'Area {} Blocked by crane {}'.format(config_area.description, crane.crane_no)
                    adjustments = adjustments + tba
                    break
        return adjustments
    
    def process_gantry_map_adjustment(self):
        adjustments = []
        config = self._gantry_map_adjustment_config
        if config is None:
            # logger.warning('gantry block area is none')
            return adjustments

        gantries = GantryInfoContainer().get_all_gantry_info()
        for gantry in gantries:
            if gantry.block_nul == 0:
                logger.debug('gantry no {} invalid block nul {}'.format(gantry.gantry_no, gantry.block_nul))
                continue

            if not gantry.is_stable:
                logger.debug('gantry no {} ,block nul {} is not stable'.format(gantry.gantry_no, gantry.block_nul))
                continue

            position = GantryInfoContainer().get_position(gantry)
            if position is None:
                logger.debug('gantry no {} invalid position'.format(gantry.gantry_no))
                continue

            yard = YardConverter().to_yard(gantry.block_nul)
            if yard is None:
                logger.debug('gantry no {} block nul {} convert invalid yard'.format(gantry.gantry_no, gantry.block_nul))
                continue
            
            #logger.debug('gantry block map:gantry {}, block nul {}, position{} blocked yard?'.format(gantry.gantry_no, gantry.block_nul, position))
            for config_area in config.config_area:
                if yard == config_area.description:
                    is_in = is_in_width_ploygon(position[0], position[1], config_area.critical_area_from.utm_x, config_area.critical_area_from.utm_y,\
                      config_area.critical_area_to.utm_x, config_area.critical_area_to.utm_y, self._yard_box_lane_width)
                    logger.debug('gantry block map:gantry {} at: {}, area: {}, is_in: {} '.format(gantry.gantry_no,position,config_area.description,is_in))
                    if is_in:
                        #logger.debug('gantry block map:Area {} Blocked by gantry {}'.format(config_area.description, gantry.gantry_no))
                        tba = list(config_area.map_adjustments)
                        for t in tba:
                            t.uuid = str(uuid.uuid1())
                            t.adjustment_reason_description = 'Area {}04-{}01 Blocked by gantry {}'.format(config_area.description, config_area.description, gantry.gantry_no)
                        adjustments = adjustments + tba
        return adjustments


    def get_yard_line_map_adjustment(self, position: Position):
        adjustments = []
        config = self._yard_line_map_adjustment_config
        if config is None:
            logger.warning('yard line block area is none')
            return adjustments

        for config_area in config.config_area:
            logger.debug('gantry block map:is Position ({},{}) Blocked by {}06-{}00 yard?'.format(position.utm_x, position.utm_y, config_area.description, config_area.description))
            is_in = is_in_width_ploygon(position.utm_x, position.utm_y, config_area.critical_area_from.utm_x, config_area.critical_area_from.utm_y,\
              config_area.critical_area_to.utm_x, config_area.critical_area_to.utm_y, self._yard_box_lane_width)
            if is_in:
                logger.debug('gantry block map:Position ({},{}) Blocked by yard {}06-{}00'.format(position.utm_x, position.utm_y, config_area.description, config_area.description))
                tba = list(config_area.map_adjustments)
                for t in tba:
                    t.uuid = str(uuid.uuid1())
                    t.adjustment_reason_description = 'Position ({},{}) Blocked by yard {}06-{}00'.format(position.utm_x, position.utm_y, config_area.description, config_area.description)
                adjustments = adjustments + tba
                break
        return adjustments


    def process_roadside_map_adjustment(self):
        adjustments = []
        block_ids = []
        blocks = rspu.get_roadside_perception_blocks()
        for block in blocks:
            logger.debug(f'road side block:{MessageToString(block, as_one_line=True)},block_ids:{block_ids}')
            for info in block.blockinfos.blockinfos:
                block_id = info.section_id
                if block_id > 0 and block_id not in block_ids:
                    map_adjust = MapAdjustment()
                    #map_adjust.uuid = str(uuid.uuid1())
                    map_adjust.adjustment_type = MapAdjustment.BLOCK_POINT
                    map_adjust.adjustment_reason = MapAdjustment.ROADSIDE_BLOCK
                    map_adjust.block_point.point.utm_x = info.utm_x
                    map_adjust.block_point.point.utm_y = info.utm_y
                    map_adjust.block_point.unit_id = info.lane_id
                    map_adjust.adjustment_reason_description = 'Blocked by roadside rspu_name {} obstacle Position ({},{}) '.format(block.rspu.rspu_name,map_adjust.block_point.point.utm_x, map_adjust.block_point.point.utm_y)
                    logger.debug(f'{map_adjust.adjustment_reason_description}')
                    adjustments =  adjustments + [map_adjust]
                    block_ids = block_ids + [block_id]
        return adjustments
