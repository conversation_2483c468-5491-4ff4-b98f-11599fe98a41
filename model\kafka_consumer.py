import json
import time
import traceback
import datetime

from common.constant import KAFKA_GROUP_ID
from common.logger import logger
from common.periodical_task import PeriodicalTask
from model.kafka_server import KafkaMsgConsumer


class ConsumerEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime("datetime.datetime(%Y, %m, %d, %H, %M, %S)")
        else:
            return json.JSONEncoder.default(self, obj)


class KafkaConsumers(object):
    def __init__(self, server_list, topics=[], group_id=KAFKA_GROUP_ID, topics_offset_ms={}, interval_time=10,
                 receive_time_out=1000, sasl_config={}, master_only=True, topic_job_type_mapping=None):
        self._topic = None
        self._server = server_list
        self._topics = topics
        self._topics_offset_ms = topics_offset_ms
        self._interval_time = interval_time / 1000  # ms
        self._receive_time_out = receive_time_out  # ms
        self._master_only = master_only
        self._group_id = group_id
        self._sasl_config = sasl_config
        self._consumer = None
        self._handlers = {}
        self._logger = None
        self._topic_job_type_mapping = topic_job_type_mapping or {}

    def __dispatch_message(self, msg):
        logger.debug(
            f'msg topic:{msg.topic},partition:{msg.partition},key:{msg.key},len(value):{len(msg.value)},value[:1024]:{msg.value[:1024]}')
        msg_value = json.loads(msg.value)
        job_type = msg_value.get('MSG_JOB_TYPE', self._topic_job_type_mapping.get(msg.topic))
        if job_type in self._handlers:
            body_json = json.dumps(msg_value.get('MSG_BODY', msg_value), cls=ConsumerEncoder).encode('utf-8')
            # logger.info(f'''job_type:{job_type},msg_body:{body_json}''')
            self._handlers[job_type](body_json)

    def __handle_message(self):
        if self._consumer and self._consumer.connect():
            start = time.time()
            fetch_time = time.time()
            try:
                result = self._consumer.receive(self._receive_time_out)
                fetch_time = time.time()
                for _, msgs in result.items():
                    for msg in msgs:
                        try:
                            self.__dispatch_message(msg)
                        except Exception as e:
                            logger.warning(f"[handle_message] dispatch_message err:{e}, trace:{traceback.format_exc()}")
            except Exception as e:
                logger.warning(f"[handle_message] err:{e}, trace:{traceback.format_exc()}")
            interval_time = time.time() - start
            logger.debug(f"receive handle_message loop cost:{interval_time}, kafka cost: {fetch_time - start}")
            if interval_time > 1.1:
                logger.warning(
                    f"receive handle_message loop over cost execeed round time:{round(interval_time)}, execeed time:{interval_time}")
        else:
            logger.warning(f'vmsconsumer fail to connect server:{self._server}')

    def __register_handler(self, handler_type: str, handler):
        self._handlers[handler_type] = handler

    def __seek_by_time(self, topics_offset_ms: dict = {}):
        if self._consumer and self._consumer.connect():
            self._consumer.seek_by_time(topics_offset_ms)
        else:
            logger.warning(f'vmsconsumer fail to connect server:{self._server}')

    def __init_connect(self):
        self._consumer = KafkaMsgConsumer(self._server, topic=self._topic, topics=self._topics,
                                          group_id=self._group_id, sasl_config = self._sasl_config)  # TODO offset commit
        self.__seek_by_time(self._topics_offset_ms)

    def __start_task(self):
        self.__init_connect()
        self._consumer_thread = PeriodicalTask(target=self.__handle_message, interval=self._interval_time,
                                               master_only=self._master_only)
        self._consumer_thread.start()

    def __register_handlers(self, handlers: dict):
        for handler_type, handler in handlers.items():
            self.__register_handler(handler_type, handler)

    def start_work(self, handlers: dict = {}):
        self.__register_handlers(handlers)
        self.__start_task()

    def start_consumer(self):
        self.start_work()


if __name__ == '__main__':
    pass
