# coding:utf-8

"""
Products:
    all chassis info will inserted by grpc interface -> "PushChassisInfo", every seconds.
Customer:
    coroutine for every vehiclename will insert data into  mysql every minute
"""
import os
import datetime
import time
import asyncio
import traceback
from asyncio import tasks
from threading import Thread
from cache import vehicle_cache
from cache.chassis_cache import ChassisCache
from common.constant import CHASSIS_CACHE_INTERVAL_TIME, MSGSM_WECHAT_URL, CHASSIS_DATA_DIR
from common.logger import logger
from common.master import Master
from model import wechat_message
from model.chassis_detail import insert_chassis_detail
from config.config_manage import ConfigManage

wechat_=wechat_message.WeChatMessage(MSGSM_WECHAT_URL)

async def insert_chassis_from_redis_into_mysql(vehicle_name):
    logger.debug(f"insert_chassis_from_redis_into_mysql.....vehicle_name:{vehicle_name}")
    try:
        chassis_cache_list = ChassisCache().query_chassis_info_by_vehicle_name(vehicle_name)
        chassis_len = len(chassis_cache_list)
        chassis_cache_len = 0
        if chassis_len > 0:
            #b"@@@".join(chassis_cahce_list) by mysql
            chassis_cache = b"".join(chassis_cache_list)
            chassis_cache_len = len(chassis_cache)
            insert_chassis_detail(vehicle_name, chassis_cache)
            ChassisCache().delete_chassis_info(vehicle_name, chassis_len)
        logger.info(f"【vehicle_name】->{vehicle_name}  Insert chassis info Success,len:{chassis_len},"
                    f"chassis_cache_len:{chassis_cache_len},average:{chassis_cache_len/(chassis_len if chassis_len > 0 else 1)}.")
    except Exception as e:
        logger.error(
            f"【vehicle_name】->{vehicle_name} Insert chassis info failed. chassis_info[vehicle_name].\nTraceback info:{traceback.format_exc()}")
        ChassisCache().limit_chassis_length(vehicle_name)
        alarm_content = f"【vehicle_name】->{vehicle_name} processing can info failed."
        wechat_.send_message(alarm_content)
        return False
    return True


async def establish_task(master, vehicle_name, identify):
    while True:
        try:
            await insert_chassis_from_redis_into_mysql(vehicle_name)
            res = await asyncio.gather(calculate_vehicle_list())
            online_vehicle_list = res [0]
            if not (is_master:=master.is_master()) or vehicle_name not in online_vehicle_list:
                logger.info(f"terminate  coroutine -> {vehicle_name}. online_vehicle_list - > {online_vehicle_list}, master:{is_master}")
                insert_res=await insert_chassis_from_redis_into_mysql(vehicle_name)
                logger.info(f"clear chassis info before offline or not master.vehicle_name:{vehicle_name}.insert result:{insert_res}")
                if not insert_res:
                    time.sleep(1)
                    # the data must has been inserted before current vehicle offline
                    logger.warning(f"vehicle_name:{vehicle_name} try to insert remaining chassis info again if failed")
                    continue
                logger.debug(f"establish_task:before release_lock vehicle_name:{vehicle_name},identify:{identify}")
                ChassisCache().release_lock(vehicle_name, identify)
                logger.debug(f"establish_task:after release_lock vehicle_name:{vehicle_name},identify:{identify}")
                break
        except Exception as e:
            logger.error(f"establish_task:vehicle_name{vehicle_name},identify:{identify},e:{e},trace:{traceback.format_exc()}")
        # insert data nexttime  if inserting failed
        await asyncio.sleep(CHASSIS_CACHE_INTERVAL_TIME)


async def calculate_vehicle_list():
    await asyncio.sleep(1)
    online_vehicle_list = []
    try:
        online_vehicle_list = sorted(vehicle_cache.get_online_trucks())
    except Exception as e:
        logger.warning("Get online trucks failed")
    if not online_vehicle_list:
        logger.debug("all vehicle offline")
    return online_vehicle_list


async def append_task(loop,master):
    last_vehicle_list = []
    while True:
        if not master.is_master():
            await asyncio.sleep(1)
            continue
        res=await asyncio.gather(calculate_vehicle_list())
        online_vehicle_list=res[0]
        new_online_list = sorted(list(set(online_vehicle_list) - set(last_vehicle_list)))
        offline_list = sorted(list(set(last_vehicle_list) - set(online_vehicle_list)))
        logger.debug(f"append_task:online_vehicle_list:{online_vehicle_list},new_online_list:{new_online_list},offline_list:{offline_list}")
        for vehicle_name in online_vehicle_list:
            try:
                logger.debug(f"append_task:before acquire_lock vehicle_name:{vehicle_name}")
                identify = ChassisCache().acquire_lock(vehicle_name)
                if identify:
                    logger.debug(f"append_task:after acquire_lock establish_task vehicle_name:{vehicle_name},identify:{identify}")
                    new_task = establish_task(master, vehicle_name, identify)
                    asyncio.run_coroutine_threadsafe(new_task, loop)
                else:
                    logger.debug(f"append_task:after acquire_lock may have establish_task vehicle_name:{vehicle_name},identify:{identify}")
            except Exception as e:
                logger.warning(f"append_task:vehicle_name{vehicle_name},e:{e},trace:{traceback.format_exc()}")
        last_vehicle_list = online_vehicle_list


async def log_task_num(loop,master):
    while True:
        logger.debug(f"tasks length is {len(tasks.all_tasks(loop))},master:{master.is_master()}")
        await asyncio.sleep(1)


def start_loop(loop):
    asyncio.set_event_loop(loop)
    loop.run_forever()


def chassis_coroutine_manage_process():
    if not os.path.exists(CHASSIS_DATA_DIR):
        os.makedirs(CHASSIS_DATA_DIR)
        print(f"[chassis_coroutine_manage_process] create chassis_data dir:{CHASSIS_DATA_DIR}")
    if ConfigManage().is_master_server_node():
        ChassisCache().release_all_lock()
    else:
        print(f"[chassis_coroutine_manage_process] no need to release lock for not master server node")
    master = Master()
    thread_loop = asyncio.new_event_loop()
    run_loop_thread = Thread(target=start_loop, args=(thread_loop,))
    run_loop_thread.start()
    advocate_loop = asyncio.get_event_loop()
    advocate_loop.run_until_complete(
        asyncio.wait([append_task(thread_loop,master), calculate_vehicle_list(), log_task_num(thread_loop,master)]))


if __name__ == '__main__':
    # chassis_process()
    import multiprocessing
    p = multiprocessing.Process(target=chassis_coroutine_manage_process)
    p.start()
    p.join()
