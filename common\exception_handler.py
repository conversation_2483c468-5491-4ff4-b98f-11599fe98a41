import functools
import traceback
import sys

from common.logger import logger
from model.wechat_message import WeChatMessage

def http_expection_catch(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            inner_ret = func(*args, **kwargs)
        except Exception as e:
            expection_function_name = str(wrapper.__name__)
            logger.info(f"http failed: {expection_function_name}\n{traceback.format_exc()}")
            error_string = f"http failed: {expection_function_name}\n" + str(e)[0:200]
            WeChatMessage().send_message_to_sim(error_string)
            inner_ret = None
        return inner_ret
    return wrapper

def rpc_expection_catch(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            inner_ret = func(*args, **kwargs)
        except Exception as e:
            expection_function_name = str(wrapper.__name__)
            logger.info(f"rpc failed: {expection_function_name}\n{traceback.format_exc()}")
            error_string = f"rpc failed: {expection_function_name}\n" + str(e)[0:200]
            WeChatMessage().send_message_to_sim(error_string)
            inner_ret = None
        return inner_ret
    return wrapper
