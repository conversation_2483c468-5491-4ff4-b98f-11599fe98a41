# =======================
# FMS 订阅（上行，从站控发来）
# =======================

# 上行-密钥请求
MQTT_ENCRYPT_KEY_REQ_TOPIC = "HCMS/+/S2M/encryptKeyReq"

# 上行-心跳
MQTT_KEEPALIVE_UP_TOPIC = "HCMS/+/S2M/keepalive"

# 上行-业务请求（如换电完成、定位结果等）
MQTT_STATION_REQUEST_TOPIC = "HCMS/+/S2M/request"

# 上行-业务回复（如定位回复、换电完成回复等）
MQTT_STATION_RESPONSE_TOPIC = "HCMS/+/S2M/response"

# =======================
# FMS 发布（下行，发给站控，使用固定站点编号 155047）
# =======================

# 下行-密钥请求回复
MQTT_ENCRYPT_KEY_RESP_TOPIC = "HCMS/155047/M2S/encryptKeyResp"

# 下行-心跳回复
MQTT_KEEPALIVE_DOWN_TOPIC = "HCMS/155047/M2S/keepalive"

# 下行-业务请求（如启动换电、定位检测等）
MQTT_FMS_REQUEST_TOPIC = "HCMS/155047/M2S/request"

# 下行-业务回复（如定位回复、换电完成回复等）
MQTT_FMS_RESPONSE_TOPIC = "HCMS/155047/M2S/response"

# 上行-定位检测结果-请求
FUNCTION_LOCATION_RESULT_REQ = "locationResultReq"

# 上行-自动换电完成-请求
FUNCTION_SWAP_FINISH_REQ = "swapFinishReq"

# 上行-定位检测车辆位置-回复
FUNCTION_LOCATION_DETECTION_RESP = "locationDetectionResp"

# 上行-调度启动换电-回复
FUNCTION_DISPATCH_SWAP_START_RESP = "dispatchSwapStartResp"

# 下行-定位检测车辆位置-请求
FUNCTION_LOCATION_DETECTION_REQ = "locationDetectionReq"

# 下行-定位检测结果-回复
FUNCTION_LOCATION_RESULT_RESP = "locationResultResp"

# 下行-调度启动换电-请求
FUNCTION_DISPATCH_SWAP_START_REQ = "dispatchSwapStartReq"

# 下行-自动换电完成-回复
FUNCTION_SWAP_FINISH_RESP = "swapFinishResp"


if __name__ == "__main__":
    pass
