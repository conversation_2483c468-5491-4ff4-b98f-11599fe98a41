import traceback
from typing import Dict
import datetime
#from dataclasses_json import dataclass_json
import sqlalchemy
from sqlalchemy import Column, Integer, String, DateTime,and_,or_
from sqlalchemy.sql import func

from common.logger import logger
from meishanalia.communication.db import TosSession
from meishanalia.common.constant import TABLE_CMD_INFO
from meishanalia.model.table_info import TosWisInfo, DcWiTnfo


def query_by_id(id):
    session = TosSession().acquire()
    try:
        info = session.query(DcWiTnfo).filter(DcWiTnfo.ID == id).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_id err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def update(id, update_items:dict):
    session = TosSession().acquire()
    try:
        session.query(DcWiTnfo).\
                filter_by(ID = id).\
                update(update_items)
        session.commit()
    except Exception as e:
        logger.warning(f"Update DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
    finally:
        session.close()
    return

def insert(record: DcWiTnfo):
    session = TosSession().acquire()
    try:
        #  2022.03.09
        # cur = session.query(DcWiTnfo).filter(DcWiTnfo.TRUCK_NO == record.TRUCK_NO,DcWiTnfo.WI_NO == record.wi_no,DcWiTnfo.WI_ACT == record.WI_ACT).first()
        cur = session.query(DcWiTnfo).filter(DcWiTnfo.TRUCK_NO == record.TRUCK_NO,DcWiTnfo.WI_ID == record.wi_id,DcWiTnfo.WI_ACT == record.WI_ACT).first()
        if cur is None:
            record.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            record.VERSION = 1
            record.RESET_VERSION = 0
            logger.info(f"Insert DcWiTnfo table to_dict:{record.to_dict()}")
            session.add(record)  #
        else:
            record.VERSION = cur.VERSION + 1
            record.UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            logger.info(f"Update DcWiTnfo table origin:{cur.__dict__}")
            logger.info(f"Update DcWiTnfo table to_dict():{record.to_dict()}")
            for key in record.__dict__:
                if key == '_sa_instance_state':
                    continue
                setattr(cur, key, getattr(record, key))
        session.commit()
    except Exception as e:
        logger.warning(f"Insert DcWiTnfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def copy_from_wis(wis:TosWisInfo, truck_status):
    wi = DcWiTnfo()
    for key in wis.__dict__:
        if key == '_sa_instance_state':
            continue
        setattr(wi, key, getattr(wis, key))
    wi.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
    wi.UPDATE_TIME = wi.INSERT_TIME
    wi.CTRL_INSERT_TIME = wi.INSERT_TIME
    wi.CTRL_UPDATE_TIME = wi.INSERT_TIME
    # wi.TRUCK_STATUS = 'START'
    wi.TRUCK_STATUS = truck_status
    return wi

def insert_by_wi_id(id, truck_status=""):
    ret = False
    session = TosSession().acquire()
    try:
        wis = session.query(TosWisInfo).filter_by(ID=id).first()
        if wis is not None:
            cur = copy_from_wis(wis, truck_status)
            session.merge(cur)
            session.commit()
            ret = True
        else:
            logger.warning(f"Insert DcWiTnfo fail to get TosWisInfo by id:{id}")
    except Exception as e:
        logger.warning(f"Insert DcWiTnfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()
    return ret

def update_by_id(id, update_column:dict,UPDATE_TIME=False):
    session = TosSession().acquire()
    try:
        if UPDATE_TIME:
            update_column['UPDATE_TIME'] = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
        logger.info(f'[update_by_id] update_column:{update_column}')
        session.query(DcWiTnfo).filter(DcWiTnfo.ID == id).update(update_column)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert DcWiTnfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def update_arrive_area_by_id(id, arrive_area):
    session = TosSession().acquire()
    try:
        update_column = {}
        update_column['ARRIVE_AREA'] = arrive_area
        update_column['UPDATE_TIME'] = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
        logger.info(f'[update_arrive_area_by_id] update_column:{update_column}')
        session.query(DcWiTnfo).filter(DcWiTnfo.ID == id).update(update_column)
        session.commit()
        return True
    except Exception as e:
        logger.warning(f"update_arrive_area_by_id err{e}, trace:{traceback.format_exc()}")
        return False
    finally:
        session.close()

def update_vesion(id, version):
    update_column = dict()
    update_column['VERSION'] = version
    update_by_id(id, update_column)


def update_wi_status(id, truck_status:str):
    update_column = dict()
    time_filed = ''
    if truck_status in ['START', 'ARRIVE', 'FINISH']:
        time_filed = truck_status + '_TIME'
        update_column[time_filed] = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
    update_column['TRUCK_STATUS'] = truck_status
    update_by_id(id, update_column, True)


def query_all():
      session = TosSession().acquire()
      try:
          curs = session.query(DcWiTnfo).\
                          order_by(sqlalchemy.asc(DcWiTnfo.ID)).all()
          results = list()
          for cur in curs:
              results.append(cur.to_dict())
          return results
      except Exception as e:
          logger.warning(f"query all DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
          return []
      finally:
          session.close()

def query_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        curs = session.query(DcWiTnfo).\
                        filter_by(TRUCK_NO = vehicle_name).\
                        order_by(sqlalchemy.asc(DcWiTnfo.ID)).all()
        results = list()
        for cur in curs:
            results.append(cur.to_dict())
        return results
    except Exception as e:
        logger.warning(f"query all DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()

def query_by_wi_ctn(vehicle_name, wi_id, wi_act, ctn_no):
    session = TosSession().acquire()
    try:
        # 2022.03.09
        # info = session.query(DcWiTnfo).filter_b(TRUCK_NO = vehicle_name,WI_NO = wi_no,WI_ACT = wi_act, CTN_NO = ctn_no).first()
        info = session.query(DcWiTnfo).filter_b(TRUCK_NO = vehicle_name,WI_ID = wi_id,WI_ACT = wi_act, CTN_NO = ctn_no).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_wi_ctn err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def delete_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        session.query(DcWiTnfo).filter_by(TRUCK_NO = vehicle_name).delete()
        session.commit()
        return True
    except Exception as e:
        logger.warning(f"Delete DcWiTnfo by name Error: {e},trace:{traceback.format_exc()}")
        return False
    finally:
        session.close()

def delete_all():
    session = TosSession().acquire()
    try:
        session.query(DcWiTnfo).delete()
        session.commit()
    except Exception as e:
        logger.warning(f"Delete DcWiTnfo all Err: {e},trace:{traceback.format_exc()}")
    finally:
        session.close()
    return

def delete_by_id(id):
    session = TosSession().acquire()
    try:
        session.query(DcWiTnfo).filter_by(ID=id).delete()
        session.commit()
        return True
    except Exception as e:
        logger.warning(f"Delete DcWiTnfo by id Error: {e},trace:{traceback.format_exc()}")
        return False
    finally:
        session.close()

def count_id(limit_num = 10):
    ret = -1
    session = TosSession().acquire()
    try:
        if limit_num > 0:
            ret = session.query(func.count(DcWiTnfo.ID)).limit(limit_num).scalar()
        else:
            ret = session.query(func.count(DcWiTnfo.ID)).scalar()
    except Exception as e:
        logger.warning(f"count DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
        ret = -1
    finally:
        session.close()
    return ret


if __name__ == '__main__':
    '''
    wi_record = DcWiTnfo()
    update_by_id(7)
    info = get_by_id(7)
    if info:
        print(f'type(info):{type(info)}')
        test = info.to_dict() #if not Base.to_dict = to_dict will error
        print(f'info get test:{test}')
        #print(f'info get test.dict:{test.__dict__}')
    else:
        print('no info id')
    '''
    insert_by_wi_id(13)
