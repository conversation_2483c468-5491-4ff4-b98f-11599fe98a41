import datetime
import json
import time
import traceback

from meishanalia.model import crane_info
from meishanalia.model.table_info import CraneInfo
from meishanalia.vms_message.vms_control import vms_control_handler
from meishanalia.vms_message.vms_wi import vms_wi_handler


#
def insert_tos_command(container_size, container_position, command_type, command_action, destination, priority,
                       lane=None, truck_no='AT800', wi=125, ctn='CTN', twin_wi=0, twin_ctn='None', orientation='R',
                       lock_flag='', lock_pavilion='', lock_node='', queue_node='', twin_flag="", cur_wi="", cur_wis="",
                       device_disable=0, vessel_pos='None', working_bay='None', cycle_mode = 'S', vessel_ref='None', vessel_class='None'):
    # if twin_wi == 0:
    #     twin_flag = 'N'
    # else:
    #     twin_flag = 'T'

    print(
        f"insert_tos_command:{truck_no} {container_position} {command_type} {command_action} {destination} {priority} "
        f"{orientation} {lock_flag} {lock_pavilion} {lock_node} {queue_node}")
    # assert(truck_no.startswith('AT8')), 'vehicle name invalid, make sure that vehicle name starts with AT8,for example AT801'
    if command_type not in {'LOAD', 'DSCH', 'YARD'}:
        print(f"command type:{command_type} invalid")
        return False
    if command_type not in {'LOAD', 'UNLOAD'}:
        print(f"command action invalid:{command_action} invalid")
        return False

    tos_dict = {"TRUCK_NO": "AT501", "WI_NO": 538894, "CTN_NO": "OOCU8103979", "TO_POS": "CR44", \
                "WI_TYPE": "DSCH", "WI_ACT": "LOAD", "WI_STATUS": "DISPATCH", "TWIN_FLAG": "N",
                "DISPATCH_TIME": 1620817260, \
                "TWIN_WI_NO": 0, "TWIN_CTN_NO": None, "TRUCK_POS": "M", "EQUIT_TYPE": "45GP", "TEU": 2, \
                "TRUCK_SEQ": 0, "VERSION": 0, "RESET_VERSION": 0, "LOCK_FLAG": "", "LOCK_PAVILION": "",
                "POW_NAME": "6YARD", \
                "CTRL_STOP_NODE": "", "CTRL_ACTION": "UNLOAD", "CTRL_CUR_WIS": "", "CTRL_WAIT_NODE": "",
                "CTRL_LOCK_NODE": "YQ08U004", "CTRL_QUEUE_NODE": ""}

    control_dict = {"TRUCK_NO": "AT501", "SPEED": 0,
                    "STOP_NODE": "", "ACTION": "UNLOAD", "CUR_WIS": "", "WAIT_NODE": "", "LOCK_NODE": "YQ08U004",
                    "QUEUE_NODE": ""}
    ret = True
    try:
        tos_dict['TRUCK_NO'] = truck_no
        tos_dict['WI_NO'] = wi
        tos_dict['WI_ID'] = wi
        tos_dict['CTN_NO'] = ctn
        tos_dict['TO_POS'] = destination
        tos_dict['WI_TYPE'] = command_type
        tos_dict['WI_ACT'] = command_action
        tos_dict['EQUIT_TYPE'] = container_size
        tos_dict['TRUCK_POS'] = container_position
        tos_dict['DISPATCH_TIME'] = time.time()
        tos_dict['INSERT_TIME'] = tos_dict['DISPATCH_TIME']
        tos_dict['UPDATE_TIME'] = tos_dict['INSERT_TIME']
        tos_dict['TWIN_FLAG'] = twin_flag
        tos_dict['TWIN_WI_NO'] = twin_wi
        tos_dict['TWIN_CTN_NO'] = twin_ctn
        tos_dict['TRUCK_SEQ'] = priority
        tos_dict['LOCK_FLAG'] = lock_flag
        tos_dict['LOCK_PAVILION'] = lock_pavilion
        if destination.startswith('CR'):
            now = datetime.datetime.now()
            vessel_pos = '%02d' % now.hour + '%02d' % now.minute + '%02d' % now.second
            tos_dict['VESSEL_POS'] = vessel_pos
        tos_json = json.dumps(tos_dict).encode('utf-8')
        vms_wi_handler(tos_json)

        '''
        tos = TosWiInfo()
        tos.from_dict(tos_dict)
        tos_wi_info.insert(tos)
        '''

        control_dict['TRUCK_NO'] = truck_no
        control_dict['ACTION'] = tos_dict['WI_ACT']
        control_dict['LOCK_NODE'] = lock_node
        # if and ctn == "BEFORE" and twin_flag == 'T':
        #     control_dict['CUR_WIS'] = str(tos_dict['WI_NO']) + ',' + str(tos_dict['TWIN_WI_NO'])
        # else:
        #     control_dict['CUR_WIS'] = str(tos_dict['WI_NO'])
        if cur_wis:
            control_dict['CUR_WIS'] = str(cur_wis)
        else:
            control_dict['CUR_WIS'] = str(tos_dict['WI_NO'])
        control_dict['CUR_IDS'] = control_dict['CUR_WIS']
        if destination.startswith('CR'):
            if queue_node and priority == "100":
                control_dict['QUEUE_NODE'] = queue_node
            control_dict['WAIT_NODE'] = 'YQ07D002'
        elif destination.startswith('FLD'):
            control_dict['WAIT_NODE'] = 'YQ07D002'
        else:
            control_dict['WAIT_NODE'] = 'VR080013'

        if not destination.startswith('CR'):
            destination_list = list(destination)
            destination_list.insert(2, '0')
            destination_list.insert(0, 'BK0')
            destination = ''.join(destination_list)
            # 66->'BK0660'
            if len(destination) == 6:
                destination = destination + '06'
        control_dict['STOP_NODE'] = destination
        # if
        '''
        if control_dict.get('QUEUE_NODE') is not None and len(control_dict.get('QUEUE_NODE')) > 0:
            control_dict['STOP_NODE'] = control_dict.get('QUEUE_NODE') + ',' + control_dict['STOP_NODE']
        '''
        control_json = json.dumps(control_dict).encode('utf-8')
        vms_control_handler(control_json)
        '''
        control = ControlInfo()
        control.from_dict(control_dict)
        control_info.insert(control)
        '''

        if not lane is None and destination.startswith('CR'):
            crane_dict = dict()
            crane_dict['CRANE_ID'] = destination
            crane_dict['LANE_NO'] = lane
            crane_dict['VESSEL_DIRECTION'] = orientation
            crane = CraneInfo()
            crane.from_dict(crane_dict)
            crane_info.insert(crane, True, True)
    except Exception as e:
        print(f"Insert TOS command err:{e}, trace:{traceback.format_exc()}")
        ret = False
    return ret


from common.diagnose_base import create_insert_tos_command_handler
insert_tos_command_dict = create_insert_tos_command_handler(insert_tos_command)