import socket
import struct
import time,datetime
import traceback

from common.logger import logger
from common.common_util import calc_crc
from proto.cache_pb2 import CraneStoreInfo
from plc.model import STORE_CRANE_MSGS_12150,STORE_CRANE_MSGS_11100, STORE_CRANE_LIST_12150, STORE_CRANE_LIST_11100


class CraneStorer(object):
    def __init__(self, crane_no, ip = '', port = 0,interval = 0.9):
        self._crane_no = crane_no
        self._msg_no = 0
        self._msg_dict = dict()
        self._msg1_crane = STORE_CRANE_LIST_12150
        self._msg2_crane = STORE_CRANE_LIST_11100
        self._interval = interval
        if crane_no in self._msg1_crane:
            self._msg_dict[crane_no] = STORE_CRANE_MSGS_12150
        elif crane_no in self._msg2_crane:
            self._msg_dict[crane_no] = STORE_CRANE_MSGS_11100
        else:
            logger.warning('crane_no:' + str(crane_no) + ' not in msg dict')
        self._ip = ip
        self._port = port
        self._sock = None
        if len(self._ip) > 0:
            self._sock = socket.socket(socket.AF_INET,  # Internet
                                      socket.SOCK_DGRAM)  # UDP
            self._sock.settimeout(0.1)

    @property
    def interval(self):
        return self._interval

    @interval.setter
    def interval(self, interval):
        self._interval = interval

    def crane_no(self):
        return self._crane_no

    def increase_msg_no(self):
        self._msg_no = self._msg_no + 1
        return self._msg_no

    def get_msg(self):
        if self._crane_no in self._msg_dict:
            return self._msg_dict[self._crane_no]
        else:
            logger.warning('crane_no:' + str(self._crane_no) + ' not in msg dict.use default')
            return None
    
    def fetch_one(self, arr, addr, size, signed):
        result = 0
        for i in range(addr, addr + size):
            result = result + arr[i] * (2 ** (16 * (i - addr)))
            if size == 2:
                logger.debug(f"result:{result},i:{i},arr:{arr[i]}")
        if signed:
            if result >= (2 ** (16 * size - 1)):
                result = result - (2 ** (16 * size))
        return result

    def fetch(self):
        msgs = self.get_msg()
        start = min(msgs[k][0] for k in msgs)
        end = max(msgs[k][0] + msgs[k][1] for k in msgs)
        bin = struct.pack('!BBHH', 1, 3, start, end - start)
        self._sock.sendto(bin, (self._ip, self._port))
        received = self._sock.recvfrom(1024)[0]
        if received[2] != (end - start) * 2:
            raise Exception(f"Crane fetch size error,{received[2]}:{(end - start) * 2}, {self._crane_no}")
        rep_str = '!BBB' + 'H' * (end - start) + 'BB'
        arr = struct.unpack(rep_str, received)[3:]
        result = CraneStoreInfo()
        for k in msgs:
            v = msgs[k]
            setattr(result, k, self.fetch_one(arr, v[0] - start, v[1], v[2]))
        return result


    #突然下线
    def store(self, info):
        msgs = self.get_msg()
        start = min(msgs[k][0] for k in msgs)
        end = max(msgs[k][0] + msgs[k][1] for k in msgs)
        bin = struct.pack('!BBHHB11HIH', 0x1, 0x10, start, (end - start), (end - start)*2,\
              info.header, info.crane_no,info.msg_no,info.year,info.month,info.day,info.hour,info.min,\
              info.sec,info.msec,info.truck_no,info.wi_id // 65536 + (info.wi_id % 65536)*65536,info.via_status)
        '''
        bin_hs=''.join(['%02X' %x  for x in bin])
        bin_crc = int(calc_crc(bin_hs),16)
        logger.info(f"bin:{bin},bin_crc:{bin_crc}")
        bin = struct.pack('!BBHHB11HIHH', 0x1, 0x10, start, (end - start), (end - start)*2,\
        info.header, info.crane_no,info.msg_no,info.year,info.month,info.day,info.hour,info.min,\
        info.sec,info.msec,info.truck_no,info.wi_id // 65536 + (info.wi_id % 65536)*65536,info.via_status,bin_crc)
        logger.info(f"cr bin:{bin}")
        '''
        self._sock.sendto(bin, (self._ip, self._port))
        received, addr = self._sock.recvfrom(1024)
        '''
        received_hs=''.join(['%02X' %x  for x in received])
        received_crc = calc_crc(received_hs[:-4])
        logger.info(f"received_crc:{received_crc}-{int(received_crc,16)}")
        '''
        arr = struct.unpack("!BBHHH",received)
        #logger.info(f"arr:{arr},addr:{addr},received:{received}")
        if arr[3] != (end - start):
            logger.warning(f"Crane:{self._crane_no},addr:{addr} store size error,{arr[3]}:{(end - start)}.received:{received},end:{end},start:{start}")
            return False
        return True


    def store_single(self, key, value):
        msgs = self.get_msg()
        msg_info = msgs.get(key)
        if msg_info is None:
            logger.warning(f"Crane:{self._crane_no} fail to get key:{key} mesg")
            return False
        start = msg_info[0]
        bin = struct.pack('!BBHH', 0x1, 6, start, value)
        '''
        bin_hs=''.join(['%02X' %x  for x in bin])
        bin_crc = int(calc_crc(bin_hs),16)
        logger.info(f"bin:{bin},bin_crc:{bin_crc}")
        bin = struct.pack('!BBHHH', 0x1, 6, start,value,bin_crc)
        logger.info(f"cr bin:{bin}")
        '''
        self._sock.sendto(bin, (self._ip, self._port))
        received, addr = self._sock.recvfrom(1024)
        '''
        received_hs=''.join(['%02X' %x  for x in received])
        received_crc = calc_crc(received_hs[:-4])
        logger.info(f"received_crc:{received_crc}-{int(received_crc,16)}")
        '''
        arr = struct.unpack("!BBHHH",received)
        #logger.info(f"arr:{arr},addr:{addr},received:{received},start:{start}")
        if arr[3] != value:
            logger.warning(f"Crane:{self._crane_no},addr:{addr} store size error,received:{received},start:{start},value:{arr[3]}!={value}.")
            return False
        return True


if __name__ == '__main__':
    from google.protobuf.text_format import MessageToString
    a = '01102B5C000E'
    #a= '01 10 2B 5C 00 0E 00 1C 00 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'
    ret = calc_crc(a)
    print(f'#################{ret}')
    info = CraneStoreInfo()
    info.header = 0
    info.crane_no  = 1234
    info.msg_no  = 0
    info.truck_no = 500 #AT500
    info.via_status = 1
    crane_no = 51
    ip = '0.0.0.0'
    '''
    crane_no = 52
    ip = '*************'
    port = 10060
    '''

    '''
    crane_no = 39
    ip = '*************'
    port = 10080
    '''
    value = 1
    test = struct.pack('!BBHH',1,16,11100,14)
    hs=''.join(['%02X' %x  for x in test])
    ret = calc_crc(hs)
    print(f"hs:{hs},ret:{ret}")

    while True:
        print(f"#################################")
        try:
            info.msg_no = info.msg_no + 1
            info.crane_no = crane_no
            localtime = datetime.datetime.now()
            info.year = localtime.year
            info.month = localtime.month
            info.day = localtime.day
            info.hour = localtime.hour
            info.min = localtime.minute
            info.sec = localtime.second
            info.msec = int(localtime.microsecond / 1000)
            #info.wi_id = int(time.time() % 1e3)
            info.wi_id = 0
            print(f'send info:{MessageToString(info, as_one_line=True)}')
            CraneStorer(crane_no, ip, port).store(info)
            CraneStorer(crane_no, ip, port).store_single('via_status',value)
            #ret = CraneStorer(crane_no, ip, port).fetch()
            #print(f'receive info:{MessageToString(ret, as_one_line=True)}')
        except Exception as e:
            print(f"server error: {e}, trace:{traceback.format_exc()}")
        time.sleep(1)
