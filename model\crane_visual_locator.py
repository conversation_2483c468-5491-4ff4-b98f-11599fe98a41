import math
import numpy as np
from common.constant import CRANE_LANE_THETA
from common.logger import logger
from common.singleton import Singleton


class CraneVisualLocator(metaclass=Singleton):
    def __init__(self):
        self._crane_bases = {
            '31_left': [403221.33438555995, 3293178.094170208, 1885],
            '31_right': [403242.0828638748, 3293197.2588517345, 1855],
            '32_left': [403219.7565330949, 3293176.8616214064, 2015],
            '32_right': [403240.6933117609, 3293196.2002297244, 1985],
            '33_left': [403219.28963567014, 3293176.345456666, 2060],
            '33_right': [403241.6047109209, 3293196.957151843, 2025],
            '34_left': [403221.71873317467, 3293178.5963300476, 2085],
            '34_right': [403242.84570365393, 3293198.110612238, 2055],
            '35_left': [403219.1410754591, 3293176.474469875, 2115],
            '35_right': [403241.8386118967, 3293197.439242471, 2080],
            '36_left': [403220.47332150437, 3293177.3006890854, 2160],
            '36_right': [403242.0612282135, 3293197.2915921058, 2155],
            '37_left': [403218.98569264094, 3293176.177813227, 2205],
            '37_right': [403239.7816558589, 3293195.3863549842, 2170],
            '38_left': [403220.2507838658, 3293177.4815044943, 2260],
            '38_right': [403240.2346728478, 3293195.9399602027, 2230],
            '39_left': [403220.625255649, 3293178.0837025223, 2550],  # 模糊
            '39_right': [403241.92628165, 3293197.7587540275, 2515],
            '40_left': [403218.37766162056, 3293176.004265474, 2520],
            '40_right': [403239.86267494207, 3293195.863347031, 2515],
            '41_left': [403219.01165717555, 3293176.4992200593, 2605],
            '41_right': [403240.25033118436, 3293196.116679096, 2575],
            '42_left': [403219.1332150248, 3293176.188360488, 2670],
            '42_right': [403243.87086146313, 3293199.0377043537, 2630],
            '43_left': [403217.2923718465, 3293174.940685184, 2745],
            '43_right': [403240.56316403684, 3293196.435144413, 2705],
            '44_left': [403218.4571526475, 3293176.047815871, 2665],
            '44_right': [403240.3034121195, 3293196.226481457, 2635],
            '45_left': [403219.9814712013, 3293176.52032215, 2710],
            '45_right': [403241.0412952888, 3293196.3840823527, 2720],  # 水气
            '46_left': [403219.25594648777, 3293176.2616779343, 2750],
            '46_right': [403241.0412952888, 3293196.3840823527, 2720],
            '47_left': [403218.5982634469, 3293176.5621415987, 3270],
            '47_right': [403238.51183817076, 3293194.9531975864, 3235],
            '48_left': [403217.83344092814, 3293176.315019961, 3310],
            '48_right': [403237.99710047385, 3293194.9714940, 3280],
            '49_left': [403217.49472668703, 3293175.712508, 3475],
            '49_right': [403238.7015521672, 3293195.33672578, 3430],
            '50_left': [403217.84226457507, 3293175.9947192473, 3550],
            '50_right': [403238.6334477281, 3293195.1988458172, 3515],
            '51_left': [403218.692745739, 3293176.6789020826, 3220],
            '51_right': [403239.03449970577, 3293195.4679058134, 3190],
            '52_left': [403218.11616624123, 3293176.0461893254, 3405],
            '52_right': [403239.0151523492, 3293195.350063276, 3355],
            '53_left': [403218.5282084251, 3293176.307007468, 3360],
            '53_right': [403239.78809283173, 3293195.9440578464, 3330],
            '54_left': [403217.6203261937, 3293175.737936694, 3565],
            '54_right': [403239.036644808, 3293195.5194801637, 3535],
            '55_left': [403216.825876411, 3293175.361620262, 3550],
            '55_right': [403239.42766769446, 3293196.2033249494, 3545],
            '56_left': [403217.30275944166, 3293175.2793290764, 3855],
            '56_right': [403238.3068503913, 3293194.680111519, 3825],
        }
        self._mark_len = 0.66
        self._roi_width = 768

    def get_position_by_mark(self, crane_no, mark, bbox_bias, is_left):
        if is_left:
            crane_key = f'{crane_no}_left'
            bias = mark - bbox_bias
        else:
            crane_key = f'{crane_no}_right'
            bias = mark + bbox_bias
        if crane_key not in self._crane_bases:
            logger.debug(f'no base found for {crane_key}')
            return None

        base_utm = self._crane_bases[crane_key]
        mark = self.correct_mark(base_utm[2], mark)
        utmx = base_utm[0] + math.cos(CRANE_LANE_THETA)*bias
        utmy = base_utm[1] + math.sin(CRANE_LANE_THETA)*bias

        return np.array([utmx, utmy])

    def get_mark_by_position(self, crane_no, utm, bias, is_left):
        if is_left:
            crane_key = f'{crane_no}_left'
        else:
            crane_key = f'{crane_no}_right'
            bias = -bias
        if crane_key not in self._crane_bases:
            logger.debug(f'no base found for {crane_key}')
            return None

        # 根据utm x,y分别反解左右mark值, 取平均
        base_utm = self._crane_bases[crane_key]
        mark_x = (utm[0]-base_utm[0]) / math.cos(CRANE_LANE_THETA) + bias
        mark_y = (utm[1]-base_utm[1]) / math.sin(CRANE_LANE_THETA) + bias
        if abs(mark_x - mark_y) > 5:
            logger.warning(
                f'cr{crane_no} large diff mark from x and y: {mark_x} vs {mark_y}')

        mark = (mark_x + mark_y)/2.0
        return mark

    def estimate_bias(self, bbox, is_left):
        bias_pixel = self._roi_width - bbox[2] if is_left else bbox[0]
        bbox_bias = bias_pixel/(bbox[2] - bbox[0]) * self._mark_len

        return bbox_bias

    def correct_mark(self, base_mark, detected_mark):
        # TODO(ningqingqun) use measured table of coastline
        return detected_mark
