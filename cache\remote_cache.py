import time
from typing import Optional, Iterable, List

from cache.client import Cache<PERSON>lient
from proto import antenna_pb2
from common.logger import logger

REMOTE_CRANE_PREFIX = 'remote-crane-info'
REMOTE_CRANE_DEBUG_PREFIX = 'remote-crane-debug-info'
REMOTE_GANTRY_PREFIX = 'remote-gantry-info'
PLC_REMOTE_GANTRY_PREFIX = 'plc-remote-gantry-info'
HASH_REMOTE_CRANE_PREFIX = 'hash-remote-crane-info'
HASH_REMOTE_CRANE_DEBUG_PREFIX = 'hash-remote-crane-debug-info'
HASH_REMOTE_GANTRY_PREFIX = 'hash-remote-gantry-info'
HASH_BARRIER_GATE_EVENT_PREFIX = 'barrier-gate-event'
HASH_REMOTE_TRAFFIC_LIGHT_PREFIX = 'hash-remote-traffic-light'


def remote_time_valid(info_time_ns, ex = 60.0, cur_time = time.time()):
    if ex is None:
        return True
    return cur_time - info_time_ns / 1000000000.0 < ex

# --------------------—crane--------------------- #
def set_remote_crane(crane_no: int, info: antenna_pb2.GetCraneStatusResponse, is_debug = False, ex=None):
    name = f"{REMOTE_CRANE_PREFIX}"
    key = f"{crane_no}"
    if is_debug:
        name = f"{REMOTE_CRANE_DEBUG_PREFIX}"
    CacheClient().hset_proto(name, key, info)


def get_remote_crane(crane_no: int, is_debug = False, ex = 2) -> Optional[antenna_pb2.GetCraneStatusResponse]:
    name = f"{REMOTE_CRANE_PREFIX}"
    key = f"{crane_no}"
    if is_debug:
        name = f"{REMOTE_CRANE_DEBUG_PREFIX}"
    crane = CacheClient().hget_proto(name, key, antenna_pb2.GetCraneStatusResponse)
    if crane is not None and not remote_time_valid(crane.timestamp_ns, ex):
        crane = None
    return crane


def get_remote_cranes(crane_nos, mode = 0, is_debug = False, ex = 2):
    name = f"{REMOTE_CRANE_PREFIX}"
    if is_debug:
        name = f"{REMOTE_CRANE_DEBUG_PREFIX}"
    keys = [f"{crane_no}" for crane_no in crane_nos]
    cranes = CacheClient().hmget_proto(name, keys, antenna_pb2.GetCraneStatusResponse)
    cur_time = time.time()
    if mode == 0:
       cranes = {crane.crane_id:crane for crane in cranes if crane is not None and remote_time_valid(crane.timestamp_ns, ex, cur_time)}
    else:
       cranes = [crane for crane in cranes if crane is not None and remote_time_valid(crane.timestamp_ns, ex, cur_time)]
    return cranes

def get_remote_ex_cranes(is_debug = False, ex = 2)->List[antenna_pb2.GetCraneStatusResponse]:
    name =  f"{REMOTE_CRANE_PREFIX}"
    if is_debug:
        name = f"{REMOTE_CRANE_DEBUG_PREFIX}"
    cranes = CacheClient().hgetvals_proto(name, antenna_pb2.GetCraneStatusResponse)
    cur_time = time.time()
    cranes = [crane for crane in cranes if crane is not None and remote_time_valid(crane.timestamp_ns, ex, cur_time)]
    return cranes
# ---------------------gantry--------------------- #

def set_remote_gantry(gantry_no: int, info: antenna_pb2.GetGantryStatusResponse, ex=None):
    name = f"{REMOTE_GANTRY_PREFIX}"
    key = f"{gantry_no}"
    CacheClient().hset_proto(name, key, info)


def get_remote_gantry(gantry_no: int, ex = 2) -> Optional[antenna_pb2.GetGantryStatusResponse]:
    name = f"{REMOTE_GANTRY_PREFIX}"
    key = f"{gantry_no}"
    gantry = CacheClient().hget_proto(name, key, antenna_pb2.GetGantryStatusResponse)
    if gantry is not None and not remote_time_valid(gantry.timestamp_ns, ex):
        gantry = None
    return gantry


def get_remote_gantries(gantry_nos, ex = 2) -> List[Optional[antenna_pb2.GetGantryStatusResponse]]:
    name = f"{REMOTE_GANTRY_PREFIX}"
    keys = [f"{gantry_no}" for gantry_no in gantry_nos]
    gantries = CacheClient().hmget_proto(name, keys, antenna_pb2.GetGantryStatusResponse)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if gantry is not None and remote_time_valid(gantry.timestamp_ns, ex, cur_time)]
    return gantries


def get_remote_ex_gantries(ex = 2)->List[antenna_pb2.GetGantryStatusResponse]:
    name = f"{REMOTE_GANTRY_PREFIX}"
    gantries = CacheClient().hgetvals_proto(name, antenna_pb2.GetGantryStatusResponse)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if gantry is not None and remote_time_valid(gantry.timestamp_ns, ex, cur_time)]
    return gantries


def hset_remote_crane(crane_no: int, info: antenna_pb2.GetCraneStatusResponse, is_debug = False, ex=None):
    name = f"{HASH_REMOTE_CRANE_PREFIX}"
    if is_debug:
        name = f"{HASH_REMOTE_CRANE_DEBUG_PREFIX}"
    key = f"{crane_no}"
    CacheClient().hset_proto(name, key, info, ex)

def hget_remote_cranes(is_debug = False)->List[antenna_pb2.GetCraneStatusResponse]:
    name =  f"{HASH_REMOTE_CRANE_PREFIX}"
    if is_debug:
        name = f"{HASH_REMOTE_CRANE_DEBUG_PREFIX}"
    cranes = CacheClient().hget_all_proto(name, antenna_pb2.GetCraneStatusResponse)
    return cranes

def hset_remote_gantry(gantry_no: int, info: antenna_pb2.GetGantryStatusResponse, ex=None):
    name = f"{HASH_REMOTE_GANTRY_PREFIX}:{gantry_no}"
    key = f"{gantry_no}"
    CacheClient().hset_proto(name, key, info, ex)

def hget_remote_gantries()->List[antenna_pb2.GetGantryStatusResponse]:
    name =  f"{HASH_REMOTE_GANTRY_PREFIX}"
    gantries = CacheClient().hget_all_proto(name, antenna_pb2.GetGantryStatusResponse)
    return gantries


# ---------------------nt gantry--------------------- #

def set_plc_remote_gantry(gantry_no: int, info: antenna_pb2.GetGantryStatusResponse, ex=None):
    name = f"{PLC_REMOTE_GANTRY_PREFIX}"
    key = f"{gantry_no}"
    CacheClient().hset_proto(name, key, info)


def get_plc_remote_ex_gantries(ex = 2)->List[antenna_pb2.GetGantryStatusResponse]:
    name = f"{PLC_REMOTE_GANTRY_PREFIX}"
    gantries = CacheClient().hgetvals_proto(name, antenna_pb2.GetGantryStatusResponse)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if gantry is not None and remote_time_valid(gantry.timestamp_ns, ex, cur_time)]
    return gantries


# ---------------------barrier gate--------------------- #
def hgetall_remote_barrier_gates(ex=20) -> List[antenna_pb2.BarrierGateStatus]:
    name = f"{HASH_BARRIER_GATE_EVENT_PREFIX}"
    gates = CacheClient().hgetall_proto(name, antenna_pb2.BarrierGateStatus)
    cur_time = time.time()
    gates = [gate for gate in gates if gate is not None and remote_time_valid(gate.send_time * 1000000000, ex, cur_time)]
    return gates


# --------------------traffic light-----------------------#
def hgetall_remote_traffic_light() -> List[antenna_pb2.TrafficLight]:
    name = f"{HASH_REMOTE_TRAFFIC_LIGHT_PREFIX}"
    lights = CacheClient().hgetall_proto(name, antenna_pb2.TrafficLight)
    lights = [light for light in lights if light is not None]
    return lights

def hset_remote_traffic_light(id : str,info: antenna_pb2.TrafficLight):
    name = f"{HASH_REMOTE_TRAFFIC_LIGHT_PREFIX}"
    key = f"{id}"
    return CacheClient().hset_proto(name, key, info)

def hdel_remote_traffic_light(ids : list):
    name = f"{HASH_REMOTE_TRAFFIC_LIGHT_PREFIX}"
    return CacheClient().hdel(name, *ids)

def hdel_all_remote_traffic_light():
    keys = [f"{HASH_REMOTE_TRAFFIC_LIGHT_PREFIX}"]
    return CacheClient().mdelete(keys)


if __name__ == '__main__':
    print(hgetall_remote_barrier_gates())