import datetime
import traceback

from sqlalchemy import Column, String, Integer, Float, DateTime
from sqlalchemy import event
from sqlalchemy.ext.declarative import declarative_base

from common import common_util
from common.logger import logger
from model.connection import MysqlSession

Base = declarative_base()
Base.to_dict = common_util.to_dict


'设计修改，从MySQL转型至Redis，该class暂时弃用'
class EcsCommand(Base):
    __tablename__ = 'ecs_command'

    id = Column('id', Integer, primary_key=True, autoincrement=True)
    uuid = Column('uuid', String, primary_key=True)
    vehicle_name = Column('vehicle_name', String)
    command_type = Column('command_type', String)
    command_status = Column('command_status', String)
    brake_type = Column('brake_type', String)
    terminal_point_x = Column('terminal_point_x', Float)
    terminal_point_y = Column('terminal_point_y', Float)
    insert_time = Column('insert_time', DateTime, default=datetime.datetime.now)
    update_time = Column('update_time', DateTime, default=datetime.datetime.now)
    cancle_time = Column('cancle_time', DateTime)
    version = Column('version', Integer, default=1)

    def dispatch_command(self, ecs_command):
        session = MysqlSession().acquire()
        try:
            query_result = session.query(EcsCommand).filter_by(uuid=ecs_command.uuid).filter_by(
                vehicle_name=ecs_command.vehicle_name)
            if query_result.count() > 0:
                logger.warning(
                    f"Insert EcsCommand error: data already in the database, uuid: {ecs_command.uuid}, vehicle_name: {ecs_command.vehicle_name}")
                return False
            session.add(ecs_command)
            session.commit()
            return True
        except Exception as e:
            logger.warning(f"Insert EcsCommand error: {e},trace:{traceback.format_exc()}")
            return False
        finally:
            session.close()

    def cancel_command(self, uuid, vehicle_name):
        session = MysqlSession().acquire()
        try:
            query_result = session.query(EcsCommand).filter_by(uuid=uuid).filter_by(vehicle_name=vehicle_name)
            if query_result.count() == 1:
                command_info = query_result.first()
                command_info.update_time = datetime.datetime.now()
                command_info.cancle_time = datetime.datetime.now()
                command_info.version = command_info.version + 1
            else:
                logger.warning(
                    f"Update EcsCommand error: query count: {query_result.count()}, uuid: {uuid}, vehicle_name: {vehicle_name}")
                return False
            session.commit()
            return True
        except Exception as e:
            logger.warning(f"Update EcsCommand error: {e},trace:{traceback.format_exc()}")
            return False
        finally:
            session.close()

    def query_all(self):
        session = MysqlSession().acquire()
        try:
            return session.query(EcsCommand).all()
        except Exception as e:
            logger.warning(f"Query EcsCommand error: {e},trace:{traceback.format_exc()}")
            return None
        finally:
            session.close()


@event.listens_for(EcsCommand, 'after_update')
def test_after_update(mapper, connection, target):
    print("update......")


@event.listens_for(EcsCommand, 'after_insert')
def test_after_insert(mapper, connection, target):
    print("insert......")


if __name__ == '__main__':
    print(EcsCommand.query_all())
