import datetime
import json
import time
from typing import Dict, List, Optional

from cache.client import <PERSON><PERSON><PERSON><PERSON>
from cache.command_cache import Command<PERSON>ache
from cache.map_cache import MapCache
from common import common_util
from common.logger import logger
from common.name_converter import NameConverter
from proto import antenna_pb2
from proto.cache_pb2 import TruckInfo, VehicleFaultStatus
from proto.antenna_pb2 import YardInfo
from proto.fabupilot.data_analyzer import upload_state_pb2
from proto.fabupilot.drivers import device_info_pb2, relay_status_pb2
from proto.fabupilot.perception import calibration_check_info_pb2
from proto.fabupilot.planning import planning_info_pb2
from proto.fabupilot.routing import routing_pb2

VEHICLE_STATUS_KEY_PREFIX = "vehicle-status"
HIGH_FRAME_VEHICLE_STATUS_KEY_PREFIX = "high-frame-vehicle-status"
ONLINE_TRUCK_NO_SET = "online-truck-no-set"
MANUAL_VEHICLE_STATUS_KEY_PREFIX = "manual-vehicle-status"
ONLINE_MANUAL_TRUCK_NO_SET = "online-manual-truck-no-set"
VEHICLE_STATUS_TO_REDIS_KEY_PREFIX = "vehicle_status_to_redis"
TRUCK_INFO_PATH2_TO_REDIS_KEY_PREFIX = "truck_info_path2_to_redis"
TRUCK_INFO_KEY_PREFIX = "truck-info"
WHITE_LIST_TRUCK_PREFIX = "truck-white-list"
PLANNING_INFO_ = "PLANNING_INFO_"
ROUTING_RESPONSE_ = "ROUTING_RESPONSE_"
UPLOAD_STATE_ = "UPLOAD_STATE_"
VEHICLE_PRIORITY = "VEHICLE_PRIORITY_"
VehicleUploadInfo = "VEHICLE_PRIORITY_"
VEHICLE_UPLOAD_INFO = "VEHICLE_UPLOAD_INFO_"
CALIBRATION_CHECK_FEEDBACK_ = "CALIBRATION_CHECK_FEEDBACK_"
VEHICLE_FAULT_STATUS = "vehicle_fault_status_"
THREE_IN_ONE_VEHICLE_SET = "three-in-one-vehicle-set"
VP2V_INFO = "vp2v_cpi_obstacle"
VP2V_RESULT = "vp2v_cpi_result"
I2V_INFO = "inner-truck-cpi"
I2V_RESULT = "inner-truck-cpi-result"
LAST_TRUCK_POSITION_KEY = "last_truck_position"
LAST_TRUCK_SENT_TS_KEY = "last_truck_sent_ts"
OFFLINE_START_HASH = "OFFLINE_START_HASH"
OFFLINE_15S_SET = "OFFLINE_15S_SET"
LAST_VEHICLE_STATUS_KEY_PREFIX = "last_vehicle-status"
VEHICLE_OPERATION_MODE = "vehicle-operation-mode"


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return json.JSONEncoder.default(self, obj)


######################################################################
def __get_vehicle_status_key(node_name, truck_no):
    # 只提取Node
    return f"{VEHICLE_STATUS_KEY_PREFIX}{node_name[:4]}:{truck_no}"


def __get_vehicle_status(truck_no, node_name="", all_status=True):
    key = __get_vehicle_status_key(node_name, truck_no)
    obj = CacheClient().get_proto(key, antenna_pb2.VehicleStatus)
    if not all_status and obj.fit_route_points is not None and len(obj.fit_route_points) > 0:
        del obj.fit_route_points[:]
    return obj


def __get_vehicle_status_list(truck_no_list, node_name="", all_status=True):
    keys = [__get_vehicle_status_key(node_name, truck_no) for truck_no in truck_no_list]
    values = CacheClient().mget_proto(keys, antenna_pb2.VehicleStatus)
    for value in values:
        if not all_status and value.fit_route_points is not None and len(value.fit_route_points) > 0:
            del value.fit_route_points[:]
    return values


def __set_vehicle_status(truck_no, vehicle_status, node_name=""):
    key = __get_vehicle_status_key(node_name, truck_no)
    CacheClient().set_proto(key, vehicle_status)


def __set_vehicle_offline(truck_no, node_name=""):
    key = __get_vehicle_status_key(node_name, truck_no)
    CacheClient().client().delete(key)


def __get_online_trucks(node_name=""):
    key = f"{ONLINE_TRUCK_NO_SET}{node_name}"
    return CacheClient().get_set(key, str)


def __get_online_vehicles_by_keys(node_name=""):
    keys = []
    # truck_nos = NameConverter().all_truck_no()
    truck_nos = get_truck_white_list(node_name)
    if len(truck_nos) == 0:
        logger.warning(f"ATTENTION:server node name:{node_name} may error,no white list!!!")
    # logger.info(f"[get_online_vehicles_by_keys] whilte list({len(truck_nos)}):{sorted(truck_nos)}")
    for truck_no in truck_nos:
        key = __get_vehicle_status_key(node_name, truck_no)
        keys.append(key)
    values = CacheClient().client().mget(keys)
    online_truck = []
    for truck_no, value in zip(truck_nos, values):
        if value is not None:
            online_truck.append(truck_no)
    return online_truck


def __get_refresh_online_trucks(node_name=""):
    current = set(__get_online_vehicles_by_keys(node_name))
    online_key = f"{ONLINE_TRUCK_NO_SET}{node_name}"
    new, stale = CacheClient().get_update_sets(online_key, current, str)
    logger.debug(f"node_name:{node_name}:count:{len(current)} online_vehicles:{sorted(current)}")
    return new, stale


def __refresh_line_trucks(onlines, offlines, node_name=""):
    online_key = f"{ONLINE_TRUCK_NO_SET}{node_name}"
    new, stale = CacheClient().update_sets(online_key, onlines, offlines)
    logger.debug(f"refresh_trucks node_name:{node_name}:new online:{sorted(new)},new offline:{sorted(stale)}")
    return new, stale


def __refresh_online_trucks(node_name=""):
    current = set(__get_online_vehicles_by_keys(node_name))
    online_key = f"{ONLINE_TRUCK_NO_SET}{node_name}"
    new, stale = CacheClient().update_set(online_key, current, str)
    logger.debug(f"node_name:{node_name}:{len(current)} online_vehicles:{sorted(current)}")
    return new, stale


def get_offline_start_time(truck_no):
    key = f"{OFFLINE_START_HASH}"
    return CacheClient().hget(key, truck_no)


def __set_offline_start_time(truck_no, timestamp):
    key = f"{OFFLINE_START_HASH}"
    return CacheClient().hset(key, truck_no, timestamp)


def delete_offline_start_time(truck_no):
    key = f"{OFFLINE_START_HASH}"
    return CacheClient().hdel(key, truck_no.encode())


def get_all_offline_starts():
    key = f"{OFFLINE_START_HASH}"
    offline_starts_dict = CacheClient().hgetall(key)
    offline_starts_dict = {
        k.decode(): v.decode() for k, v in offline_starts_dict.items()
    }
    return offline_starts_dict


def add_offline_15s_truck(truck_no):
    key = f"{OFFLINE_15S_SET}"
    return CacheClient().sadd(key, truck_no)


def remove_offline_15s_truck(truck_no):
    key = f"{OFFLINE_15S_SET}"
    return CacheClient().srem(key, truck_no.encode())


def get_all_offline_15s_trucks():
    key = f"{OFFLINE_15S_SET}"
    return CacheClient().smembers(key)


def clear_offline_data(truck_no):
    r1 = delete_offline_start_time(truck_no)
    r2 = remove_offline_15s_truck(truck_no)
    logger.debug(f"[clear_offline_data] truck_no:{truck_no}, hdel:{r1}, srem:{r2}")


def get_truck_white_list(node_name=""):
    key = f"{WHITE_LIST_TRUCK_PREFIX}:{node_name}"
    return CacheClient().get_set(key, str)


def add_truck_white_list(trucks_no: list, node_name=""):
    key = f"{WHITE_LIST_TRUCK_PREFIX}:{node_name}"
    CacheClient().set_add(key, trucks_no)


def del_truck_white_list(trucks_no: list, node_name=""):
    key = f"{WHITE_LIST_TRUCK_PREFIX}:{node_name}"
    CacheClient().set_rem(key, trucks_no)


def clear_truck_white_list(node_name=""):
    key = f"{WHITE_LIST_TRUCK_PREFIX}:{node_name}"
    CacheClient().delete(key)


def init_truck_white_list(trucks_no: list, node_name=""):
    clear_truck_white_list(node_name)
    add_truck_white_list(trucks_no, node_name)


#####distributed nodes share
def get_vehicle_status(truck_no, all_status=True):
    return __get_vehicle_status(truck_no, all_status=all_status)


def get_vehicle_status_list(truck_no_list, all_status=True):
    return __get_vehicle_status_list(truck_no_list, all_status=all_status)


def set_vehicle_status(truck_no, vehicle_status):
    # ToDo 暂时保留
    if __get_vehicle_status(truck_no) is None:
        CommandCache().clear_last_status_changed_command(truck_no)
    __set_vehicle_status(truck_no, vehicle_status)


def set_vehicle_offline(truck_no):
    __set_vehicle_offline(truck_no)


def get_online_trucks():
    return __get_online_trucks()


def refresh_online_trucks():
    return __refresh_online_trucks()


#####local node
def get_vehicle_status_(truck_no, node_name="", all_status=True):
    return __get_vehicle_status(truck_no, node_name, all_status)


def set_vehicle_status_(truck_no, vehicle_status, node_name=""):
    node_name = node_name
    if get_vehicle_status_(truck_no, node_name) is None:
        CommandCache().clear_last_status_changed_command(truck_no)
    __set_vehicle_status(truck_no, vehicle_status, node_name)


def set_vehicle_offline_(truck_no, node_name=""):
    end_status = get_vehicle_status(truck_no)
    set_last_vehicle_status(truck_no, end_status)
    # set global
    set_vehicle_offline(truck_no)
    # set local node
    if len(node_name) > 0:
        __set_vehicle_offline(truck_no, node_name)
    CommandCache().clear_last_status_changed_command(truck_no)


def get_online_trucks_(node_name=""):
    return __get_online_trucks(node_name)


def refresh_online_trucks_(node_name=""):
    new, stale = __get_refresh_online_trucks(node_name)
    if len(new) > 0 or len(stale) > 0:
        # refresh global
        __refresh_line_trucks(new, stale)
        # refresh node
        __refresh_line_trucks(new, stale, node_name)
    return new, stale


def sync_online_trucks_(node_name=""):
    node_white_trucks = get_truck_white_list(node_name)
    node_online_trucks = get_online_trucks_(node_name)
    node_offline_trucks = set(node_white_trucks) - set(node_online_trucks)
    white_trucks = NameConverter().all_truck_no()
    online_trucks = get_online_trucks()
    offline_trucks = set(white_trucks) - set(online_trucks)
    sync_online_trucks = sorted(list(set(node_online_trucks) & set(offline_trucks)))
    sync_offline_trucks = sorted(list(set(node_offline_trucks) & set(online_trucks)))

    print(f"node_online_trucks:{node_online_trucks}")
    print(f"node_white_trucks:{node_white_trucks}")
    print(f"node_offline_trucks:{node_offline_trucks}")
    print(f"online_trucks:{online_trucks}")
    print(f"white_trucks:{white_trucks}")
    print(f"offline_trucks:{offline_trucks}")
    print(f"sync_online_trucks:{sync_online_trucks}")
    print(f"sync_offline_trucks:{sync_offline_trucks}")
    if len(sync_online_trucks) > 0 or len(sync_offline_trucks) > 0:
        print(
            f"ATTENTION:node_name:{node_name} may not happen:sync_online_trucks:{sync_online_trucks},sync_offline_trucks:{sync_offline_trucks}"
        )
        __refresh_line_trucks(sync_online_trucks, sync_offline_trucks)


##################################
# 2022.02.21 mysql truck_info 的 PATH2 信息保存至 redis
def set_truck_info_path2_to_redis(truck_no, path2):
    key = f"{TRUCK_INFO_PATH2_TO_REDIS_KEY_PREFIX}"
    CacheClient().set_field(key, truck_no, json.dumps(path2))


# 2022.02.21 get redis  PATH2
def get_truck_info_path2_from_redis(truck_no):
    key = f"{TRUCK_INFO_PATH2_TO_REDIS_KEY_PREFIX}"
    if CacheClient().get_field(key, truck_no):
        return json.loads(CacheClient().get_field(key, truck_no))
    else:
        logger.warning(f"get_truck_info_path2_from_redis failed:{truck_no}")
        return ""


def set_vehicle_status_to_redis(truck_no, info):
    key = f"{VEHICLE_STATUS_TO_REDIS_KEY_PREFIX}"
    CacheClient().set_field(key, truck_no, json.dumps(info, cls=DateEncoder))


def get_high_frame_vehicle_status(truck_no, all_status=True):
    key = f"{HIGH_FRAME_VEHICLE_STATUS_KEY_PREFIX}:{truck_no}"
    obj = CacheClient().get_proto(key, antenna_pb2.HighFrameVehicleStatus)
    if not all_status and len(obj.fit_route_points) > 0:
        del obj.fit_route_points[:]
    return obj


def set_high_frame_vehicle_status(truck_no, high_frame_vehicle_status):
    key = f"{HIGH_FRAME_VEHICLE_STATUS_KEY_PREFIX}:{truck_no}"
    CacheClient().set_proto(key, high_frame_vehicle_status)


####
def get_truck_info(truck_no):
    name = f"{TRUCK_INFO_KEY_PREFIX}"
    key = f"{truck_no}"
    return CacheClient().hget_proto(name, key, TruckInfo)


def get_all_trucks_info():
    name = f"{TRUCK_INFO_KEY_PREFIX}"
    return CacheClient().hgetvals_proto(name, TruckInfo)


def set_truck_info(truck_no, info: TruckInfo):
    name = f"{TRUCK_INFO_KEY_PREFIX}"
    key = f"{truck_no}"
    info.timestamp_ms = int(time.time() * 1000)
    info.timestamp = datetime.datetime.fromtimestamp(info.timestamp_ms / 1000).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    return CacheClient().hset_proto(name, key, info)


def set_truck_offline(truck_no):
    if (info := get_truck_info(truck_no)) is not None:
        info.truck_mode = 0
        set_truck_info(truck_no, info)


# 根据vehicle_name获取车辆状态
def get_truck_fault_status(vehicle_name: str):
    key = f"{VEHICLE_FAULT_STATUS}{vehicle_name}"
    return CacheClient().get_proto(key, VehicleFaultStatus)


# 根据 vehicle_names 批量获取车辆状态
def get_truck_fault_status_by_names(names):
    keys = [f"{VEHICLE_FAULT_STATUS}{name}" for name in names if name]
    result = {}
    if len(keys) == 0:
        return result
    for val in CacheClient().client().mget(keys) or []:
        if val:
            status = VehicleFaultStatus()
            status.ParseFromString(val)
            result[status.vehicle_name] = status.long_term_stop
    return result


####
def get_manual_vehicle_status(truck_no):
    key = f"{MANUAL_VEHICLE_STATUS_KEY_PREFIX}:{truck_no}"
    obj = CacheClient().get_proto(key, antenna_pb2.VehicleStatus)
    return obj


def set_manual_vehicle_status(truck_no, vehicle_status):
    key = f"{MANUAL_VEHICLE_STATUS_KEY_PREFIX}:{truck_no}"
    CacheClient().set_proto(key, vehicle_status)


def set_manual_vehicle_offline(truck_no):
    key = f"{MANUAL_VEHICLE_STATUS_KEY_PREFIX}:{truck_no}"
    CacheClient().client().delete(key)


def get_online_manual_trucks():
    return CacheClient().get_set(ONLINE_MANUAL_TRUCK_NO_SET, str)


def _get_online_manual_vehicles_by_keys():
    primary_key = f"""{MANUAL_VEHICLE_STATUS_KEY_PREFIX}*"""
    keys = CommandCache().client().keys(primary_key)
    logger.info(f"primary_key:{primary_key},keys:{keys}")
    online_truck = []
    for key in keys:
        truck_no = key.split(MANUAL_VEHICLE_STATUS_KEY_PREFIX)[1]
        online_truck.append(truck_no)
    return online_truck


def refresh_online_manual_trucks():
    current = set(_get_online_manual_vehicles_by_keys())
    new, stale = CacheClient().update_set(ONLINE_MANUAL_TRUCK_NO_SET, current, str)
    logger.debug(f"online_manual_vehicles:{current}")
    return new, stale


def set_planning_info(vehicle_name_at, planning_info):
    key = f"{PLANNING_INFO_}{vehicle_name_at}"
    return CacheClient().set_proto(key, planning_info, ex=2)


def get_planning_info(vehicle_name_at):
    key = f"{PLANNING_INFO_}{vehicle_name_at}"
    return CacheClient().get_proto(key, planning_info_pb2.PlanningInfo)


# 全局路径每次修改时才重新写入，故不设置超时时间
def set_routing_response(vehicle_name_at, routing_response):
    key = f"{ROUTING_RESPONSE_}{vehicle_name_at}"
    return CacheClient().set_proto(key, routing_response)


def get_routing_response(vehicle_name_at):
    key = f"{ROUTING_RESPONSE_}{vehicle_name_at}"
    return CacheClient().get_proto(key, routing_pb2.RoutingResponse)


def get_routing_response_list(vehicle_name_at_list):
    with CacheClient().client().pipeline() as pipe:
        for vehicle_name_at in vehicle_name_at_list:
            pipe.get(f"{ROUTING_RESPONSE_}{vehicle_name_at}")
        routing_response_list = []
        for routing_response in pipe.execute():
            if routing_response:
                routing_response_pb = routing_pb2.RoutingResponse()
                routing_response_pb.ParseFromString(routing_response)
                routing_response_list.append(routing_response_pb)
            else:
                routing_response_list.append(None)
        return routing_response_list


# 车端数据上传进度
def hset_upload_state(vehicle_name_at, computer_name, upload_state):
    name = f"{UPLOAD_STATE_}{vehicle_name_at}"
    return CacheClient().hset_proto(name, computer_name, upload_state)


def get_upload_state(vehicle_name_at, computer_name):
    name = f"{UPLOAD_STATE_}{vehicle_name_at}"
    return CacheClient().hget_proto(name, computer_name, upload_state_pb2.UploadStateList)


def rpush_vehicle_upload_info(vehicle_upload_info):
    return CacheClient().rpush(VEHICLE_UPLOAD_INFO, vehicle_upload_info.SerializeToString())


def lpop_vehicle_upload_info():
    return CacheClient().lpop(VEHICLE_UPLOAD_INFO)


# 全局调度车辆优先级相关数据
def set_vehicle_priority(vehicle_priority):
    key = f"{VEHICLE_PRIORITY}"
    return CacheClient().set_proto(key, vehicle_priority, ex=3)


def get_vehicle_priority():
    key = f"{VEHICLE_PRIORITY}"
    return CacheClient().get_proto(key, antenna_pb2.PushVehiclePriorityRequest)


def set_calibration_check_feed_back(vehicle_name_at, calibration_check_feed_back):
    key = f"{CALIBRATION_CHECK_FEEDBACK_}{vehicle_name_at}"
    return CacheClient().set_proto(key, calibration_check_feed_back)


def get_calibration_check_feed_back():
    key = f"{CALIBRATION_CHECK_FEEDBACK_}"
    return CacheClient().get_proto(key, calibration_check_info_pb2.CalibrationCheckFeedback)


def set_three_in_one_vehicle(vehicle_list):
    CacheClient().delete(THREE_IN_ONE_VEHICLE_SET)
    for vehicle in vehicle_list:
        CacheClient().sadd(THREE_IN_ONE_VEHICLE_SET, vehicle)


def get_three_in_one_vehicles():
    vehicle_list = []
    for member in CacheClient().smembers(THREE_IN_ONE_VEHICLE_SET):
        vehicle_list.append(member.decode())
    return vehicle_list


def set_vehicle_vp2v_info(vehicle_name_at, vp2v_info, ex=5):
    key = f"{VP2V_INFO}:{vehicle_name_at}"
    return CacheClient().set_proto(key, vp2v_info, ex=ex)


def set_all_vehicle_vp2v_info(vehicle_name_at_list, vp2v_info_list, ex=5):
    keys = [f"{VP2V_INFO}:{vehicle_name_at}" for vehicle_name_at in vehicle_name_at_list]
    with CacheClient().pipeline() as pipe:
        for key, vp2v_info in zip(keys, vp2v_info_list):
            pipe.set(key, vp2v_info.SerializeToString(), ex=ex)
        pipe.execute()


def get_vehicle_vp2v_info(vehicle_name_at):
    key = f"{VP2V_INFO}:{vehicle_name_at}"
    return CacheClient().get_proto(key, antenna_pb2.CPIMessageRequest)


def get_all_vehicle_vp2v_info():
    online_vehicles = get_online_trucks()
    keys = [f"{VP2V_INFO}:{vehicle}" for vehicle in online_vehicles]
    values = CacheClient().mget_proto(keys, antenna_pb2.CPIMessageRequest)
    result = {}
    for vehicle, value in zip(online_vehicles, values):
        if value:
            result[vehicle] = value
    return result


def set_vehicle_vp2v_result(
    vehicle_name_at, vp2v_result: antenna_pb2.CPIResultDataCache
):
    key = f"{VP2V_RESULT}:{vehicle_name_at}"
    return CacheClient().set_proto(key, vp2v_result, ex=5)


def set_all_vehicle_vp2v_result(
    vehicle_name_at_list, vp2v_result_list: List[antenna_pb2.CPIResultDataCache], ex=5
):
    keys = [
        f"{VP2V_RESULT}:{vehicle_name_at}" for vehicle_name_at in vehicle_name_at_list
    ]
    with CacheClient().pipeline() as pipe:
        for key, vp2v_result in zip(keys, vp2v_result_list):
            pipe.set(key, vp2v_result.SerializeToString(), ex=ex)
        pipe.execute()


def get_global_vehicle_vp2v_result() -> Optional[antenna_pb2.CPIResultDataCache]:
    key = f"{VP2V_RESULT}"
    return CacheClient().get_proto(key, antenna_pb2.CPIResultDataCache)


def set_global_vehicle_vp2v_result(
    cpi_result_data_cache: antenna_pb2.CPIResultDataCache, ex=5
):
    key = f"{VP2V_RESULT}"
    return CacheClient().set_proto(key, cpi_result_data_cache, ex=ex)


def get_vehicle_vp2v_result(
    vehicle_name_at,
) -> Optional[antenna_pb2.CPIResultDataCache]:
    key = f"{VP2V_RESULT}:{vehicle_name_at}"
    return CacheClient().get_proto(key, antenna_pb2.CPIResultDataCache)


def get_all_vehicle_vp2v_result() -> Dict[str, antenna_pb2.CPIResultDataCache]:
    online_vehicles = get_online_trucks()
    keys = [f"{VP2V_RESULT}:{vehicle}" for vehicle in online_vehicles]
    values = CacheClient().mget_proto(keys, antenna_pb2.CPIResultDataCache)
    result = {}
    for vehicle, value in zip(online_vehicles, values):
        if value:
            result[vehicle] = value
    return result


def get_all_i2v_info() -> Dict[str, str]:
    # key是车辆名称，value是车辆信息的json string
    # 车辆信息的json string格式如下：
    # {
    #     "boxPosition": [x1, y1, x2, y2, x3, y3, x4, y4],
    #     "heading": 0.0,
    #     "speed": 0.0,
    #     "utmX": 0.0,
    #     "utmY": 0.0,
    #     "timeStampMs": 0,
    #     "workNo": "T181"
    # }
    key = f"{I2V_INFO}"
    values = CacheClient().get_dict(key)
    result = {}
    try:
        for vehicle, value in values.items():
            result[vehicle] = json.loads(value)
    except Exception as e:
        logger.error(f"get_all_i2v_info failed: {e}")
        return {}
    return result


def get_global_i2v_result() -> Optional[antenna_pb2.CPIResultDataCache]:
    key = f"{I2V_RESULT}"
    return CacheClient().get_proto(key, antenna_pb2.CPIResultDataCache)


def set_global_i2v_result(cpi_result_data_cache: antenna_pb2.CPIResultDataCache, ex=5):
    key = f"{I2V_RESULT}"
    return CacheClient().set_proto(key, cpi_result_data_cache, ex=ex)


def get_vehicle_i2v_result(vehicle_name_at) -> Optional[antenna_pb2.CPIResultDataCache]:
    key = f"{I2V_RESULT}:{vehicle_name_at}"
    return CacheClient().get_proto(key, antenna_pb2.CPIResultDataCache)


def set_vehicle_i2v_result(vehicle_name_at, i2v_result: antenna_pb2.CPIResultDataCache):
    key = f"{I2V_RESULT}:{vehicle_name_at}"
    return CacheClient().set_proto(key, i2v_result, ex=5)

def set_last_position(truck_no: str, pos_x: float, pos_y: float):
    key = LAST_TRUCK_POSITION_KEY
    value = f"{pos_x},{pos_y}"
    return CacheClient().set_field(key, truck_no, value)

def get_last_position(truck_no: str) -> tuple:
    key = LAST_TRUCK_POSITION_KEY
    value = CacheClient().get_field(key, truck_no)
    if value:
        try:
            pos_x, pos_y = map(float, value.decode().split(','))
            return (pos_x, pos_y)
        except Exception as e:
            logger.warning(f"parse last position failed for {truck_no}: {e}")
    return None

def set_all_last_position(pos_dict: dict):
    key = LAST_TRUCK_POSITION_KEY
    str_dict = {k: f"{v[0]},{v[1]}" for k, v in pos_dict.items()}
    return CacheClient().hmset(key, str_dict)

def get_all_last_position() -> dict:
    key = LAST_TRUCK_POSITION_KEY
    raw_dict = CacheClient().get_dict(key)
    result = {}
    for k, v in raw_dict.items():
        try:
            truck_no = k.decode()
            pos_x, pos_y = map(float, v.decode().split(','))
            result[truck_no] = (pos_x, pos_y)
        except Exception as e:
            logger.warning(f"parse position failed: key={k}, value={v}, err={e}")
    return result

def set_all_last_sent_ts(ts_dict: dict):
    key = LAST_TRUCK_SENT_TS_KEY
    str_dict = {k: str(v) for k, v in ts_dict.items()}
    return CacheClient().hmset(key, str_dict)

def get_all_last_sent_ts() -> dict:
    key = LAST_TRUCK_SENT_TS_KEY
    raw_dict = CacheClient().get_dict(key)
    result = {}
    for k, v in raw_dict.items():
        try:
            truck_no = k.decode()
            ts = float(v.decode())
            result[truck_no] = ts
        except Exception as e:
            logger.warning(f"parse last sent ts failed: key={k}, value={v}, err={e}")
    return result

def __get_last_vehicle_status_key(node_name, truck_no):
    return f"{LAST_VEHICLE_STATUS_KEY_PREFIX}{node_name[:4]}:{truck_no}"


def set_last_vehicle_status(truck_no, vehicle_status, node_name=""):
    if vehicle_status is None:
        return
    key = __get_last_vehicle_status_key(node_name, truck_no)
    CacheClient().set_proto(key, vehicle_status)


def get_last_vehicle_status(truck_no, node_name="", all_status=True):
    key = __get_last_vehicle_status_key(node_name, truck_no)
    obj = CacheClient().get_proto(key, antenna_pb2.VehicleStatus)
    if (
        not all_status
        and obj.fit_route_points is not None
        and len(obj.fit_route_points) > 0
    ):
        del obj.fit_route_points[:]
    return obj


def set_offline_start_time(truck_no):
    if get_offline_start_time(truck_no) is not None:
        return
    __set_offline_start_time(truck_no, int(time.time()))


def set_offline_15s_vehicle_list():
    # 更新离线超过15秒的车辆集合
    offline_starts = get_all_offline_starts()
    current_ts = time.time()

    yard_info = MapCache().get_map_yard_info()
    yard_dict = {yard.name: yard for yard in yard_info.yards}

    for truck_no, offline_start_ts_str in offline_starts.items():
        try:
            offline_start_ts = float(offline_start_ts_str)
        except ValueError:
            logger.warning(f"Invalid timestamp for truck {truck_no}: {offline_start_ts_str}")
            continue

        offline_duration = current_ts - offline_start_ts
        if offline_duration < 5:
            remove_offline_15s_truck(truck_no)
            # logger.debug(f"[{truck_no}] removed (offline {offline_duration:.1f}s < 5s)")
            continue

        last_status = get_last_vehicle_status(truck_no)
        if not last_status:
            # logger.debug(f"[{truck_no}] skipped (no last status)")
            continue

        behavior_info = getattr(last_status, "behavior_info_sub", None)
        scene_info = getattr(behavior_info, "scene_info", None)

        scene_type = getattr(scene_info, "scene_type", "")
        if scene_type != "PORT_MEISHAN_YARD":
            # logger.debug(f"[{truck_no}] skipped (scene_type={scene_type})")
            continue

        road_name = getattr(scene_info, "road_name", "")
        function_type = getattr(behavior_info, "function_type", "")
        stage_type = getattr(behavior_info, "stage_type", "")

        left_yard, right_yard = common_util.parse_road_name(road_name)
        if not left_yard or not right_yard:
            continue

        left_mode = common_util.get_yard_work_mode(yard_dict, left_yard)
        right_mode = common_util.get_yard_work_mode(yard_dict, right_yard)

        # 如果其中任意一个有值且不是 GANTRY，则跳过
        if (left_mode is not None and left_mode != YardInfo.WorkMode.GANTRY) or \
                (right_mode is not None and right_mode != YardInfo.WorkMode.GANTRY):
            # logger.debug(f"[{truck_no}] skipped (work_mode not GANTRY: {left_mode}, {right_mode})")
            continue

        pos = (last_status.position.utm_x, last_status.position.utm_y)
        overtaking_lanes = common_util.get_yard_overtaking_lanes_by_location(pos)

        if overtaking_lanes or "CHANGE" in function_type or stage_type == "CHANGING":
            add_offline_15s_truck(truck_no)
            logger.debug(f"[{truck_no}] added to offline_15s_trucks")

def get_vehicle_operation_mode(vehicle_name_at) -> antenna_pb2.VehicleOperationMode:
    key = f"{VEHICLE_OPERATION_MODE}"
    value = CacheClient().hget(key, vehicle_name_at)
    if value:
        try:
            return antenna_pb2.VehicleOperationMode.Value(value.decode())
        except Exception as e:
            logger.error(f"get_vehicle_operation_mode failed: {e}")
    return antenna_pb2.VehicleOperationMode.ALL


def set_vehicle_operation_mode(vehicle_name_at: str, operation_mode: antenna_pb2.VehicleOperationMode):
    key = f"{VEHICLE_OPERATION_MODE}"
    return CacheClient().hset(key, vehicle_name_at, antenna_pb2.VehicleOperationMode.Name(operation_mode))


def get_all_vehicle_operation_mode() -> Dict[str, int]:
    key = f"{VEHICLE_OPERATION_MODE}"
    values = CacheClient().get_dict(key)
    result = {}
    for k, v in values.items():
        try:
            result[k.decode()] = antenna_pb2.VehicleOperationMode.Value(v.decode())
        except Exception as e:
            logger.error(f"get_all_vehicle_operation_mode failed: {e}")
            result[k.decode()] = antenna_pb2.VehicleOperationMode.ALL
    return result


if __name__ == "__main__":
    print(get_vehicle_status("IGV811"))
