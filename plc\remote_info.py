import math
import selectors
import socket
import time
import traceback
import types
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List

import numpy as np
from google.protobuf.text_format import MessageToString

from common.name_converter import NameConverter
from plc.crane_receiver import CraneInfoContainer
from plc.gantry_receiver import GantryInfoContainer
from plc.model import is_crane_mm_enable
from cache import crane_calib, plc_cache, remote_cache
from cache.config_cache import ConfigCache
from ship.map_ships import MapShips
from common.singleton import Singleton
from common.logger import logger
from common.periodical_task import PeriodicalTask
from common.common_util import from_utm_to_latlon
from proto import antenna_pb2
from tos_db import TosDb
from proto.cache_pb2 import TpToGantryInfo
from cache.arrive_cache import ArriveCache
from cache.command_cache import CommandCache


class PlcRemoteInfo(object):
    def __init__(self):
        self._db = TosDb()
        self._crane_info_container = CraneInfoContainer()
        self._gantry_info_container = GantryInfoContainer()
        self._running = False
        self._common_loop_interval = 0.5
        self._crane_interval_time = 0.05
        self._gantry_interval_time = 0.05
        self._gantry_update_status_interval_time = 0.5
        self._crane_update_lanes_interval = 0.1
        self._crane_update_lanes_count = 0
        self._crane_lanes = dict()
        self._crane_lanes_debug = dict()
        self._common_loop_thread = None
        self._crane_update_lanes_loop_thread = None
        self._crane_loop_thread = None
        self._gantry_loop_thread = None
        self._debug_crane_enable = True
        self._gantry_status = dict()
        self._last_gantry_store_server_alive = None
        self._last_gantry_status = None
        self._map_ships = MapShips()
        self._config_cache = None
        self._manual_offsets = dict()

    def start_common(self):
        master_only = True
        self._common_loop_thread = PeriodicalTask(target=self.common_loop, interval=self._common_loop_interval, master_only=master_only)
        self._common_loop_thread.start()

    def start_crane(self):
        master_only = True
        self._crane_update_lanes_loop_thread = PeriodicalTask(target=self.crane_update_lanes_loop, interval=self._crane_update_lanes_interval, master_only=master_only)
        self._crane_loop_thread = PeriodicalTask(target=self.crane_loop, interval=self._crane_interval_time, master_only=master_only)
        self._crane_update_lanes_loop_thread.start()
        self._crane_loop_thread.start()

    def start_gantry(self):
        master_only = True
        self._config_cache = ConfigCache()
        self._gantry_update_status_loop_thread = PeriodicalTask(target=self.gantry_update_status_loop, interval=self._gantry_update_status_interval_time, master_only=master_only)
        self._gantry_update_status_loop_thread.start()
        self._gantry_loop_thread = PeriodicalTask(target=self.gantry_loop, interval=self._gantry_interval_time, master_only=master_only)
        self._gantry_loop_thread.start()

    def start(self):
        if not self._running:
            self.start_common()
            self.start_crane()
            self.start_gantry()
            self._running = True

    def common_loop(self):
        # 更新人工设置的对位偏移
        self._manual_offsets = plc_cache.get_all_device_manual_offset()

    def get_remote_crane_info(self, info, manual_offset=None):
        crane_info = info.get('crane_info')
        lane_no = info.get('lane_no')
        direction = info.get('direction')
        working_bay = info.get('working_bay')
        cycle_mode = info.get('cycle_mode')
        lane_no_debug = info.get('lane_no_debug')
        direction_debug = info.get('direction_debug')
        working_bay_debug = info.get('working_bay_debug')
        cycle_mode_debug = info.get('cycle_mode_debug')
        offset = info.get('offset',None)
        debug_crane_enable = info.get('debug_crane_enable',True)
        response = antenna_pb2.GetCraneStatusResponse()
        response.crane_id = crane_info.crane_no
        #response.cps_guide_cm = crane_info.cps_guide
        #response.crane_bay = str(crane_info.crane_bay)
        response.crane_stable = (crane_info.g_state == 1)
        response.spreader_height_cm = crane_info.h_pos
        response.spreader_locked = (crane_info.spr_state == 2)
        response.spreader_size = crane_info.spr_size
        response.impassible = (crane_info.qcenable != 1)
        response.timestamp_ns = crane_info.recv_timestamp
        response.is_double_cycle = True if cycle_mode == 'D' else False
        response.g_pos = crane_info.g_pos if not is_crane_mm_enable(crane_info) else crane_info.g_pos_b
        if crane_info.spr_pos in antenna_pb2.SpreaderPosition.values():
            response.spreader_position = crane_info.spr_pos
        else:
            response.spreader_position = antenna_pb2.SpreaderPosition.SPREADER_POSITION_UNKNOWN
        position = self._crane_info_container.get_position(crane_info, offset)
        if position is not None:
            response.approximate_point.utm_x = position[0]
            response.approximate_point.utm_y = position[1]
            #extra add
            response.approximate_point.latitude, response.approximate_point.longitude = from_utm_to_latlon(position[0],position[1])
        calibration_position = self._crane_info_container.get_calibration_position(crane_info, offset)
        if calibration_position is not None:
            response.approximate_point_calib.utm_x = calibration_position[0]
            response.approximate_point_calib.utm_y = calibration_position[1]
        if crane_info.t_pos < 5100 and crane_info.t_pos > 0:  # <1车道t_pos,>9车道t_pos
            response.is_spreader_on_lane = True
            response.spreader_lane = self._crane_info_container.get_spreader_lane(crane_info)
            #response.spreader_lane_old = crane_info.qclane
        else:
            response.is_spreader_on_lane = False
        spreader_loc = self._crane_info_container.get_spreader_location(crane_info, offset)
        if spreader_loc is not None:
            response.spreader_location.utm_x = spreader_loc[0]
            response.spreader_location.utm_y = spreader_loc[1]
            if self._map_ships is not None:
                response.coastline_coordinate = int(self._map_ships.convert_utm_to_coastline_coordinate(spreader_loc[0], spreader_loc[1]))
        spreader_spd = self._crane_info_container.get_lane_spd(crane_info)
        response.spreader_speed.utm_x_spd = spreader_spd[0]
        response.spreader_speed.utm_y_spd = spreader_spd[1]
        #response.netload = crane_info.netload * 10
        response.is_working = False
        #default R
        response.vessel_direction = antenna_pb2.VesselDirection.LEFT if direction == 'L' else antenna_pb2.VesselDirection.RIGHT
        response.working_bay.CopyFrom(working_bay)
        if lane_no is not None:
            lane_no = int(lane_no)
            if lane_no > 0:
                location = self._crane_info_container.get_lane_location(lane_no, crane_info, offset)
                if location is not None:
                    response.working_point.utm_x = location[0]
                    response.working_point.utm_y = location[1]
                response.working_lane = lane_no
                response.is_working = True
        if manual_offset is not None:
            response.manual_offset = manual_offset
        response_debug  = antenna_pb2.GetCraneStatusResponse() if debug_crane_enable else None
        if response_debug is not None:
            response_debug.CopyFrom(response)
            if lane_no_debug is not None:
                lane_no_debug = int(lane_no_debug)
                if lane_no_debug > 0:
                    location = self._crane_info_container.get_lane_location(lane_no_debug, crane_info, offset)
                    if location is not None:
                        response_debug.working_point.utm_x = location[0]
                        response_debug.working_point.utm_y = location[1]
                    response_debug.working_lane = lane_no_debug
                    response_debug.is_working = True
            #default R
            response_debug.vessel_direction = antenna_pb2.VesselDirection.LEFT if direction_debug == 'L' else antenna_pb2.VesselDirection.RIGHT
            response_debug.working_bay.CopyFrom(working_bay_debug)
            response_debug.is_double_cycle = True if cycle_mode_debug == 'D' else False
        return (response, response_debug)

    def crane_loop(self):
        start_time = time.time()
        crane_nos = plc_cache.get_online_crane_nos()
        if crane_nos:
            cranes = self._crane_info_container.get_all_crane_info(crane_nos)
            calib_offset_cranes = [crane.crane_no for crane in cranes if crane is not None]
            offsets = crane_calib.get_crane_calib_offsets(calib_offset_cranes)
            for crane, offset in zip(cranes, offsets):
                try:
                    info = dict()
                    info['crane_info'] = crane
                    info['lane_no'] = self._crane_lanes[crane.crane_no].get('LANE_NO') if crane.crane_no in self._crane_lanes else None
                    info['direction'] = self._crane_lanes[crane.crane_no].get('VESSEL_DIRECTION') if crane.crane_no in self._crane_lanes else None
                    info['cycle_mode'] = self._crane_lanes[crane.crane_no].get('CYCLE_MODE','S') if crane.crane_no in self._crane_lanes else 'S'
                    info['lane_no_debug'] = self._crane_lanes_debug[crane.crane_no].get('LANE_NO') if crane.crane_no in self._crane_lanes_debug else None
                    info['direction_debug'] = self._crane_lanes_debug[crane.crane_no].get('VESSEL_DIRECTION') if crane.crane_no in self._crane_lanes_debug else None
                    info['cycle_mode_debug'] = self._crane_lanes_debug[crane.crane_no].get('CYCLE_MODE','S') if crane.crane_no in self._crane_lanes_debug else 'S'
                    working_bay = antenna_pb2.WorkingBay()
                    working_bay.bay1 = self._crane_lanes[crane.crane_no].get('BAY1', 'None') if crane.crane_no in self._crane_lanes else 'None'
                    working_bay.bay2 = self._crane_lanes[crane.crane_no].get('BAY2', 'None') if crane.crane_no in self._crane_lanes else 'None'
                    working_bay.bay3 = self._crane_lanes[crane.crane_no].get('BAY3', 'None') if crane.crane_no in self._crane_lanes else 'None'
                    info['working_bay'] = working_bay
                    working_bay_debug = antenna_pb2.WorkingBay()
                    working_bay_debug.bay1 = self._crane_lanes_debug[crane.crane_no].get('BAY1', 'None') if crane.crane_no in self._crane_lanes_debug else 'None'
                    working_bay_debug.bay2 = self._crane_lanes_debug[crane.crane_no].get('BAY2', 'None') if crane.crane_no in self._crane_lanes_debug else 'None'
                    working_bay_debug.bay3 = self._crane_lanes_debug[crane.crane_no].get('BAY3', 'None') if crane.crane_no in self._crane_lanes_debug else 'None'
                    info['working_bay_debug'] = working_bay_debug
                    info['offset'] = offset
                    info['debug_crane_enable'] = self._debug_crane_enable
                    (remote,remote_debug) = self.get_remote_crane_info(info, manual_offset=self._manual_offsets.get(f"crane_{crane.crane_no}"))
                    remote_cache.set_remote_crane(remote.crane_id,remote,False,2)
                    if remote_debug is not None:
                        remote_cache.set_remote_crane(remote.crane_id,remote_debug,True,2)
                except Exception as e:
                    logger.warning(f"crane_loop: crane_no:{crane.crane_no}, err:{e}, trace:{traceback.format_exc()}")
        end_time = time.time()
        if end_time - start_time > 0.1:
            logger.warning(f"crane_loop over cost:{end_time - start_time}")

    def crane_update_lanes_loop(self):
        start_time = time.time()
        crane_nos = plc_cache.get_online_crane_nos()
        if crane_nos:
            self._crane_lanes = self._db.batch_query_cranes(crane_nos, False)
            if self._debug_crane_enable:
                self._crane_lanes_debug = self._db.batch_query_cranes(crane_nos, True)
        self._crane_update_lanes_count = self._crane_update_lanes_count + 1
        if (1 // self._crane_update_lanes_interval) <= self._crane_update_lanes_count:
            logger.debug(f"crane_update_lanes_loop:crane_lanes:{self._crane_lanes}")
            self._crane_update_lanes_count = 0
        end_time = time.time()
        if end_time - start_time > 0.1:
            logger.warning(f"crane_update_lanes_loop over cost:{end_time - start_time}")

    def _convert_to_act_yard(self, block_nul):
        yard_list = []
        if block_nul <= 0:
            return yard_list
        yard = str(hex((block_nul-1) // 14 + 6))[-1:] + (yard_1 if (yard_1:= str(hex(block_nul % 14))[-1]) != '0' else 'E')
        yard = yard.upper()

        convert_dict = {'A': 'X', 'B': 'Y'}
        first_alphabet = yard[0]
        if first_alphabet in convert_dict:
            yard = convert_dict[first_alphabet] + yard[1:]

        mul_yards = [['91', 'J1'], ['92', 'J2']]
        for mul_yard in mul_yards:
            if yard in mul_yard:
                yard_list.extend(mul_yard)
                yard = ''
                break
        if len(yard) > 0:
            yard_list.append(yard)
        return yard_list

    def get_gantry_type(self, gantry_no):
        # 非空判断
        if gantry_no is None:
            return antenna_pb2.GetGantryStatusResponse.GantryType.MS_REMOTE_CONTROL  #如果龙门吊没有属性类型type设置为远控

        # 判断 gantry_no 是否在指定的范围内
        if 119 <= gantry_no <= 124 or 163 <= gantry_no <= 171:
            return antenna_pb2.GetGantryStatusResponse.GantryType.MS_LOCAL_CONTROL  # 游龙
        else:
            return antenna_pb2.GetGantryStatusResponse.GantryType.MS_REMOTE_CONTROL  # 远控

    def get_remote_gantry_info(self, gantry_info, gantry_status=None, manual_offset=None) -> antenna_pb2.GetGantryStatusResponse:
        response = antenna_pb2.GetGantryStatusResponse()
        position = self._gantry_info_container.get_position(gantry_info)
        if position is not None:
            response.approximate_point.utm_x = position[0]
            response.approximate_point.utm_y = position[1]
        response.gantry_id = gantry_info.gantry_no
        #response.cps_guide_cm = gantry_info.cps_guide
        response.spreader_height_cm = int(gantry_info.h_pos / 10)
        response.spreader_locked = gantry_info.spr_state
        response.timestamp_ns = gantry_info.recv_timestamp
        response.spreader_size = gantry_info.spr_size
        response.spreader_horizonal_position_mm = gantry_info.t_pos
        response.block_nul = gantry_info.block_nul
        spreader_loc = self._gantry_info_container.get_spreader_location(gantry_info)
        if spreader_loc is not None:
            response.spreader_location.utm_x = spreader_loc[0]
            response.spreader_location.utm_y = spreader_loc[1]
        response.act_yard.extend(self._convert_to_act_yard(gantry_info.block_nul))
        response.smm1_pos = gantry_info.smm1_pos
        response.is_stable = gantry_info.is_stable
        response.g_pos = gantry_info.g_pos
        response.enable_ctrl = True if gantry_status and gantry_status.get('enable_ctrl',False) else False
        response.gantry_type = self.get_gantry_type(gantry_info.gantry_no)
        if manual_offset is not None:
            response.manual_offset = manual_offset
        response.rtg_o_tag = (gantry_info.rtg_o_tag == 1)
        return response

    def gantry_loop(self):
        start_time = time.time()
        gantries = self._gantry_info_container.get_all_gantry_info()
        for gantry in gantries:
            remote = self.get_remote_gantry_info(
                gantry,
                self._gantry_status.get(gantry.gantry_no),
                self._manual_offsets.get(f"gantry_{gantry.gantry_no}")
            )
            remote_cache.set_remote_gantry(remote.gantry_id,remote,2)
            vehicle_name = NameConverter().to_no(remote.abb_gantry_status.agv_id)
            if vehicle_name:
                commands = CommandCache().query_commands(vehicle_name)
                if len(commands) > 0 and remote.abb_gantry_status.in_position == 1:
                    info = TpToGantryInfo()
                    info.truck_no = vehicle_name
                    info.state = 1
                    info.timestamp = gantry.offset_timestamp
                    info.tos_command_id = commands[0].command.command_context.tos_command_id
                    ArriveCache().hset_tp_gantry_info(info)
        end_time = time.time()
        if end_time - start_time > 0.1:
            logger.warning(f"gantry_loop over cost:{end_time - start_time}")

    def gantry_update_status_loop(self):
        start_time = time.time()
        #心跳是否有效
        is_alive = plc_cache.is_store_gantry_server_alive()
        if self._last_gantry_store_server_alive != is_alive:
            if self._last_gantry_store_server_alive != None:
                logger.info(f"store_gantry_server_alive change from {self._last_gantry_store_server_alive} to {is_alive}")
            self._last_gantry_store_server_alive = is_alive
        #取在线的干预龙门吊
        online_gantry_nos = plc_cache.get_online_store_gantry_nos() if is_alive else []
        #龙门吊干预使能状态
        config_gantry_nos = self._config_cache.get_gantries_ctrl_enable_config() if is_alive else []
        #配置了使能且在线的才可以真正干预
        enable_gantry_nos = sorted(list(set(online_gantry_nos) & set(config_gantry_nos)))

        self._gantry_status = {} if len(enable_gantry_nos) == 0 else {gantry_no:{'enable_ctrl':is_alive} for gantry_no in enable_gantry_nos}
        if self._last_gantry_status != self._gantry_status:
            if self._last_gantry_status != None:
                logger.info(f"gantry store enable change from {self._last_gantry_status} to {self._gantry_status}")
            self._last_gantry_status = self._gantry_status
        end_time = time.time()
        if end_time - start_time > 0.1:
            logger.warning(f"gantry_update_status_loop over cost:{end_time - start_time}")

   
if __name__ == '__main__':
    # init_logger("test_remote_info")
    # PlcRemoteInfo().start()
    print(PlcRemoteInfo()._convert_to_act_yard(70))
