import copy
import time
import traceback
from common.logger import logger


def prase_tos_command(vehicle_name, tos_command: dict):
    size_map = {
        20: '22GP',
        40: '43GP',
        45: 'L2GP',
    }
    position_map = {
        'FRONT': 'F',
        'MID': 'M',
        'BEHIND': 'A',
        '': ''
    }
    wi = tos_command.get('wi_no', 0) if tos_command.get(
        'wi_no', 0) != 0 else (int(int(time.time() * 1000) % 1e12))
    ctn_no = tos_command.get('ctn', '0') if tos_command.get(
        'ctn', '0') != '0' else str(wi)
    twin_wi = tos_command.get('twin_wi_no', 0) if tos_command.get(
        'twin_wi_no', 0) != 0 else (wi + 1)
    twin_ctn_no = tos_command.get('twin_ctn', '0') if tos_command.get(
        'twin_ctn', '0') != '0' else str(twin_wi)
    dest_str = tos_command.get('destination')
    orientation = tos_command.get('orientation', 'R')
    lane = tos_command.get('lane', 3)
    lock_flag = tos_command.get('lock_flag', 'N')
    lock_pavilion = tos_command.get('lock_pavilion', '')
    command_type = tos_command.get('command_type', 'NONE')
    command_action = tos_command.get('command_action')
    device_disable = tos_command.get('device_disable', 0)
    vessel_pos = tos_command.get('vessel_pos', 'None')
    vessel_ref = tos_command.get('vessel_ref', 'None')
    vessel_class = tos_command.get('vessel_class', 'None')
    working_bay = tos_command.get('working_bay', 'None')
    mode = tos_command.get('mode', 0)
    cycle_mode = tos_command.get('cycle_mode', 'S')
    pow_name = tos_command.get('pow_name', 'None')
    if len(lock_pavilion) > 0 and lock_flag == 'N':
        lock_flag = 'U' if command_type == 'DSCH' else lock_flag
        lock_flag = 'A' if (
            command_type == 'LOAD' and command_action == 'UNLOAD') else lock_flag
        logger.info(f"vehicle_name:{vehicle_name},command_type:{command_type},command_action:{command_action}"
                    f",reset lock_flag:{lock_flag} for lock_pavilion:{lock_pavilion}")
    command_source = tos_command.get('command_source', 'TEST')
    wis = []
    if ',' in dest_str:
        container_size = tos_command.get('container_size') if tos_command.get(
            'container_size') in size_map else 20
        dests = dest_str.split(',')
        if vessel_pos != 'None':
            vessel_pos = vessel_pos.split(',')
            vessel_pos_0 = vessel_pos[0]
            vessel_pos_1 = vessel_pos[1]
        else:
            vessel_pos_0 = vessel_pos_1 = vessel_pos
        wi_one = dict()
        wi_one['container_size'] = size_map[container_size]
        wi_one['container_position'] = 'F'
        wi_one['command_type'] = command_type
        wi_one['command_action'] = command_action
        wi_one['destination'] = dests[0]
        wi_one['priority'] = int(tos_command.get('priority', '3'))
        wi_one['lane'] = lane
        wi_one['wi'] = wi
        wi_one['ctn'] = ctn_no
        wi_one['twin_wi'] = twin_wi
        wi_one['twin_ctn'] = twin_ctn_no
        wi_one['orientation'] = orientation
        wi_one['truck_no'] = vehicle_name
        wi_one['lock_flag'] = lock_flag
        wi_one['lock_pavilion'] = lock_pavilion
        wi_one['command_source'] = command_source
        wi_one['device_disable'] = device_disable
        wi_one['mode'] = mode
        wi_one['vessel_pos'] = vessel_pos_0
        wi_one['vessel_ref'] = vessel_ref
        wi_one['vessel_class'] = vessel_class
        wi_one['working_bay'] = working_bay
        wi_one['cycle_mode'] = cycle_mode
        wi_one['pow_name'] = pow_name
        wi_other = dict()
        wi_other = copy.deepcopy(wi_one)
        wi_other['container_position'] = 'A'
        wi_other['destination'] = dests[1]
        wi_other['wi'] = twin_wi
        wi_other['ctn'] = twin_ctn_no
        wi_other['twin_wi'] = wi
        wi_other['twin_ctn'] = ctn_no
        wi_other['vessel_pos'] = vessel_pos_1
        wis = [wi_one, wi_other]
    else:
        container_size = tos_command.get('container_size') if tos_command.get(
            'container_size') in size_map else 40
        wi_one = dict()
        wi_one['container_size'] = size_map[container_size]
        wi_one['container_position'] = position_map[tos_command.get(
            'container_position', '')]
        wi_one['command_type'] = command_type
        wi_one['command_action'] = command_action
        wi_one['destination'] = dest_str
        wi_one['priority'] = int(tos_command.get('priority', '3'))
        wi_one['lane'] = lane
        wi_one['wi'] = wi
        wi_one['ctn'] = ctn_no
        wi_one['twin_wi'] = tos_command.get('twin_wi_no', 0)
        wi_one['twin_ctn'] = tos_command.get('twin_ctn', 'None')
        wi_one['orientation'] = orientation
        wi_one['truck_no'] = vehicle_name
        wi_one['lock_flag'] = lock_flag
        wi_one['lock_pavilion'] = lock_pavilion
        wi_one['command_source'] = command_source
        wi_one['device_disable'] = device_disable
        wi_one['mode'] = mode
        wi_one['vessel_pos'] = vessel_pos
        wi_one['vessel_ref'] = vessel_ref
        wi_one['vessel_class'] = vessel_class
        wi_one['working_bay'] = working_bay
        wi_one['cycle_mode'] = cycle_mode
        wi_one['pow_name'] = pow_name
        wis = [wi_one]
    return wis


def create_insert_tos_command_handler(func, prase_func=prase_tos_command):
    def warp_func(vehicle_name, tos_command: list):
        wis = prase_func(vehicle_name=vehicle_name, tos_command=tos_command)
        for wi in wis:
            logger.info(f"[create_insert_tos_command_handler]:wi:{wi}")
            ret = func(container_size=wi.get('container_size'),
                       container_position=wi.get('container_position'),
                       command_type=wi.get('command_type'),
                       command_action=wi.get('command_action'),
                       destination=wi.get('destination'),
                       priority=wi.get('priority'),
                       lane=wi.get('lane'),
                       truck_no=wi.get('truck_no'),
                       wi=wi.get('wi', 125),
                       ctn=wi.get('ctn', 'CTN'),
                       twin_wi=wi.get('twin_wi', 0),
                       twin_ctn=wi.get('twin_ctn', 'None'),
                       orientation=wi.get('orientation', 'R'),
                       lock_flag=wi.get('lock_flag', 'N'),
                       lock_pavilion=wi.get('lock_pavilion', ''),
                       command_source=wi.get('command_source', 'TEST'),
                       device_disable=wi.get('device_disable', 0),
                       mode=wi.get('mode', 0),
                       vessel_pos=wi.get('vessel_pos', 'None'),
                       vessel_ref=wi.get('vessel_ref', 'None'),
                       vessel_class=wi.get('vessel_class', 'None'),
                       working_bay=wi.get('working_bay', 'None'),
                       cycle_mode=wi.get('cycle_mode', 'S'),
                       pow_name=wi.get('pow_name', 'None'))
            if not ret:
                logger.warning(f"fail to insert tos command")
                return False
        return True
    return warp_func
