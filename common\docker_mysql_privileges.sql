#mysql:5.7.37
#grant all on *.* to fabutech@'%' identified by 'fabu1#%' with grant option;
#mysql:8.0.21
drop user if exists fabutech@'localhost';
create user fabutech@'localhost' identified with mysql_native_password by 'fabu1#%';
grant all on *.* to fabutech@'localhost';
drop user if exists fabutech@'%';
create user fabutech@'%' identified with mysql_native_password by 'fabu1#%';
grant all on *.* to fabutech@'%';
flush privileges;