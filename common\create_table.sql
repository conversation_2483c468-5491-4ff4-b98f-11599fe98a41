CREATE TABLE T_CRANE_INFO_4V_DEBUG
(
  ID NUMBER PRIMARY KEY,
  CRANE_ID VARCHAR2(4),
  LANE_NO VARCHAR2(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  CYCLE_MODE VARCHAR(4),
  INSERT_TIME DATE,
  UPDATE_TIME DATE
);

CREATE TABLE T_CRANE_INFO_4V
(
  ID NUMBER PRIMARY KEY,
  CRANE_ID VARCHAR2(4),
  LANE_NO VARCHAR2(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  CYCLE_MODE VARCHAR(4),
  INSERT_TIME DATE,
  UPDATE_TIME DATE
);

CREATE TABLE T_WI_INFO_4V_TOS
(
  ID NUMBER PRIMARY KEY,
  TRUCK_NO VARCHAR2(12),
  TR<PERSON>K_SEQ NUMBER,
  WI_NO NUMBER,
  CTN_NO VARCHAR2(12),
  EQUIT_TYPE VARCHAR2(4),
  TEU NUMBER,
  FROM_POS VARCHAR2(12),
  TO_POS VARCHAR2(12),
  WI_TYPE VARCHAR2(4),
  WI_ACT VARCHAR2(12),
  WI_STATUS VARCHAR2(12),
  TWIN_FLAG VARCHAR2(1),
  TWIN_WI_NO NUMBER,
  TWIN_CTN_NO VARCHAR2(12),
  TRUCK_POS VARCHAR2(1),
  DISPATCH_TIME DATE,
  CANCEL_TIME DATE,
  CONFIRMED_TIME DATE,
  REMARK1 VARCHAR2(12),
  REMARK2 VARCHAR2(12),
  REMARK3 VARCHAR2(12),
  INSERT_TIME DATE,
  UPDATE_TIME DATE,
  VERSION NUMBER,
  LOCK_FLAG VARCHAR2(12),
  LOCK_PAVILION VARCHAR2(12),
  CTN_WEIGHT NUMBER,
  YC_ID VARCHAR2(12),
  YC_KIND NUMBER,
  SELECT_FLAG NUMBER,
  VESSEL_POS VARCHAR2(12),
  VESSEL_REF VARCHAR2(12),
  VESSEL_CLASS VARCHAR2(12)
);

CREATE TABLE T_WI_INFO_4V_DC
(
  ID NUMBER PRIMARY KEY,
  TRUCK_NO VARCHAR2(12),
  TRUCK_SEQ NUMBER,
  WI_NO NUMBER,
  CTN_NO VARCHAR2(12),
  FROM_POS VARCHAR2(12),
  TO_POS VARCHAR2(12),
  TOS_WI_NO NUMBER,
  TOS_WI_ACT VARCHAR2(12),
  TOS_WI_TYPE VARCHAR2(4),
  REMARK1 VARCHAR2(12),
  REMARK2 VARCHAR2(12),
  REMARK3 VARCHAR2(12),
  TRUCK_STATUS VARCHAR2(12),
  TWIN_FLAG VARCHAR2(1),
  TWIN_WI_NO NUMBER,
  TWIN_CTN_NO VARCHAR2(12),
  TRUCK_POS VARCHAR2(1),
  START_TIME DATE,
  EST_TIME DATE,
  ARRIVE_TIME DATE,
  FINISH_TIME DATE,
  INSERT_TIME DATE,
  UPDATE_TIME DATE,
  VERSION NUMBER,
  LOCK_FLAG VARCHAR2(12),
  LOCK_PAVILION VARCHAR2(12),
  ARRIVE_AREA VARCHAR2(24)
);


CREATE TABLE T_TRUCK_INFO_4V
(
  ID NUMBER PRIMARY KEY,
  TRUCK_NO VARCHAR2(12) NOT NULL,
  POS_X FLOAT,
  POS_Y FLOAT,
  CUR_POS VARCHAR2(12),
  POWER_TYPE VARCHAR2(4),
  REST_OIL FLOAT,
  REST_ELECTRIC FLOAT,
  SPEED FLOAT,
  SENSOR_STATUS VARCHAR2(1),
  INSERT_TIME DATE,
  UPDATE_TIME DATE,
  TRUCK_MODE NUMBER,
  CUR_WIS VARCHAR2(50),
  IS_SAFE_MODE VARCHAR2(4),
  IS_THREE_IN_ONE VARCHAR2(4)
);


CREATE SEQUENCE SEQ_T_WI_INFO_4V_DC
MINVALUE 1 
NOMAXVALUE 
INCREMENT BY 1 
START WITH 1 NOCACHE ;

CREATE SEQUENCE SEQ_WI_INFO_4V_TOS
MINVALUE 1 
NOMAXVALUE 
INCREMENT BY 1 
START WITH 1 NOCACHE ;

CREATE SEQUENCE SEQ_T_TRUCK_INFO_4V
MINVALUE 1 
NOMAXVALUE 
INCREMENT BY 1 
START WITH 1 NOCACHE;

CREATE SEQUENCE SEQ_T_CRANE_INFO_4V
MINVALUE 1 
NOMAXVALUE 
INCREMENT BY 1 
START WITH 1 NOCACHE;

CREATE OR REPLACE TRIGGER T_WI_INFO_4V_TRIGGER
BEFORE INSERT ON T_WI_INFO_4V_DC     
FOR EACH ROW WHEN(new.WI_NO IS NULL)
BEGIN
  select SEQ_T_WI_INFO_4V_DC.nextval into :new.WI_NO from dual;
END;
/


CREATE OR REPLACE TRIGGER T_TRUCK_INFO_4V_TRIGGER
BEFORE INSERT ON T_TRUCK_INFO_4V         
FOR EACH ROW WHEN(new.ID IS NULL)
BEGIN
  select SEQ_T_TRUCK_INFO_4V.nextval into :new.ID from dual;
END;
/

CREATE OR REPLACE TRIGGER T_CRANE_INFO_4V_DEBUG_TRIGGER
BEFORE INSERT ON T_CRANE_INFO_4V_DEBUG
FOR EACH ROW WHEN(new.ID IS NULL)
BEGIN
  select SEQ_T_CRANE_INFO_4V.nextval into :new.ID from dual;
END;
/

SELECT * FROM T_CRANE_INFO_4V_DEBUG;

INSERT INTO T_CRANE_INFO_4V_DEBUG(CRANE_ID, LANE_NO, VESSEL_DIRECTION, INSERT_TIME, UPDATE_TIME)
VALUES('CR34', '2', 'R', systimestamp, systimestamp);

SELECT * FROM T_CRANE_INFO_4V_DEBUG;

CREATE OR REPLACE TRIGGER T_CRANE_INFO_4V_TRIGGER
BEFORE INSERT ON T_CRANE_INFO_4V
FOR EACH ROW WHEN(new.ID IS NULL)
BEGIN
  select SEQ_T_CRANE_INFO_4V.nextval into :new.ID from dual;
END;
/

SELECT * FROM T_CRANE_INFO_4V;

INSERT INTO T_CRANE_INFO_4V(CRANE_ID, LANE_NO, VESSEL_DIRECTION, INSERT_TIME, UPDATE_TIME)
VALUES('CR34', '2', 'R', systimestamp, systimestamp);

SELECT * FROM T_CRANE_INFO_4V;

CREATE TABLE "DEVICE_BASE" (
  "GID" NUMBER(8,0) VISIBLE NOT NULL,
  "TYPE" NUMBER(1,0) VISIBLE,
  "CODE" VARCHAR2(20 BYTE) VISIBLE,
  "X" NUMBER VISIBLE,
  "Y" NUMBER VISIBLE,
  "LICENSEPLATE" VARCHAR2(20 BYTE) VISIBLE,
  "UPDATETIME" DATE VISIBLE,
  "REMARKS" VARCHAR2(40 BYTE) VISIBLE,
  "STATE" NUMBER(1,0) VISIBLE DEFAULT 1,
  "MAINTAIN_FLAG" NUMBER(1,0) VISIBLE,
  "REPAIR_STATUS" NUMBER(1,0) VISIBLE,
  "GPSTIME" DATE VISIBLE,
  "SPEED" NUMBER VISIBLE
);
INSERT INTO "DEVICE_BASE" VALUES ('596', '1', 'QC45', '122.01944', '29.782732', 'A66664', to_date('0004-11-20 00:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '1', '1', '1', '1', to_date('0003-11-20 00:00:00', 'SYYYY-MM-DD HH24:MI:SS'), '0.3');
COMMIT;
COMMIT;