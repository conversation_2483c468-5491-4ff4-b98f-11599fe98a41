#!/bin/bash
addgroup --gid "$DOCKER_GRP_ID" "$DOCKER_GRP"
adduser --disabled-password --gecos '' "$DOCKER_USER" \
    --uid "$DOCKER_USER_ID" --gid "$DOCKER_GRP_ID" 2>/dev/null
usermod -aG sudo,video,audio "$DOCKER_USER"
echo '%sudo ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers
sudo rsync -lrKog --chown=${DOCKER_USER_ID}:${DOCKER_GRP_ID} /etc/skel/. /home/<USER>
sed -i "s/^resize /#resize /g" "/home/<USER>/.zshrc"
sed -i "s/en_US.utf8/C.UTF-8/g" "/home/<USER>/.zshrc" "/home/<USER>/.bashrc"
rm /home/<USER>/.tmux.conf
chsh -s /usr/bin/zsh $DOCKER_USER
if [ -z "$LOCAL_REDIS_PORT" ]; then
LOCAL_REDIS_PORT=6379
fi
if [ -n "$REDIS_PASSWORD" ]; then
  nohup redis-server --port $LOCAL_REDIS_PORT --protected-mode no --requirepass "$REDIS_PASSWORD" &
else
  nohup redis-server --port $LOCAL_REDIS_PORT --protected-mode no &
fi
AutoStarBash="/antenna-server/script/auto_start.sh"
if [ -f "$AutoStarBash" ] ;then
echo "docker restart" > docker.log
su $DOCKER_USER -c "nohup bash $AutoStarBash &"
fi
sudo -EHu ${DOCKER_USER} "$@"
