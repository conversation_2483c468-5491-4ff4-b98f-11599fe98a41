import math

from cache.client import <PERSON><PERSON><PERSON><PERSON>
from cache.config_cache import ConfigCache
from common.common_util import is_in_polygon
from common.constant import COMMAND_EXPIRE_TIME
from common.constant import COMMON_REDIS_EXPIRE_TIME
from common.logger import logger
from common.singleton import Singleton
from model.hdmap_client import Hd<PERSON><PERSON><PERSON>lient
from model.wechat_message import WeChatMessage
from proto import cache_pb2
from typing import Optional, List

LOCK_PAVILION_PREFIX = 'lock-pavilion'
CTN_NO_LOCK_FLAG = 'ctn-no-lock-flag'
WI_NO_LOCK_FLAG = 'wi_no_lock_flag'
CTN_LOCK_IS_EXIST_PREFIX = 'CTN_LOCK_IS_EXIST_PREFIX'
NEED_PARSE_LOCK_COMMAND_BUSINESS_SCENES = 'NEED_PARSE_LOCK_COMMAND_BUSINESS_SCENES'  # 支持集中装卸锁的车端场景
AVAILABLE_PAVILIONS = 'AVAILABLE_PAVILIONS'  # 作业可用锁亭
LAST_MODE_SEND_TO_LOCK_STATION_KEY = "last-mode-send-to-lock-station-key"
LAST_STATE_FLOW_SEND_TO_LOCK_STATION_KEY = "last-state-flow-send-to-lock-station-key"
LOCK_PAVILION_VEHICLE_SET_KEY = "lock-pavilion-vehicle-set"
MS_ONLINE_LOCK_NAME_SET_KEY = "online-lock-name-set" # 所有的梅山在线锁亭, 云控设置
MS_LOCK_PAVILION_STATUS_KEY = "lock_pavilion_status_" # 梅山锁亭配置, 云控设置
LOCK_SETTING_RECV_MSG_QUEUE_PREFIX = 'lock_setting_recv_msg-queue' # 接收锁站系统修改锁站相关配置消息 回执
YZ_LOCK_PAVILION_REVERSE_INFO_KEY = "yz_lock_pavilion_reverse_info" # 甬舟锁站是否为潮汐
MS_PASSBY_MANUAL_LOCK_INFO_KEY = "passby-manual-lock-info-set" # 梅山途径点调度开启的锁站

class LockCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._need_lock_flag_list = ['A', 'B', 'O', 'I', 'X']
        self._init_lock_station_fence_map = False
        self._lock_station_fence_map_igv = {}  # key: 锁亭名称, value: 矩形区域范围 (甬州igv)
        self._lock_station_fence_map_at = {}  # key: 锁亭名称, value: 矩形区域范围 (甬州at)
        """
            因为引桥有潮汐车道，所以一个引桥的4、5号锁亭既可能上也可能下，关键在于使用时是卸锁指令还是装锁指令
            将 map value 中第一个当作上引桥的作业点，第二个当作下引桥的作业点
        """
        self._lock_station_point_map = {"8Y1": [(404741.58400, 3295191.97300), ()],
                                        "8Y2": [(404809.06400, 3295119.04100), ()],
                                        "9Y1": [(405001.17700, 3295437.39200), ()],
                                        "9Y2": [(405067.34200, 3295365.82200), ()],
                                        "9Y3": [(), (405070.32900, 3295383.37000)],
                                        "10Y1": [(405321.41, 3295616.54), (405313.92, 3295624.38)],
                                        "10Y2": [(405325.26, 3295620.10), (405317.62, 3295628.07)],
                                        "10Y3": [(405274.91, 3295680.52), (405266.23, 3295689.52)],
                                        "10Y4": [(405278.33, 3295684.42), (405269.60, 3295693.61)],
                                        "11Y1": [(405576.68, 3295867.56), (405568.98, 3295875.51)],
                                        "11Y2": [(405580.49, 3295870.92), (405572.88, 3295879.14)],
                                        "11Y3": [(405533.44, 3295928.13), (405525.37, 3295936.40)],
                                        "11Y4": [(405536.97, 3295931.87), (405528.76, 3295940.47)],
                                        "12Y1": [(405836.88421735103, 3296110.215287709), (405829.88970451115, 3296117.754254011)],
                                        "12Y2": [(405840.68726829241, 3296113.6227911715), (405833.64396068593, 3296121.3462143452)],
                                        "12Y4": [(405794.80669130775, 3296177.3743318482), (405786.07422855613, 3296186.6230212231)]}

        # heading记录的所在车道往泊位方向的弧度
        self._lock_station_heading_map = {
                                        "8Y1": -0.823,
                                        "8Y2": -0.825,
                                        "9Y1": -0.824,
                                        "9Y2": -0.821,
                                        "9Y3": -0.821,
                                        "10Y1": -0.825,
                                        "10Y2": -0.825,
                                        "10Y3": -0.824,
                                        "10Y4": -0.824,
                                        "11Y1": -0.820,
                                        "11Y2": -0.820,
                                        "11Y3": -0.824,
                                        "11Y4": -0.824,
                                        "12Y1": -0.826,
                                        "12Y2": -0.826,
                                        "12Y4": -0.824,
                                    }

        self._yz_lock_station_point_map_igv = {'PSTP_601': [(388841.06143189035, 3316861.6286159717), (388841.06143189035, 3316861.6286159717)],
                                               'PSTP_602': [(388906.14233290765, 3316885.8233310105), (388906.14233290765, 3316885.8233310105)],
                                               'PSTP_102': [(388076.82382551976, 3318366.528596906), (388076.82382551976, 3318366.528596906)],
                                               'PSTP_501': [(388673.45116325415, 3317164.1929805735), (388673.45116325415, 3317164.1929805735)],
                                               'PSTP_502': [(388657.694833048, 3317149.3414623206), (388657.694833048, 3317149.3414623206)],
                                               'PSTP_503': [(388756.73272783903, 3317197.373421036), (388756.73272783903, 3317197.373421036)],
                                               'PSTP_504': [(388741.02743764373, 3317182.433023131), (388741.02743764373, 3317182.433023131)],
                                               'PSTP_301': [(388345.73565133876, 3317766.3818271914), (388345.73565133876, 3317766.3818271914)],
                                               'PSTP_302': [(388327.32764134044, 3317749.9936262826), (388327.32764134044, 3317749.9936262826)],
                                               'PSTP_304': [(388403.13447282667, 3317778.662731576), (388403.13447282667, 3317778.662731576)],
                                               'PSTP_303': [(388421.45276858174, 3317795.064995664), (388421.45276858174, 3317795.064995664)],
                                               'PSTP_201': [(388173.01041044143, 3318062.4557850277), (388173.01041044143, 3318062.4557850277)],
                                               'PSTP_202': [(388157.06935186096, 3318047.43850251), (388157.06935186096, 3318047.43850251)],
                                               'PSTP_203': [(388257.61867570586, 3318096.306968945), (388257.61867570586, 3318096.306968945)],
                                               'PSTP_204': [(388241.89135583356, 3318081.3702072697), (388241.89135583356, 3318081.3702072697)],
                                               'PSTP_101': [(388021.8521958887, 3318347.877903435), (388021.8521958887, 3318347.877903435)],
                                               'PSTP_401': [(388505.8861200105, 3317463.2625863017), (388505.8861200105, 3317463.2625863017)],
                                               'PSTP_402': [(388490.3802856851, 3317448.478105113), (388490.3802856851, 3317448.478105113)],
                                               'PSTP_403': [(388590.85481641215, 3317497.2089150213), (388590.85481641215, 3317497.2089150213)],
                                               'PSTP_404': [(388575.6618813563, 3317482.6112206653), (388575.6618813563, 3317482.6112206653)]}

        self._yz_lock_station_point_map_at = {'PSTP_601': [(388836.1089431784, 3316858.874029876), (388846.01392060233, 3316864.383202067)],
                                              'PSTP_602': [(388901.1898441957, 3316883.068744915), (388911.0948216196, 3316888.577917106)],
                                              'PSTP_102': [(388071.8713368078, 3318363.7740108105), (388081.77631423174, 3318369.2831830014)],
                                              'PSTP_501': [(388668.4986745422, 3317161.438394478), (388678.40365196613, 3317166.947566669)],
                                              'PSTP_502': [(388652.742344336, 3317146.586876225), (388662.64732175996, 3317152.096048416)],
                                              'PSTP_503': [(388751.78023912705, 3317194.6188349407), (388761.685216551, 3317200.1280071316)],
                                              'PSTP_504': [(388736.07494893175, 3317179.6784370355), (388745.9799263557, 3317185.1876092264)],
                                              'PSTP_301': [(388340.7831626268, 3317763.627241096), (388350.68814005074, 3317769.136413287)],
                                              'PSTP_302': [(388322.37515262846, 3317747.239040187), (388332.2801300524, 3317752.748212378)],
                                              'PSTP_304': [(388398.1819841147, 3317775.9081454803), (388408.08696153865, 3317781.4173176712)],
                                              'PSTP_303': [(388416.50027986977, 3317792.3104095687), (388426.4052572937, 3317797.8195817596)],
                                              'PSTP_201': [(388168.05792172946, 3318059.7011989322), (388177.9628991534, 3318065.210371123)],
                                              'PSTP_202': [(388152.116863149, 3318044.6839164146), (388162.02184057294, 3318050.1930886055)],
                                              'PSTP_203': [(388252.6661869939, 3318093.5523828496), (388262.57116441784, 3318099.0615550405)],
                                              'PSTP_204': [(388236.9388671216, 3318078.615621174), (388246.84384454554, 3318084.124793365)],
                                              'PSTP_101': [(388016.8997071767, 3318345.1233173395), (388026.8046846007, 3318350.6324895304)],
                                              'PSTP_401': [(388500.93363129854, 3317460.508000206), (388510.8386087225, 3317466.017172397)],
                                              'PSTP_402': [(388485.42779697315, 3317445.7235190175), (388495.3327743971, 3317451.2326912084)],
                                              'PSTP_403': [(388585.90232770017, 3317494.454328926), (388595.8073051241, 3317499.9635011167)],
                                              'PSTP_404': [(388570.70939264435, 3317479.85663457), (388580.6143700683, 3317485.3658067607)]}

        self._yz_lock_station_point_map = {
            1: self._yz_lock_station_point_map_at,
            2: self._yz_lock_station_point_map_igv
        }

    def get_lock_station_point(self, lock_station, mode):
        offset_x, offset_y = 0, 0
        try:
            lock_offset_config = ConfigCache().get_lock_offset_config(lock_station)
            if lock_offset_config:
                lock_heading = self._lock_station_heading_map.get(lock_station)
                if lock_heading is not None:
                    offset_x = lock_offset_config[mode] * math.cos(lock_heading)
                    offset_y = lock_offset_config[mode] * math.sin(lock_heading)
        except Exception as ex:
            logger.error(f"get_lock_station offset error: {ex}")
        try:
            lock_point_x = self._lock_station_point_map[lock_station][mode][0] + offset_x
            lock_point_y = self._lock_station_point_map[lock_station][mode][1] + offset_y
            return lock_point_x, lock_point_y
        except Exception as e:
            return ()

    def need_lock(self, lock_flag):
        return lock_flag in self._need_lock_flag_list

    def need_unlock(self, lock_flag):
        return lock_flag == 'U'

    def set_available_pavilions(self, pavilion_list):
        self._redis.delete(AVAILABLE_PAVILIONS)
        for pavilion in pavilion_list:
            self._redis.sadd(AVAILABLE_PAVILIONS, pavilion)

    def get_available_pavilions(self):
        pavilion_list = []
        for member in self._redis.smembers(AVAILABLE_PAVILIONS):
            pavilion_list.append(member.decode())
        if 0 == len(pavilion_list):
            pavilion_list = ["8Y1", "8Y2", "8Y3", "8Y4", "9Y1", "9Y2", "9Y3", "9Y4", "10Y8", "10Y9", "11Y1", "11Y2",
                             "11Y3", "11Y4"]
            WeChatMessage().send_message_to_sim('Reis内可选择锁亭为空，启用默认锁亭')
        return pavilion_list

    def set_lock_business_scene(self, business_scene_list):
        self._redis.delete(NEED_PARSE_LOCK_COMMAND_BUSINESS_SCENES)
        for business_scene in business_scene_list:
            self._redis.sadd(NEED_PARSE_LOCK_COMMAND_BUSINESS_SCENES, business_scene)

    def get_lock_business_scene(self):
        scene_list = ["port_meishan_driverless"]
        for member in self._redis.smembers(NEED_PARSE_LOCK_COMMAND_BUSINESS_SCENES):
            scene_list.append(member.decode())
        return scene_list

    def judge_lock_business_scene(self, business_scene):
        if business_scene in self.get_lock_business_scene():
            return True
        return False

    def set_lock_pavilion(self, lock_pavilion_no, lock_pavilion_pos):
        key = f"{CTN_NO_LOCK_FLAG}:{lock_pavilion_no}"
        return self._redis.set(key, lock_pavilion_pos, nx=True)

    def get_lock_pavilion(self, lock_pavilion_no):
        key = f"{CTN_NO_LOCK_FLAG}:{lock_pavilion_no}"
        if self._redis.get(key) is not None:
            return self._redis.get(key).decode()
        else:
            return (0, 0)

    # key exit, not update
    def set_have_passed_pavilion(self, wi_no, have_passed_pavilion):
        key = f"{WI_NO_LOCK_FLAG}:{wi_no}"
        return self._redis.set(key, have_passed_pavilion, ex=COMMON_REDIS_EXPIRE_TIME, nx=True)

    # key not exit, set
    def update_have_passed_pavilion(self, wi_no, have_passed_pavilion):
        key = f"{WI_NO_LOCK_FLAG}:{wi_no}"
        return self._redis.set(key, have_passed_pavilion, ex=COMMON_REDIS_EXPIRE_TIME, xx=False)

    def get_have_passed_pavilion(self, wi_no):
        key = f"{WI_NO_LOCK_FLAG}:{wi_no}"
        value = self._redis.get(key)
        if value is not None:
            return value.decode()
        else:
            return 'UNKNOWN'

    # CTN_LOCK_IS_EXIST_PREFIX 代表锁标志位，True为带锁，False为不带锁

    # 设置指定id带锁状态，只新增，key存在不修改value
    def set_container_lock_is_exist(self, lock_container_id, container_lock_is_exist):
        key = f"{CTN_LOCK_IS_EXIST_PREFIX}:{lock_container_id}"
        return self._redis.set(key, container_lock_is_exist, ex=COMMON_REDIS_EXPIRE_TIME, nx=True)

    # 更新指定id带锁状态，key不存在则新增
    def update_container_lock_is_exist(self, lock_container_id, container_lock_is_exist):
        key = f"{CTN_LOCK_IS_EXIST_PREFIX}:{lock_container_id}"
        logger.debug(
            f"[get_container_lock_is_exist] lock_container_id: {lock_container_id}, is_exist: {container_lock_is_exist}")
        return self._redis.set(key, container_lock_is_exist, ex=COMMON_REDIS_EXPIRE_TIME, xx=False)

    # 查询指定id带锁状态，若key不存在返回UNKNOWN
    def get_container_lock_is_exist(self, lock_container_id):
        key = f"{CTN_LOCK_IS_EXIST_PREFIX}:{lock_container_id}"
        value = self._redis.get(key)
        logger.debug(f"[get_container_lock_is_exist] lock_container_id: {lock_container_id}, is_exist: {value}")
        if value is not None:
            return value.decode()
        else:
            return 'UNKNOWN'

    def init_yongzhou_lock_station_fence(self):
        lock_station_names = [
            'PSTP_101', 'PSTP_102',
            'PSTP_201', 'PSTP_202', 'PSTP_203', 'PSTP_204',
            'PSTP_301', 'PSTP_302', 'PSTP_303', 'PSTP_304',
            'PSTP_401', 'PSTP_402', 'PSTP_403', 'PSTP_404',
            'PSTP_501', 'PSTP_502', 'PSTP_503', 'PSTP_504',
            'PSTP_601', 'PSTP_602',
        ]
        # 集卡
        self._lock_station_fence_map_at = HdmapClient().get_electronic_fence(lock_station_names, forward_extend_dist = 1, backward_extend_dist = 5)
        # IGV
        self._lock_station_fence_map_igv = HdmapClient().get_electronic_fence(lock_station_names, forward_extend_dist = -8, backward_extend_dist = 9)
        return

    def is_in_lock_station_fence(self, lock_station_name, localization, truck_type, is_reverse):
        if not self._init_lock_station_fence_map:
            self.init_yongzhou_lock_station_fence()
            self._init_lock_station_fence_map = True
        lock_station_fence_map = self.get_lock_station_fence_map(truck_type)
        if lock_station_name not in lock_station_fence_map:
            logger.error(f"{lock_station_name} not in lock_station_fence_map")
            return False
        fences = lock_station_fence_map[lock_station_name]
        fence = fences[1] if is_reverse else fences[0]
        return is_in_polygon(fence, localization)

    # 改用get_lock_station_name_in_fence
    # def is_in_any_lock_station_fence(self, localization, truck_type):
    #     if not self._init_lock_station_fence_map:
    #         self.init_yongzhou_lock_station_fence()
    #         self._init_lock_station_fence_map = True
    #     for name, fences in self.get_lock_station_fence_map(truck_type).items():
    #         lock_station_info = self.hget_yz_lock_station_info(name)
    #         fence = fences[1] if lock_station_info and lock_station_info.is_reverse else fences[0]
    #         if is_in_polygon(fence, localization):
    #             return True
    #     return False

    def get_lock_station_name_in_fence(self, localization, truck_type):
        if not self._init_lock_station_fence_map:
            self.init_yongzhou_lock_station_fence()
            self._init_lock_station_fence_map = True
        all_lock_station_infos = self.hgetall_yz_lock_station_infos()
        for name, fences in self.get_lock_station_fence_map(truck_type).items():
            lock_station_info = all_lock_station_infos.get(name)
            fence = fences[1] if lock_station_info and lock_station_info.is_reverse else fences[0]
            if is_in_polygon(fence, localization):
                return name
        return None

    def get_lock_station_fence_map(self, truck_type):
        lock_station_fence_map = {}
        if truck_type == 1:
            lock_station_fence_map = self._lock_station_fence_map_at
        elif truck_type == 2:
            lock_station_fence_map = self._lock_station_fence_map_igv
        return lock_station_fence_map

    def in_which_lock_station(self, localization, truck_type):
        if not self._init_lock_station_fence_map:
            self.init_yongzhou_lock_station_fence()
            self._init_lock_station_fence_map = True
        for name, fence in self.get_lock_station_fence_map(truck_type).items():
            if is_in_polygon(fence, localization):
                return name
        return ''

    def set_last_mode_send_to_lock_station(self, vehicle_name, mode):
        logger.debug(f"set_last_mode_send_to_lock_station, vehicle_name:{vehicle_name}")
        key = f"{LAST_MODE_SEND_TO_LOCK_STATION_KEY}:{vehicle_name}"
        return self._redis.set(key, str(mode), ex=COMMAND_EXPIRE_TIME)

    def del_last_mode_send_to_lock_station(self, vehicle_name):
        logger.debug(f"del_last_mode_send_to_lock_station, vehicle_name:{vehicle_name}")
        key = f"{LAST_MODE_SEND_TO_LOCK_STATION_KEY}:{vehicle_name}"
        return self._redis.delete(key)

    def get_last_mode_send_to_lock_station(self, vehicle_name) -> bool:
        key = f"{LAST_MODE_SEND_TO_LOCK_STATION_KEY}:{vehicle_name}"
        value = self._redis.get(key)
        ret = False
        if value is not None:
            ret = (value.decode() == 'True')
        return ret
    
    def set_last_state_flow_send_to_lock_station(self, vehicle_name, state_flow):
        logger.debug(f"set_last_state_flow_send_to_lock_station, vehicle_name:{vehicle_name}, state_flow:{state_flow}")
        key = LAST_STATE_FLOW_SEND_TO_LOCK_STATION_KEY
        return self._redis.set_field(key, vehicle_name, state_flow)
    
    def get_last_state_flow_send_to_lock_station(self, vehicle_name) -> str:
        key = LAST_STATE_FLOW_SEND_TO_LOCK_STATION_KEY
        return self._redis.get_field(key, vehicle_name)
    
    def set_all_last_state_flow_send_to_lock_station(self, state_flow_dict):
        key = LAST_STATE_FLOW_SEND_TO_LOCK_STATION_KEY
        return self._redis.hmset(key, state_flow_dict)

    def get_all_last_state_flow_send_to_lock_station(self):
        key = LAST_STATE_FLOW_SEND_TO_LOCK_STATION_KEY
        state_flow_dict = self._redis.get_dict(key)
        state_flow_dict = {k.decode(): v.decode() for k, v in state_flow_dict.items()}
        return state_flow_dict

    def push_lock_setting_recv_msg(self, msg):
        key = f"{LOCK_SETTING_RECV_MSG_QUEUE_PREFIX}"
        return self._redis.rpush_proto(key, msg)

    def clear_lock_setting_recv_msg(self,message_len=None):
        key = f"{LOCK_SETTING_RECV_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_lock_setting_recv_msg(self,auto_delete=True):
        key = f"{LOCK_SETTING_RECV_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange_proto(key, 0, -1, cache_pb2.LockSettingReceiveMsg)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def get_ms_online_lock_names(self):
        """
            查询所有在线锁站
        """
        set = []
        for member in self._redis.smembers(MS_ONLINE_LOCK_NAME_SET_KEY):
            set.append(member.decode())
        return set

    def get_ms_lock_status(self, lock_name):
        """
            查询指定锁站云控配置
        """
        key = f"{MS_LOCK_PAVILION_STATUS_KEY}{lock_name}"
        return self._redis.get_proto(key, cache_pb2.LockPavilion)

    def is_lock_station_emergency_brake_enable(self, lock_name):
        """
            查询指定锁站是否配置急刹
        """
        status = self.get_ms_lock_status(lock_name)
        if status is not None:
            return status.emergency_brake_enable
        return False

    def get_yz_lock_station_poi_igv(self):
        """
        处理电子围栏数据并计算中心点
        """
        lock_station_names = [
            'PSTP_101', 'PSTP_102',
            'PSTP_201', 'PSTP_202', 'PSTP_203', 'PSTP_204',
            'PSTP_301', 'PSTP_302', 'PSTP_303', 'PSTP_304',
            'PSTP_401', 'PSTP_402', 'PSTP_403', 'PSTP_404',
            'PSTP_501', 'PSTP_502', 'PSTP_503', 'PSTP_504',
            'PSTP_601', 'PSTP_602',
        ]
        # 获取电子围栏数据
        fence_data = HdmapClient().get_electronic_fence(lock_station_names, forward_extend_dist=0,backward_extend_dist=0)
        # 计算中心点
        centroids = {}
        for station, coordinates in fence_data.items():
            x_coords = [coord[0] for coord in coordinates]
            y_coords = [coord[1] for coord in coordinates]
            centroid_x = sum(x_coords) / len(x_coords)
            centroid_y = sum(y_coords) / len(y_coords)
            centroids[station] = (centroid_x, centroid_y)
        return centroids

    def get_yz_lock_station_poi_at(self, work_type):
        """
        AT 车型获取偏移后的点位信息
        work_type: L 驶向岸桥 U 驶向堆场
        """
        centroids = self.get_yz_lock_station_poi_igv()
        # 上引桥方向 heading = -2.634，下引桥相反
        adjacent_side_x = math.cos(-2.634) * 5.667
        adjacent_side_y = math.sin(-2.634) * 5.667
        # 遍历 centroids
        shifted_centroids = {}
        for station, (centroid_x, centroid_y) in centroids.items():
            # 将邻边值加到每个中心点坐标上
            if work_type == 'L':
                shifted_centroids[station] = (centroid_x + adjacent_side_x, centroid_y + adjacent_side_y)
            elif work_type == 'U':
                shifted_centroids[station] = (centroid_x - adjacent_side_x, centroid_y - adjacent_side_y)
            else:
                return "UNKNOWN"
        return shifted_centroids

    def hset_yz_lock_station_info(self, info: cache_pb2.LockStationReverseInfo):
        name = f"{YZ_LOCK_PAVILION_REVERSE_INFO_KEY}"
        key =  f"{info.lock_station_name}"
        return self._redis.hset_proto(name, key, info)

    def hget_yz_lock_station_info(self, lock_station_name: str) -> Optional[cache_pb2.LockStationReverseInfo]:
        name = f"{YZ_LOCK_PAVILION_REVERSE_INFO_KEY}"
        key = f"{lock_station_name}"
        return self._redis.hget_proto(name, key, cache_pb2.LockStationReverseInfo)
    
    def hgetall_yz_lock_station_infos(self):
        name = f"{YZ_LOCK_PAVILION_REVERSE_INFO_KEY}"
        infos = self._redis.hgetall_proto(name, cache_pb2.LockStationReverseInfo)
        return {info.lock_station_name: info for info in infos}

    def get_ms_passby_lock_info(self):
        """
            查询梅山途径点调度开启的锁站
        """
        value_map = self._redis.hgetall(MS_PASSBY_MANUAL_LOCK_INFO_KEY)
        ret_dict = dict()
        for k, v in value_map.items():
            ret_dict[k.decode()] = v.decode()
        return ret_dict

if __name__ == '__main__':
    # print(LockCache().get_container_lock_is_exist('1653985199'))

    # # 设置集中装卸锁场景
    # business_list = ["port_meishan_driverless"]
    # print(LockCache().set_lock_business_scene(business_list))
    # print(LockCache().get_lock_business_scene())
    # print(LockCache().judge_lock_business_scene("port_meishan_driverless"))
    #
    # # 设置作业可用锁亭
    # pavilion_list = ["8Y1", "8Y2", "8Y3", "8Y4", "9Y1", "9Y2", "9Y3", "9Y4", "10Y8", "10Y9", "11Y1", "11Y2", "11Y3", "11Y4"]
    # print(LockCache().set_available_pavilions(pavilion_list))
    # print(LockCache().get_available_pavilions())

    # LockCache().init_yongzhou_lock_station_fence()
    # # print(LockCache().is_in_any_lock_station_fence((388832.3629033452, 3316853.413499453)))
    # # print(LockCache().is_in_any_lock_station_fence((388830.3629033452, 3316853.413499453)))
    # # print(LockCache().is_in_any_lock_station_fence((388831.3629033452, 3316850.413499453)))

    # """
    # LockCache().set_last_mode_send_to_lock_station('AT801', True)
    # print(LockCache().get_last_mode_send_to_lock_station('AT801'))
    # LockCache().set_last_mode_send_to_lock_station('AT801', False)
    # print(LockCache().get_last_mode_send_to_lock_station('AT801'))
    # LockCache().del_last_mode_send_to_lock_station('AT801')
    # print(LockCache().get_last_mode_send_to_lock_station('AT801'))
    # """
    # print(LockCache().get_lock_station_point('10Y8', 1))

    print(LockCache().get_ms_online_lock_names())
    print(LockCache().get_ms_lock_status("10Y1"))
    print(LockCache().is_lock_station_emergency_brake_enable("10Y1"))
