import json
from datetime import datetime

from cache import plc_cache
from cache.map_cache import MapCache
from common.logger import logger
from proto.cache_pb2 import GPSInfo, FenceInfo, GantryGPSInfo
from google.protobuf import json_format
from pyproj import Transformer

def vms_gps_info_handler(msg):
    infos = json.loads(msg.decode())
    gps_info = GPSInfo()
    json_format.ParseDict(infos, gps_info, ignore_unknown_fields = True)
    MapCache().hset_ms_gps_info(gps_info)
    # 如果不是龙门吊数据直接返回
    if not gps_info.deviceNo.startswith("RT"):
        return
    plc_info = plc_cache.get_gantry(gps_info.deviceNo[2:])
    # 如果没有plc数据，或者轮胎转向限位不是转道，删除缓存 直接返回
    if not plc_info or plc_info.rtg_o_tag != 0:
        MapCache().hdel_ms_gantry_gps_info(gps_info.deviceNo)
        return
    gantry_gps_info = GantryGPSInfo()
    utc_time = datetime.strptime(gps_info.utcTime, "%Y-%m-%d %H:%M:%S")
    # 定义WGS84坐标系和目标UTM坐标系（122°E 属于UTM 51N区）
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:32651", always_xy=True)
    utm_x, utm_y = transformer.transform(gps_info.longitude, gps_info.latitude)
    gantry_gps_info.deviceNo = gps_info.deviceNo
    gantry_gps_info.gantry_point_x = utm_x
    gantry_gps_info.gantry_point_y = utm_y
    gantry_gps_info.recv_timestamp = int(utc_time.timestamp() * 1000)
    MapCache().hset_ms_gantry_gps_info(gantry_gps_info)
    logger.debug(f"ms_gantry_gps_info: {gantry_gps_info}")
    return