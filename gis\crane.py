import time

from common.singleton import Singleton
from common.periodical_task import PeriodicalTask
from gis.utils import to_utm
from model.device_base import get_all_crane


class CraneGisInfo(metaclass=Singleton):
    def __init__(self):
        self._g_pos_dict = dict()
        self._running = False
        self._loop_thread = PeriodicalTask(target=self.loop, interval=1)

    def get_g_pos(self, crane_no):
        if crane_no in self._g_pos_dict:
            return self._g_pos_dict[crane_no][0], self._g_pos_dict[crane_no][1]
        return -1, -1

    def loop(self):
        cranes = get_all_crane()
        crane_dict = dict()
        for c in cranes:
            crane_id = int(c.code.lstrip('QC'))
            x, y = to_utm(c.x, c.y)
            crane_dict[crane_id] = (x, y)
        self._g_pos_dict = crane_dict

    def start(self):
        if not self._running:
            self._loop_thread.start()
            self._running = True


if __name__ == '__main__':
    CraneGisInfo().start()
    while True:
        print(CraneGisInfo().get_g_pos(45))
        time.sleep(1)
