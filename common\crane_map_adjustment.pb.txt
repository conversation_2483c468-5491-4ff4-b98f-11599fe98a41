approximate_point_offset_from: -22.4537481191
approximate_point_offset_to: 4.34625188093
approximate_point_theta: 0.75375700539180079
config_area { # bridge 7 -> dock 6 A
  description: "bridge_7_to_dock_6"
  critical_area_from {
    utm_x: 404759.04009942006  # AF
    utm_y: 3294629.673020005
  }
  critical_area_to {
    utm_x: 404772.17839680833  # AT
    utm_y: 3294642.2450056244
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {               # A1F
        utm_x: 404737.9120422932
        utm_y: 3294671.9543913077
      }
      to_point {
        utm_x: 404748.7835310879  # A1T
        utm_y: 3294620.1803391753
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404737.9120422932  # A2F=A1F
        utm_y: 3294671.9543913077 
      }
      to_point {
        utm_x: 404759.4260404763  # A2T
        utm_y: 3294611.148621388
      }
    }
  }
}
config_area { # dock 6 -> bridge 7 B
  description: "dock_6_to_bridge_7"
  critical_area_from {
    utm_x: 404761.69723362784  # BF
    utm_y: 3294635.424055355
  }
  critical_area_to {
    utm_x: 404779.08313173935  # BT
    utm_y: 3294652.6307122544
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404748.7835310879  # B1F=A1T
        utm_y: 3294620.1803391753
      }
      to_point {
        utm_x: 404740.5473511987  # B1T
        utm_y: 3294686.68556972
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404759.4260404763  # B2F=A2T
        utm_y: 3294611.148621388
      }
      to_point {
        utm_x: 404740.5473511987  # B2T=B1T
        utm_y: 3294686.68556972
      }
    }
  }
}
config_area { # bridge 7 -> dock 7 C
  description: "bridge_7_to_dock_7"
  critical_area_from {
    utm_x: 404761.69723362784  # CF=BF
    utm_y: 3294635.424055355
  }
  critical_area_to {
    utm_x: 404779.08313173935  # CT=BT
    utm_y: 3294652.6307122544
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404737.9120422932  # C1F=A1F
        utm_y: 3294671.9543913077
      }
      to_point {
        utm_x: 404801.12917482154  # C1T
        utm_y: 3294668.7619579295
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404737.9120422932  # C2F=C1F=A1F
        utm_y: 3294671.9543913077
      }
      to_point {
        utm_x: 404816.18500939116  # C2T
        utm_y: 3294663.2056394466
      }
    }
  }
}
config_area { # dock 7 -> bridge 7 D
  description: "dock_7_to_bridge_7"
  critical_area_from {
    utm_x: 404772.17839680833  # DF=AT
    utm_y: 3294642.2450056244
  }
  critical_area_to {
    utm_x: 404784.4602148565  # DT
    utm_y: 3294656.2154331114
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404801.12917482154  # D1F=C1T
        utm_y: 3294668.7619579295
      }
      to_point {
        utm_x: 404737.9120422932  # D1T=C1F=A1F
        utm_y: 3294671.9543913077
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404816.18500939116  # D2F=C2T
        utm_y: 3294663.2056394466
      }
      to_point {
        utm_x: 404740.5473511987  # D2T=B2T=B1T
        utm_y: 3294686.68556972
      }
    }
  }
}
config_area { # bridge 8 -> dock 7 E
  description: "bridge_8_to_dock_7"
  critical_area_from {
    utm_x: 405020.226011754  # EF
    utm_y: 3294871.46479281
  }
  critical_area_to {
    utm_x: 405034.740856087  # ET
    utm_y: 3294884.90711738
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {               # E1F
        utm_x: 404998.05101175397
        utm_y: 3294916.31479281
      }
      to_point {
        utm_x: 405010.090856087  # E1T
        utm_y: 3294861.95711738
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404998.05101175397  # E2F=E1F
        utm_y: 3294916.31479281 
      }
      to_point {
        utm_x: 405021.440856087  # E2T
        utm_y: 3294853.50711738
      }
    }
  }
}
config_area { # dock 7 -> bridge 8 F
  description: "dock_7_to_bridge_8"
  critical_area_from {
    utm_x: 405022.590856087  # FF
    utm_y: 3294876.88211738
  }
  critical_area_to {
    utm_x: 405043.240853087  # FT
    utm_y: 3294896.13211738
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 405010.090856087  # F1F=E1T
        utm_y: 3294861.95711738
      }
      to_point {
        utm_x: 405002.740856087  # F1T
        utm_y: 3294929.50711738
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 405021.440856087  # F2F=E2T
        utm_y: 3294853.50711738
      }
      to_point {
        utm_x: 405002.740856087  # F2T=F1T
        utm_y: 3294929.50711738
      }
    }
  }
}
config_area { # bridge 8 -> dock 8 G
  description: "bridge_8_to_dock_8"
  critical_area_from {
    utm_x: 405022.590856087  # GF=FF
    utm_y: 3294876.88211738
  }
  critical_area_to {
    utm_x: 405043.240853087  # GT=FT
    utm_y: 3294896.13211738
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404998.05101175397  # G1F=E1F
        utm_y: 3294916.31479281
      }
      to_point {
        utm_x: 405064.440856087  # G1T
        utm_y: 3294912.45711738
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 404998.05101175397  # G2F=G1F=E1F
        utm_y: 3294916.31479281
      }
      to_point {
        utm_x: 405079.190856087  # G2T
        utm_y: 3294907.05711738
      }
    }
  }
}
config_area { # dock 8 -> bridge 8 H
  description: "dock_8_to_bridge_8"
  critical_area_from {
    utm_x: 405034.740856087  # HF=ET
    utm_y: 3294884.90711738
  }
  critical_area_to {
    utm_x: 405047.690856087  # HT
    utm_y: 3294900.25711738
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 405064.440856087  # H1F=G1T
        utm_y: 3294912.45711738
      }
      to_point {
        utm_x: 404998.05101175397  # H1T=G1F=E1F
        utm_y: 3294916.31479281
      }
    }
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: CRANE_BLOCK
    block_turn {
      from_point {
        utm_x: 405079.190856087  # H2F=G2T
        utm_y: 3294907.05711738
      }
      to_point {
        utm_x: 405002.740856087  # H2T=F2T=F1T
        utm_y: 3294929.50711738
      }
    }
  }
}
