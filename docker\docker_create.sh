#!/usr/bin/env bash

SHELL_PATH="$( cd "$( dirname "$0"  )" && pwd  )"
DOCKER_REPO="docker.fabu.ai:5000/antenna-server/antenna-server"
TIME=$(date  +%Y%m%d_%H%M)
TAG="${TIME}"
LAST_BUILD="20250106_1612"

docker build -t "${DOCKER_REPO}:${TAG}" \
    -f "${SHELL_PATH}/Dockerfile" \
    --cache-from "${DOCKER_REPO}:${LAST_BUILD}" \
    "${SHELL_PATH}"

#docker tag ${IMAGE_ID} ${DOCKER_REPO}:${TAG}
#docker tag ${IMAGE_ID} docker.fabu.ai:5000/antenna-server/antenna-server:${TAG}
#docker push ${DOCKER_REPO}:${TAG}
#docker push docker.fabu.ai:5000/antenna-server/antenna-server:${TAG}