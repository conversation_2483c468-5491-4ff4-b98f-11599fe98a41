FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu20.04

ARG DEPENDENCIES=" \
        alien \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        fping \
        gdb \
        git \
        htop \
        iftop \
        iputils-ping \
        less \
        libaio1 \
        libpugixml-dev \
        libssl1.0.0 \
        libunwind-dev \
        lsof \
        locales \
        netcat \
        net-tools \
        patchelf \
        php \
        php-curl \
        pkg-config \
        python3-dbg \
        python3-dev \
        python3-pip \
        redis \
        rsync \
        silversearcher-ag \
        sqlite3 \
        ssh \
        strace \
        sudo \
        sysstat \
        tmux \
        unixodbc \
        unzip \
        vim \
        wget \
        xterm \
        zip \
        zsh"

ENV DEBIAN_FRONTEND=noninteractive

RUN sed -i s@/archive.ubuntu.com/@/mirrors.aliyun.com/@g /etc/apt/sources.list
RUN sed -i "$ a deb http://security.ubuntu.com/ubuntu xenial-security main" /etc/apt/sources.list

RUN set -e \
    && rm -f /etc/apt/apt.conf.d/docker-clean \
    && echo 'Binary::apt::APT::Keep-Downloaded-Packages "true";' >/etc/apt/apt.conf.d/keep-cache \
    && apt-get update \
    && apt-get -y install --no-install-recommends ${DEPENDENCIES} \
    && echo "no" | dpkg-reconfigure dash

RUN locale-gen en_US.UTF-8

ENV LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

RUN ln -s /usr/bin/python3 /usr/bin/python

COPY installers/install_skel.sh /tmp/installers/install_skel.sh
RUN bash /tmp/installers/install_skel.sh
COPY installers/install_instant_client.sh /tmp/installers/install_instant_client.sh
RUN bash /tmp/installers/install_instant_client.sh
COPY installers/install_arc.sh /tmp/installers/install_arc.sh
RUN bash /tmp/installers/install_arc.sh

RUN python -m pip install -i https://mirrors.aliyun.com/pypi/simple --upgrade pip

RUN pip install -i https://mirrors.aliyun.com/pypi/simple \
    # service start
    grpcio==1.62.3 \
    grpcio-tools==1.62.3 \
    grpc-interceptor \
    protobuf==4.25.5 \
    # fix protobuf relative import
    protoletariat==3.3.6 \
    Flask \
    redis \
    kafka-python \
    python-snappy \
    requests \
    aiohttp \
    cryptography \
    multiprocessing-logging \
    loguru \
    pyyaml \
    dataclasses_json \
    setproctitle \
    supervisor \
    ratelimit \
    backoff-utils \
    transitions \
    paho-mqtt \
    cryptography \
    pycryptodome \
    # service end
    # db start
    cx-Oracle==8.0.1 \
    mysqlclient \
    pymysql \
    SQLAlchemy \
    sqlalchemy_utils \
    ksql \
    # db end
    # math start
    numpy \
    utm \
    shapely \
    matplotlib \
    scikit-learn \
    pynmea2 \
    pyproj==3.5.0 \
    # math end
    # debug start
    py-spy \
    pyrasite \
    objgraph \
    psutil \
    libtmux \
    # debug end
    # trace start
    opentelemetry-sdk \
    opentelemetry-exporter-jaeger \
    opentelemetry-exporter-otlp \
    opentelemetry-instrumentation-grpc \
    opentelemetry-instrumentation-kafka-python \
    opentelemetry-instrumentation-flask \
    opentelemetry-instrumentation-requests \
    # trace end
    # package start
    wheel \
    nuitka \
    pyinstaller
    # package end

# 通过git安装最新的skywalking
COPY installers/install_skywalking.sh /tmp/installers/install_skywalking.sh
RUN bash /tmp/installers/install_skywalking.sh

# aliphone依赖aiohttp和cryptography
COPY aliphone /tmp/aliphone
RUN python /tmp/aliphone/setup.py install && rm -rf /tmp/aliphone

RUN pip install -i https://mirrors.aliyun.com/pypi/simple \
    opencv-python-headless \
    onnx==1.17.0 \
    onnxruntime-gpu==1.18.1 \
    onvif_zeep

##################
# add cuda support
##################
#newer:3bf863cc.pub
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     gnupg2 curl ca-certificates && \
#     curl -fsSL https://mirrors.aliyun.com/nvidia-cuda/ubuntu1804/x86_64/7fa2af80.pub | apt-key add - && \
#     echo "deb https://mirrors.aliyun.com/nvidia-cuda/ubuntu1804/x86_64 /" > /etc/apt/sources.list.d/cuda.list && \
#     echo "deb https://developer.download.nvidia.com/compute/machine-learning/repos/ubuntu1804/x86_64 /" > /etc/apt/sources.list.d/nvidia-ml.list && \
#     apt-get purge --autoremove -y curl

# ENV CUDA_VERSION 10.1.243
# ENV CUDA_PKG_VERSION 10-1=$CUDA_VERSION-1
# ENV NCCL_VERSION 2.8.3
# ENV CUDNN_VERSION ********

# For libraries in the cuda-compat-* package: https://docs.nvidia.com/cuda/eula/index.html#attachment-a
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     cuda-cudart-$CUDA_PKG_VERSION \
#     cuda-compat-10-1 \
#     cuda-libraries-$CUDA_PKG_VERSION \
#     cuda-npp-$CUDA_PKG_VERSION \
#     cuda-nvtx-$CUDA_PKG_VERSION \
#     libcublas10=10.2.1.243-1 \
#     libnccl2=$NCCL_VERSION-1+cuda10.1 \
#     cuda-nvml-dev-$CUDA_PKG_VERSION \
#     cuda-command-line-tools-$CUDA_PKG_VERSION \
#     cuda-nvprof-$CUDA_PKG_VERSION \
#     cuda-npp-dev-$CUDA_PKG_VERSION \
#     cuda-libraries-dev-$CUDA_PKG_VERSION \
#     cuda-minimal-build-$CUDA_PKG_VERSION \
#     libcublas-dev=10.2.1.243-1 \
#     libnccl-dev=2.8.3-1+cuda10.1 \
#     libcudnn7=$CUDNN_VERSION-1+cuda10.1 \
#     libcudnn7-dev=$CUDNN_VERSION-1+cuda10.1 \
#     libgl1-mesa-glx \
#     && ln -s cuda-10.1 /usr/local/cuda \
#     && apt-mark hold libnccl2 libcublas10 libnccl-dev libcublas-dev libcudnn7 \
#     && rm -rf /var/lib/apt/lists/*


# # Required for nvidia-docker v1
# RUN echo "/usr/local/nvidia/lib" >> /etc/ld.so.conf.d/nvidia.conf && \
#     echo "/usr/local/nvidia/lib64" >> /etc/ld.so.conf.d/nvidia.conf

# ENV PATH /usr/local/nvidia/bin:/usr/local/cuda/bin:${PATH}
# ENV LD_LIBRARY_PATH /usr/local/nvidia/lib:/usr/local/nvidia/lib64
# ENV LIBRARY_PATH /usr/local/cuda/lib64/stubs

# # nvidia-container-runtime
# ENV NVIDIA_VISIBLE_DEVICES all
# ENV NVIDIA_DRIVER_CAPABILITIES compute,utility
# ENV NVIDIA_REQUIRE_CUDA "cuda>=10.1 brand=tesla,driver>=396,driver<397 brand=tesla,driver>=410,driver<411 brand=tesla,driver>=418,driver<419"


##################
# onnx, opencv, onvif
##################
# RUN pip --no-cache-dir install -i  https://mirrors.aliyun.com/pypi/simple \
#     onnx==1.7 \
#     onnxruntime-gpu==1.4.0 \
#     opencv-python==******** \
#     onvif_zeep
