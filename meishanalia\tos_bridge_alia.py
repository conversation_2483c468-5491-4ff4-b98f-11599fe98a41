import math
import datetime
import time
import traceback
import uuid
from concurrent.futures import ThreadPoolExecutor

import google.protobuf.text_format as text_format

from cache import vehicle_cache, remote_detection_cache
from cache.command_cache import CommandCache
from cache.server_cache import ServerNodeCache
from cache.event_cache import Run<PERSON>vent<PERSON>ache
from cache import plc_cache
from common import common_util
from meishanalia.cache.vms_request_cache import VmsRequestCache
from meishanalia.common.documents_alia import TosWi
from common.name_converter import NameConverter
from common.event_log import log_event
from common.logger import logger
from common.periodical_task import PeriodicalTask
from common.tos_bridge_base import TosBridgeBase
from meishanalia.model import wis_info
from model import system_status, vehicle_event, tos_command_parse_results, sub_command_execution_info
from plc.crane_receiver import CraneInfoContainer
from proto.antenna_pb2 import CommandStatus, RemoteCommand, VehicleStatus, RemoteDetection, ControlCommandMessage
from proto.cache_pb2 import CommandWith<PERSON>tatus, ServerNode, AlarmEvent
from proto.system_status_cache_pb2 import SystemStatusDetail
from meishanalia.tos_db_alia import TosDb



class TosBridge(TosBridgeBase):
    def __init__(self,server_node = None):
        self._db = TosDb()
        self._command_cache = CommandCache()
        self._server_cache = None
        self._vms_request_cache = VmsRequestCache()
        self._offline_time = dict()
        self._offline_time[0] = 10
        self._offline_time[1] = 120 #safe mode 120秒
        self._loop_thread = None
        self._async_handler = None
        self._server_node = server_node
        self._server_node_name = ''
        self._cluster_mode = True
        self._server_mode = None
        self._run_event_cache = None
        self._run_event = dict()
        self._run_event_interval = 5 * 60.0 * 1000 #5m

    def query_remote_commands(self, vehicle_name):
        commands = self._command_cache.query_commands(vehicle_name)
        commands = list(filter(lambda command: command.status == CommandStatus.WAIT or command.status == CommandStatus.START, commands))
        return [c.command for c in commands]

    def is_command_cancelled(self, uuid):
        command = self._command_cache.query_command(uuid)
        return command is None or command.command.type == RemoteCommand.CANCEL

    def is_command_completed(self, vehicle_name, id):
        ret = False
        wi = self._db.query_wi_by_id(vehicle_name, id)
        if wi:
            status = str(wi.get('WI_STATUS'))
            if status == 'FINISH':
                ret = True
            # elif status == 'CANCEL':
            #     if wi.get('WI_TYPE') == 'DSCH' and wi.get('WI_ACT') == 'LOAD' and str(wi.get('REMARK2')) == 'GOTO':
            #         new_wi = self._db.query_wi_by_info(vehicle_name, wi)
            #         logger.info(f"vehicle_name:{vehicle_name} check wi completed status by id:{id}-->find wi:{new_wi}")
            #         if new_wi and str(new_wi.get('WI_STATUS')) == 'FINISH' and str(new_wi.get('REMARK2')) != 'GOTO':
            #             ret = True
        else:
            logger.warning(f"vehicle_name:{vehicle_name} fail to get wi by id:{id}")
        logger.info(f"vehicle_name:{vehicle_name} check wi completed status ret:{ret} for wi:{wi}")
        return ret

    def is_have_new_wi(self, vehicle_name, request):
        vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
        if vehicle_status is not None:
            # load_status = True if not vehicle_status.no_load else False
            load_status = True if (not vehicle_status.no_load and vehicle_status.driverless) else False
            wis, wis_num = self._db.get_new_wi(vehicle_name, load_status, False, True)
            if len(wis) > 0:
                logger.debug('vehicle_name:' + vehicle_name + ' has new wis size:' + str(len(wis)) + ",wis:" + str(wis))
                if self._db.is_wis_support_execute(vehicle_name, wis):
                    commands = self._command_cache.query_commands(vehicle_name)
                    logger.debug('vehicle_name:' + vehicle_name + ' query commands size:' + str(len(commands)))
                    if len(commands) == 0:
                        return True
                    elif commands[0].command.command_context.tos_command_id != wis[0]['ID']:
                        return True
        else:
            logger.warning(f"vehicle_name:{vehicle_name} invalid vehicle_status:{vehicle_status}")
        return False

    def query_crane_info(self, crane_no, is_debug):
        return self._db.query_crane_info(f'CR{crane_no}', is_debug)

    def update_vehicle_status(self, vehicle_status):
        # logger.debug('update vehicle status:' + str(vehicle_status))
        vehicle_cache.set_vehicle_status(vehicle_status.vehicle_name, vehicle_status)
        #del vehicle_status.fit_route_points[:]
        #log_event('update_vehicle_status', vehicle_status)

    def update_command_status(self, vehicle_name, command_status):
        self._command_cache.update_command_status(vehicle_name, command_status.uuid, command_status.status)
        # Mark all tasks with the same DB ID as failed.
        if (command_status.status == CommandStatus.FAILED or command_status.status == CommandStatus.CANCELED):
        #if (command_status.status == CommandStatus.FAILED):
            update_commands = self._command_cache.query_command(command_status.uuid)
            if update_commands is not None and len(update_commands.tos_command_info) > 0:
                commands = list(filter(lambda command: command.tos_command_info == update_commands.tos_command_info,
                                       self._command_cache.query_commands(vehicle_name)))
                if command_status.status == CommandStatus.CANCELED:
                    index_list = [index for index, command in enumerate(commands) if command.command.type == RemoteCommand.CANCEL]
                    if len(index_list) > 0:
                        commands = commands[:max(index_list)]
                        logger.debug(f"[update_command_status]:vehicle_name:{vehicle_name},index_list:{index_list},last_index:{max(index_list)}")
                for command in commands:
                    self._command_cache.update_command_status(vehicle_name, command.uuid, command_status.status)

    def loop(self):
            start = time.time()
            # check offline
            for truck_no in vehicle_cache.get_online_trucks_():
                status = vehicle_cache.get_vehicle_status_(truck_no, self._server_node_name)
                if status is not None:
                    offline_time = self._offline_time.get(int(status.vehicle_mode.safe_mode), 10)
                    if time.time() - status.timestamp_ns / 10 ** 9 > offline_time and \
                        abs(time.monotonic() - status.receive_timestamp_ns / 10 ** 9) > offline_time:
                        self._db.set_vehicle_offline(truck_no)
                        vehicle_cache.set_vehicle_offline_(truck_no, self._server_node_name)
                else:
                    self._db.set_vehicle_offline(truck_no)
                    vehicle_cache.set_vehicle_offline_(truck_no, self._server_node_name)
                    logger.warning(f'truck_no:{truck_no} get invalid status:{status},set offline')

            # cache online vehicles
            new_truck, stale_truck = vehicle_cache.refresh_online_trucks_(self._server_node_name)
            for truck_no in new_truck:
                logger.info(f"vehicle_online {truck_no}")
                log_event('vehicle_online', {'vehicle_name': truck_no})
                vehicle_event.insert(truck_no, vehicle_event.Event.online.value, int(time.time() * 1000))

            for truck_no in stale_truck:
                logger.info(f"vehicle_offline {truck_no}")
                log_event('vehicle_offline', {'vehicle_name': truck_no})
                vehicle_event.insert(truck_no, vehicle_event.Event.offline.value, int(time.time() * 1000))

            # process command
            for truck_no in vehicle_cache.get_online_trucks_(self._server_node_name):
                try:
                    self.process_commands(truck_no)
                    self.process_done_tasks(truck_no)
                    self.process_vehicle_status(truck_no)
                    # 2022.05 for iecs FLD
                    self.process_auto_reset_command(truck_no)
                except Exception as e:
                    logger.error(f"truck_no:{truck_no} fail to process command,e:{e}, trace:{traceback.format_exc()}")
                    self._make_run_event(truck_no, str(e), traceback.format_exc())

            self._server_cache.update_server_node(self._server_node_name,self._server_mode)
            end = time.time()
            logger.debug(f"tos_bridge loop cost:{end - start}")

    def _start_init(self):
        self._run_event_cache = RunEventCache()
        self._server_cache = ServerNodeCache()
        self._server_node_name = self._server_node.node_name
        self._server_mode = self._server_node.mode
        server_mode = ServerNode.NodeMode.Name(self._server_node.mode)
        self._cluster_mode = True if server_mode.startswith('CLUSTER') else False
        if not self._cluster_mode:
            trucks_no = sorted(list(NameConverter().all_truck_no()))
            vehicle_cache.init_truck_white_list(trucks_no, self._server_node_name)

    def start(self):
        self._start_init()
        self._async_handler = ThreadPoolExecutor(max_workers = 2)
        #master_only = False if (self._cluster_mode and self._server_mode != ServerNode.CLUSTER_BACK) else True
        master_only = True
        self._loop_thread = PeriodicalTask(target=self.loop, interval=1, master_only=master_only)
        self._loop_thread.start()

    def get_support_priority(self, truck_seq):
        if truck_seq.isdigit() and eval(truck_seq) < 4:
            return RemoteCommand.CommandContext.NORMAL
        else:
            return RemoteCommand.CommandContext.LOW

    def compare_wi_snapshot(self, one, other):
        # logger.info("one:" + one)
        # logger.info("other:" + other)
        if one == other:
            return (True, '', '')

        if False:
            commpare_keys = ['WI_STATUS']
            for key in one:
                if one.get(key) != other.get(key):
                    return (False, key, '%s: %s to %s' % (key, other[key], one[key]))
        else:
            '''
            one_truck_seq = int(one.get('TRUCK_SEQ'))
            other_truck_seq = int(other.get('TRUCK_SEQ'))
            if self.get_support_priority(int(one_truck_seq)) != self.get_support_priority(int(other_truck_seq)):
                logger.info("now the different priority tos cmd %d to %d:" % (other_truck_seq, one_truck_seq))
                return (False, 'TRUCK_SEQ', 'Priority %d to %d' % (other_truck_seq, one_truck_seq))
            '''
            #TO_POS, STOP_NODE ,QUEUE_NODE ,WAIT_NODE ,TRUCK_SEQ,TWIN_FLAG,
            if one.get('TWIN_FLAG') != 'Y' and one.get('TWIN_FLAG') != 'T':
                one['TWIN_FLAG'] = 'N'
            if other.get('TWIN_FLAG') != 'Y' and other.get('TWIN_FLAG') != 'T':
                other['TWIN_FLAG'] = 'N'
            #CTRL_VERSION
            #TRUCK_SEQ:can't be ignored for QUEUE_NODE always exist
            ignore_keys = ['TRUCK_STATUS', 'CTRL_STATUS', 'VERSION', 'RESET_VERSION', 'CTRL_RESET_VERSION', 'TO_POS', 'DRIVERLESS']  # 2022.03.15 ignore 'TO_POS',  change
            for key in one:
                if key in ignore_keys or key.endswith('_TIME'):
                    continue
                if one.get(key) != other.get(key):
                    return (False, key, '%s: %s to %s' % (key, str(other.get(key)), str(one.get(key))))
        return (True, '', '')

    def need_route_request(self, diff_key:str):
        # make sure
        reroute_key = ['TRUCK_SEQ', 'TRUCK_POS', 'TWIN_FLAG']  # , 'LOCK_FLAG'
        # reroute_key = []
        if diff_key in reroute_key:
            return True
        # 优先级，桥吊位置,吊具尺寸:无需考虑根据指令的箱位来
        return False

    def need_cancel(self, diff_key:str):
        #TRUCK_SEQ:can't be ignored for QUEUE_NODE always exist
        # cancel_key = ['WI_STATUS', 'TO_POS', 'STOP_NODE','QUEUE_NODE' ,'WAIT_NODE','TURN_NODE','CHECK_NODE','CTRL_VERSION']
        cancel_key = ['WI_STATUS', 'CTRL_STOP_NODE','QUEUE_NODE','WAIT_NODE','TURN_NODE','CHECK_NODE','TRUCK_SEQ',
                      'LOCK_FLAG','CTRL_LOCK_NODE','LOCK_PAVILION']  #'CTRL_VERSION',
        if diff_key in cancel_key:
            return True
        #优先级，桥吊位置,吊具尺寸
        return False

    def crane_command_info(self, destination, vehicle_name, debug_open = False):
        if destination is not None and destination.startswith('CR'):
            crane_no = int(destination[2:4])
            crane_info = CraneInfoContainer().get_crane_info(crane_no)
            lane_no = "None"
            vessel = "None"
            db_crane_info = self.query_crane_info(crane_no, NameConverter().is_debug_truck_no(vehicle_name))
            if db_crane_info is not None:
                lane_no = str(db_crane_info.get('LANE_NO'))
                vessel = str(db_crane_info.get('VESSEL_DIRECTION'))
            if crane_info is None:
                if debug_open:
                    system_status.insert(vehicle_name, SystemStatusDetail.CRANE_NOT_EXIST, destination[:4] + ' plc is not exist')
                logger.warning(f'{vehicle_name}:{destination} plc is not exist')
                return CommandWithStatus.TosCommandInfo.CraneCommandInfo(
                        valid=False,
                        destination=destination,
                        g_pos=0,
                        spreader_size=0,
                        g_state=0,
                        lane_no=lane_no,
                        vessel=vessel)
            else:
                return CommandWithStatus.TosCommandInfo.CraneCommandInfo(
                        valid=True,
                        destination=destination,
                        g_pos=crane_info.g_pos,
                        spreader_size=crane_info.spr_size,
                        g_state=crane_info.g_state,
                        lane_no=lane_no,
                        vessel=vessel)
        else:
            return CommandWithStatus.TosCommandInfo.CraneCommandInfo()

    def get_gantry_command_info(self, vehicle_name):
        remote_detections = remote_detection_cache.get_remote_detection(vehicle_name)
        valid = False
        gantry_no = -1
        for remote_detection in remote_detections:
            if remote_detection.type != RemoteDetection.DetecionType.GANTRY:
                continue
            gantry_no = remote_detection.id
        g_pos = 0
        g_state = 0
        is_stable = False
        if gantry_no != -1:
            gantry_info = plc_cache.get_gantry(gantry_no)
            if gantry_info is not None:
                valid = True
                g_pos = gantry_info.g_pos
                is_stable = gantry_info.is_stable
        return CommandWithStatus.TosCommandInfo.GantryCommandInfo(valid=valid, gantry_no=gantry_no, g_pos=g_pos, is_stable=is_stable)

    def _query_new_wi(self, vehicle_name):
        vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
        if vehicle_status is None:
            logger.warning(f'vehicle_name:{vehicle_name} get invalid vehicle status:{vehicle_status}')
            return [], 0
        load_status = True if (not vehicle_status.no_load and vehicle_status.driverless) else False
        return self._db.query_new_wi(vehicle_name, load_status)

    def process_commands(self, vehicle_name):
        valid = True
        commands = self._command_cache.query_commands(vehicle_name)
        if len(commands) == 0:
            # alone wi and all twins
            # new_tos_wis, query_wis_num = self._db.query_new_wi(vehicle_name)
            new_tos_wis, query_wis_num = self._query_new_wi(vehicle_name)
            logger.info('vehicle:' + vehicle_name + ',query new_tos_wis from db size:' + str(len(new_tos_wis)) + \
                        ',query_wis_num:' + str(query_wis_num) + ',new tos: ' + str(new_tos_wis))
            # alone wi and twins without yard
            tos_wis = self._db.fiter_execute_wis(vehicle_name, new_tos_wis)
            info_wis = tos_wis
            tos_wis = tos_wis[:1]
            command_info = list()
            for wi in info_wis:
                crane_command_info=self.crane_command_info(wi.get('TO_POS'), vehicle_name, True)
                if crane_command_info is None or not crane_command_info.valid:
                    logger.warning(f'{vehicle_name} fail to get crane command info,use default')
                    valid = False
                gantry_command_info = self.get_gantry_command_info(vehicle_name)
                command_info.append(CommandWithStatus.TosCommandInfo(
                      db_id=wi.get('ID'),
                      # wi_no=wi.get('WI_NO'),
                      wi_no=wi.get('WI_ID'),  # 2022.03.18 antenna 不改 proto 则取 wi_id 给 wi_no
                      version=wi.get('VERSION'),
                      wi_repr=repr(wi),
                      crane_command_info=crane_command_info,
                      gantry_command_info=gantry_command_info))
            logger.info('vehicle:' + vehicle_name + ' valid:' + str(valid) + ',query new tos_wis from db size:' + str(len(tos_wis)) + \
                        ' info_wis size:' + str(len(info_wis)) + ',tos: ' + str(tos_wis))
            for tos_wi in tos_wis:
                if not self.patch_wi(vehicle_name, tos_wi):
                    continue
                commands = TosWi(tos_wi, self._db, new_tos_wis).get_commands()
                if len(commands) == 0:
                    self._db.update_wi_status(vehicle_name, tos_wi['ID'], 'FINISH')
                else:
                    for command in commands:
                        del command.tos_command_info[:]
                        command.tos_command_info.MergeFrom(command_info)
                        try:
                            tos_command_parse_results.async_insert(self._async_handler,
                                vehicle_name, tos_wi['ID'], tos_wi['WI_NO'], tos_wi["VERSION"], command.command.uuid,
                                command.command.type, text_format.MessageToString(command, as_one_line=True))
                        except Exception as e:
                            logger.warning(f"Insert tos_command_parse_results err{e}, trace:{traceback.format_exc()}")

                    logger.info(f'''vehicle_name:{vehicle_name} append commands for wi id:{tos_wi['ID']}''')
                    #log_event('dispatch_command', {'vehicle_name': vehicle_name, 'commands': commands})
                    self._command_cache.append_commands(vehicle_name, commands)

        else:
            tos_command_info = commands[0].tos_command_info
            should_cancel = False
            should_route = False
            should_reset = False
            reset_flag = ""  # reset 标志
            command_type = commands[0].command.type
            if len(tos_command_info) > 0 and command_type != RemoteCommand.CANCEL:
                should_cancel, should_route, reset_flag = self.is_should_cancel_command(vehicle_name, command_type, tos_command_info)
            if should_cancel:
                self._command_cache.cancel_all(vehicle_name)
                should_reset = True
            if should_route or should_reset:
                for info in tos_command_info:
                    self._db.reset_command(vehicle_name, info.db_id, info="process_commands"+reset_flag, need_route=should_route)
                    should_route = False

    def is_should_cancel_command(self, vehicle_name, command_type, tos_command_info):
        should_cancel = False
        should_route = False
        message = ''
        for info in tos_command_info:
            updated_wi = self._db.query_wi_update(info.db_id, info.version)
            if updated_wi is None or (updated_wi is not None and updated_wi.get('CTRL_STATUS') == 'DISPATCH'):
                wi_snapshot = eval(info.wi_repr)
                if updated_wi != None:
                    logger.info('vehicle:' + vehicle_name + ' update tos_wi from db:' + str(updated_wi))
                    (same, diff_key, msg) = self.compare_wi_snapshot(updated_wi, wi_snapshot)
                    logger.debug(f"[is_should_cancel_command] same:{same}, diff_key:{diff_key}, msg:{msg}")
                    if not same:
                        should_cancel = self.need_cancel(diff_key)
                        should_route = self.need_route_request(diff_key)
                        if should_cancel or should_route:
                            logger.info(f"vehicle:{vehicle_name} reset tos_wi from db:{str(updated_wi)},diff:{msg},should_route:{should_route},should_cancel:{should_cancel}")
                            logger.info(f"'reset_command', 'reason': 'tos_update', 'vehicle_name': {vehicle_name}, 'old_wi': {wi_snapshot}, 'new_wi': {updated_wi}")
                            system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET, 'by tos update %s' % msg)
                            return should_cancel, should_route, ""
                    else:
                        self._db.update_wi_version(info.db_id, updated_wi['VERSION'])
            elif updated_wi is not None:
                logger.info(f"vehicle:{vehicle_name} ignore wi id:{updated_wi.get('ID')},ctrl status:{updated_wi.get('CTRL_STATUS')}")

            if common_util.is_gantry_yard_wi(info.wi_snapshot):
                should_cancel, should_route, message = self.cancel_command_in_gantry_commands(vehicle_name, command_type, info)
                if should_cancel:
                    return should_cancel, should_route, message
            else:
                should_cancel, should_route, message = self.cancel_command_in_crane_commands(vehicle_name, command_type, info)
                if should_cancel:
                    return should_cancel, should_route, message
        return should_cancel, should_route, message

    def cancel_command_in_crane_commands(self, vehicle_name, command_type, info):
        if info.crane_command_info is not None and info.crane_command_info.valid:
            new_crane_info = self.crane_command_info(info.crane_command_info.destination, vehicle_name)
            if new_crane_info is not None and new_crane_info.valid:
                if new_crane_info.spreader_size != 0 and \
                        not (new_crane_info.spreader_size == 4 and info.crane_command_info.spreader_size == 2) and \
                        not (new_crane_info.spreader_size == 2 and info.crane_command_info.spreader_size == 4) and \
                        (info.wi_snapshot['WI_TYPE'] == 'DSCH' or (
                                info.wi_snapshot['WI_TYPE'] == 'LOAD' and info.wi_snapshot['TWIN_FLAG'] == 'T')) and \
                        new_crane_info.spreader_size != info.crane_command_info.spreader_size:
                    logger.info('vehicle:' + vehicle_name + ' crane status changed spreader_size from: ' + text_format.MessageToString(info.crane_command_info, as_one_line=True)  + ' --to--> ' + text_format.MessageToString(new_crane_info, as_one_line=True))
                    system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET,
                                         info.crane_command_info.destination + ' by spreader:' + str(
                                             info.crane_command_info.spreader_size) \
                                         + ' to ' + str(new_crane_info.spreader_size))
                    return True, True,  "[spreader_size]"
                # reset by lane no
                if str(new_crane_info.lane_no) != str(info.crane_command_info.lane_no) and str(
                        new_crane_info.lane_no) != 'None':
                    logger.info(
                        'vehicle:' + vehicle_name + ' reset tos_wi crane status changed lane_no from: ' + str(
                            info.crane_command_info.lane_no) + ' --to--> ' + str(new_crane_info.lane_no))
                    system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET,
                                         info.crane_command_info.destination + ' by lane_no:' + str(
                                             info.crane_command_info.lane_no) \
                                         + ' to ' + str(new_crane_info.lane_no))
                    return True, True, "[lane_no]"
                if abs(new_crane_info.g_pos - info.crane_command_info.g_pos) > 10:
                    logger.debug(
                        f"vehicle:{vehicle_name} new crane status changed gpos from:{info.crane_command_info.g_pos} to {new_crane_info.g_pos},"
                        f"new g_state:{new_crane_info.g_state},command type:{RemoteCommand.CommandType.Name(command_type)}")
                    # to do if manual via cps?
                    if command_type == RemoteCommand.WAIT_CRANE_OFF:
                        if new_crane_info.g_state == 1 and abs(
                                new_crane_info.g_pos - info.crane_command_info.g_pos) > 15:
                            logger.info(
                                'vehicle:' + vehicle_name + ' reset tos_wi crane status changed gpos from: ' + str(
                                    info.crane_command_info.g_pos) + ' --to--> ' + str(new_crane_info.g_pos))
                            system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET, \
                                                 info.crane_command_info.destination + ' by pos:' + str(
                                                     info.crane_command_info.g_pos) \
                                                 + ' to ' + str(new_crane_info.g_pos))
                            return True, True, "[g_pos]"
                    else:
                        self._command_cache.update_command_crane_info(vehicle_name, new_crane_info)
            else:
                logger.warning('vehicle:' + vehicle_name + ' invalid new_crane_info')
        elif info.wi_snapshot['TO_POS'].startswith('CR'):
            logger.warning('vehicle:' + vehicle_name + ' invalid crane_command_info')
        return False, False, ""

    def cancel_command_in_gantry_commands(self, vehicle_name, command_type, info):
        new_gantry_info = self.get_gantry_command_info(vehicle_name)
        # 如果历史数据无效，则直接更新为有效数据
        if info.gantry_command_info is None or not info.gantry_command_info.valid:
            if new_gantry_info.gantry_no != -1:
                self._command_cache.update_command_gantry_info(vehicle_name, new_gantry_info)
            return False, False, ""
        old_target_gantry_no = info.gantry_command_info.gantry_no

        # 比较龙门吊数据是否有修改
        if abs(new_gantry_info.g_pos - info.gantry_command_info.g_pos) > 100:
            if command_type == RemoteCommand.WAIT_GANTRY_OFF:
                if new_gantry_info.gantry_no != old_target_gantry_no and new_gantry_info.gantry_no != -1:
                    logger.info(f"cancel_command_in_gantry_commands, target gantry_no changed, old: [{old_target_gantry_no}], new: [{new_gantry_info.gantry_no}]")
                    return True, True, "[gantry_no]"
                if abs(new_gantry_info.g_pos - info.gantry_command_info.g_pos) > 150 and new_gantry_info.is_stable == 1:
                    logger.info(f"cancel_command_in_gantry_commands, gpos changed , old: [{info.gantry_command_info.g_pos}], new: [{new_gantry_info.g_pos}]")
                    return True, True, "[gantry_pos]"
            # 其他指令阶段直接更新记录数据
            else:
                self._command_cache.update_command_gantry_info(vehicle_name, new_gantry_info)
        return False, False, ""


    def process_done_tasks(self, vehicle_name):
        commands = self._command_cache.query_commands(vehicle_name)
        for command in commands:
            if command.status == CommandStatus.WAIT or command.status == CommandStatus.START:
                break
            status = ''
            if command.status == CommandStatus.FINISH:
                status = command.finish_db_status
            elif command.status == CommandStatus.FAILED:
                status = 'ABANDON_YARD'
            elif command.status == CommandStatus.CANCELED:
                status = 'CANCEL_YARD'
            if len(status) > 0:
                for info in command.tos_command_info:
                    logger.info(f"vehicle_name:{vehicle_name} update wi status bd_id:{info.db_id} "
                        f"for uuid:{command.uuid},command type:{RemoteCommand.CommandType.Name(command.command.type)},"
                        f"command status:{CommandStatus.ExecuteStatus.Name(command.status)},status:{status}")
                    self._db.update_wi_status(vehicle_name, info.db_id, status)
                    if info.wi_snapshot['REMARK3'] == 'FABU' and info.wi_snapshot['WI_TYPE'] == "None" \
                        and (status == 'FINISH' or status == 'ABANDON_YARD'):
                        self._db.update_wi(vehicle_name, {'WI_STATUS':'FINISH'}, info.db_id, info.wi_snapshot['WI_TYPE'], info.wi_snapshot['WI_ACT'])
                    log_event('update_wi_status', {'vehicle_name': vehicle_name, 'tos_command_info': info, 'command_status': status})
            if self._command_cache.archive_command(vehicle_name, command.uuid):
                logger.info(f"vehicle_name:{vehicle_name} archive command "
                            f"for uuid:{command.uuid},command type:{RemoteCommand.CommandType.Name(command.command.type)},"
                            f"command status:{CommandStatus.ExecuteStatus.Name(command.status)},status:{status}")
                log_event('archive_command', {'vehicle_name': vehicle_name, 'command': command, 'command_status': status})
            else:
                logger.warning(f"vehicle_name:{vehicle_name} fail to archive command "
                            f"for uuid:{command.uuid},command type:{RemoteCommand.CommandType.Name(command.command.type)},"
                            f"command status:{CommandStatus.ExecuteStatus.Name(command.status)},status:{status}")
                break

    def process_vehicle_status(self, vehicle_name):
        vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
        if vehicle_status is None:
            logger.warning(f'vehicle_name:{vehicle_name} get invalid vehicle status:{vehicle_status} in process status')
            return
        last_command = self._command_cache.get_last_status_changed_command(vehicle_name)
        if last_command is None:
            vehicle_info = self._db.update_vehicle_status(vehicle_status, '0', '0', 2, '0', 'IDEL')
            path2 = vehicle_cache.get_truck_info_path2_from_redis(vehicle_name)
            vehicle_info.update({"PATH2": path2})
            vehicle_cache.set_vehicle_status_to_redis(vehicle_name, vehicle_info)
            return
        # 2022.03.09
        # wi_no = ','.join([str(l.wi_no) for l in last_command.tos_command_info])
        wi_id = ','.join([str(l.wi_no) for l in last_command.tos_command_info])  # 2022.03.18 antenna 不改 proto 则取wi_no
        if len(wi_id) == 0:
            wi_id = '0'
        mode = 2
        truck_status = 0

        need_check_command = True
        if vehicle_status.work_mode != VehicleStatus.WorkMode.NORMAL_WORK:
            mode = 4
            need_check_command = False
        if need_check_command:
            # 上一条为正常TOS指令
            if VehicleStatus.STOP_WORK != vehicle_status.work_mode and VehicleStatus.LEAVE_WORK != vehicle_status.work_mode:
                if last_command.status == CommandStatus.FINISH:
                    mode = 2
                    truck_status = 0
                elif last_command.status == CommandStatus.START:
                    mode = 1
                    truck_status = 2
            # 上一条为离场收工类指令
            else:
                mode = 4
        logger.debug('process vehicle_name:{}, wi_id:{}'.format(vehicle_name, str(wi_id)))
        action = 'IDEL'
        if len(last_command.tos_command_info) > 0:
            wi_snapshot = eval(last_command.tos_command_info[0].wi_repr)
            action = wi_snapshot.get('CTRL_ACTION') if wi_snapshot.get('CTRL_ACTION') is not None else 'IDEL'
        vehicle_info = self._db.update_vehicle_status(vehicle_status, wi_id, '', mode, str(truck_status), action)
        # update vehicle_status to redis
        path2 = vehicle_cache.get_truck_info_path2_from_redis(vehicle_name)
        vehicle_info.update({"PATH2": path2})
        vehicle_cache.set_vehicle_status_to_redis(vehicle_name, vehicle_info)

    def process_vehicle_arrivals(self, vehicle_name):
        vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
        if vehicle_status is None:
            logger.warning(f'vehicle_name:{vehicle_name} get invalid vehicle status:{vehicle_status} in process arrivals')
            return
        wis = self._db.get_recent_wis(vehicle_name)
        for wi in wis:
            try:
                if wi['WI_STATUS'] == 'DISPATCH' and len(wi['TO_POS']) == 2 and wi['TRUCK_STATUS'] != 'ARRIVE':
                    target_pos = get_service_point_loc(wi['TO_POS'] + '04')
                    if (target_pos[0] - vehicle_status.position.utm_x) ** 2 + (target_pos[1] - vehicle_status.position.utm_y) ** 2 < 12 * 12:
                        logger.info('auto arriving wi: {}'.format(wi['ID']))
                        self._db.update_wi_status(vehicle_name, wi['ID'], 'ARRIVE')
            except Exception as e:
                logger.warning(f"Process vehicle arrivals error: {e}, trace:{traceback.format_exc()}")

    def _need_auto_reset_wi(self, vehicle_name, wi):
        if wi.get('WI_ACT') == 'STOP_WORK' or wi.get('WI_ACT') == 'LEAVE_SPACE':
            return False
        else:
            return True

    def process_auto_reset_command(self, vehicle_name):
        if vehicle_name.startswith('AT'):
            commands = self._command_cache.query_commands(vehicle_name)
            if len(commands) == 0:
                try:
                    vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
                    load_status = ""
                    if vehicle_status:
                        load_status = True if (not vehicle_status.no_load and vehicle_status.driverless) else False
                    wi = self._db.query_abandon_wi(vehicle_name, load_status)
                    if wi is not None and wi.get('DC_UPDATE_TIME') is not None and self._need_auto_reset_wi(
                            vehicle_name, wi) and wi.get(
                            'DC_UPDATE_TIME') < datetime.datetime.now() - datetime.timedelta(seconds=15):
                        self._db.reset_command(vehicle_name, wi['ID'], info="abandon_auto_reset", need_route=False)
                        logger.info(f'''vehicle_name:{vehicle_name} auto reset abandon wi id:{wi['ID']}''')
                        system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET, 'auto reset wi id: %d' % wi['ID'])
                except Exception as e:
                    logger.warning(f"Process auto reset  command error: {e}, trace:{traceback.format_exc()}")

    def process_command_message(self, vehicle_name):
        command_messages = self._command_cache.pop_all_command_message(vehicle_name,auto_delete=True)
        message_len = len(command_messages)
        logger.debug(f"vehicle_name:{vehicle_name} process command message:{message_len}")
        for command_message in command_messages:
            try:
                logger.info(f"vehicle_name:{vehicle_name} process command message:{text_format.MessageToString(command_message,as_one_line=True)}")
                if command_message.type == ControlCommandMessage.UPDATE_COMMAND:
                    self.update_command_status(vehicle_name,command_message.command_status)
                    sub_command_execution_info.async_insert(self._async_handler,vehicle_name, command_message.command_status.uuid,\
                        command_message.command_status.status,command_message.command_status.detail_status)
                else:
                    logger.warning(f"vehicle_name:{vehicle_name} not support message type:{ControlCommandMessage.CommandMessageType.Name(command_message.type)}")
            except Exception as e:
                logger.warning(f"vehicle_name:{vehicle_name} Process command message error: {e},trace:{traceback.format_exc()}")

    def patch_wi(self, vehicle_name, wi):
        # set DRIVERLESS flag
        vehicle_status = vehicle_cache.get_vehicle_status(vehicle_name)
        if vehicle_status is None:
            logger.warning(f"vehicle_name:{vehicle_name} fail to patch id:{wi.get('ID')} for invalid vehicle_status")
            return False
        wi['DRIVERLESS'] = 'True' if vehicle_status.driverless else 'False'
        wi['BUSINESS_SCENE'] = vehicle_status.vehicle_mode.business_scene
        return True

if __name__ == '__main__':
    response_is_completed = TosBridge().is_command_completed("AT501", 1041)
    print("response_is_completed AT501 :", response_is_completed)
