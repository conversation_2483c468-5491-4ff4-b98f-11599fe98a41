import copy
import time

from cache.client import Cache<PERSON><PERSON>
from cache.config_cache import ConfigCache
from common.logger import logger
from common.name_converter import NameConverter
from common.singleton import Singleton
from proto import cache_pb2, antenna_pb2, web_service_pb2

# 锁亭调度
SCHEDULE_RESPONSE = 'schedule-response'

# 装船调度
SHIP_LOAD_CONFIG_PREFIX = 'ship-load-config'
SHIP_LOAD_CONFIG_BACK_PREFIX = 'ship-load-config-back'
VEHICLE_IN_FENCE_STATUS_PREFIX = 'vehicle-in-fence-status'
SHIP_LOAD_DEPENDENCY_PREFIX = 'ship-load-dependency'
SHIP_LOAD_SCH_IS_TOS_CONTROL_PREFIX = 'ship-load-sch-is-tos-control'

# 动态掉头点
UTURN_POINT_SCHEDULING_PREFIX = 'uturn-point-scheduling-key'


class LockSchedulingCache(metaclass=Singleton):
    def __init__(self):
        self._redis = Cache<PERSON>lient()

    # key: tos_command_id
    # value: 可由多个调度规划组成的结构体，目前结构体内只包含Lock相关
    def UpdateLockSchedulingStrategy(self, tos_command_id, schedule_response):
        logger.debug(
            f'[SCHEDULE_DEBUG] UpdateLockSchedulingStrategy: id: {tos_command_id}, schedule_response: {schedule_response}')
        key = f"{SCHEDULE_RESPONSE}:{tos_command_id}"
        scheduling_strategy = self._redis.get_proto(key, cache_pb2.SchedulingStrategy)
        if scheduling_strategy is None:
            scheduling_strategy = cache_pb2.SchedulingStrategy()
        scheduling_strategy.lock_scheduling_strategy.CopyFrom(schedule_response)
        return self._redis.set_proto(key, scheduling_strategy, ex=3600)

    def QuerySchedulingStrategy(self, tos_command_id) -> cache_pb2.SchedulingStrategy:
        key = f"{SCHEDULE_RESPONSE}:{tos_command_id}"
        schedule_strategy = self._redis.get_proto(key, cache_pb2.SchedulingStrategy)
        # logger.debug(
        #     f'[SCHEDULE_DEBUG] QuerySchedulingStrategy: id: {tos_command_id}, schedule_response: {schedule_strategy}')
        return schedule_strategy

    def DeleteSchedulingStrategy(self, tos_command_id):
        key = f"{SCHEDULE_RESPONSE}:{tos_command_id}"
        return self._redis.delete(key)


class ShipLoadSchedulingCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._expire_time = 5  # seconds

    def set_ship_load_sch_is_tos_control(self, mode):
        """
            该模式特指按照港口出具的依赖关系进行调度
        """
        return self._redis.set(SHIP_LOAD_SCH_IS_TOS_CONTROL_PREFIX, 1 if mode else 0, ex=self._expire_time)

    def is_ship_load_sch_by_tos_control(self):
        """
            该模式特指按照港口出具的依赖关系进行调度
        """
        value = self._redis.get(SHIP_LOAD_SCH_IS_TOS_CONTROL_PREFIX)
        if value is not None and value.decode() == '1':
            return True
        else:
            return False

    def set_ship_load_config(self, config):
        """ 设置装船调度配置
        """
        return self._redis.set_proto(SHIP_LOAD_CONFIG_PREFIX, config)

    def get_ship_load_config(self):
        """ 获取装船调度配置
        """
        return self._redis.get_proto(SHIP_LOAD_CONFIG_PREFIX, cache_pb2.ShipLoadConfigs)

    def get_fleets_ship_load_config(self):
        """
            ret: dict(fleet_id, sch_config)
        """
        config_dict = dict()
        configs = self.get_ship_load_config()
        if configs is None:
            return config_dict

        for config in configs.ship_load_configs:
            if not config.enable_config:
                continue

            sch_config = antenna_pb2.ShipLoadSchedulingConfig()
            sch_config.load_sch_strategy = config.load_sch_strategy
            sch_config.ship_load_lock_strategy = config.ship_load_lock_strategy
            sch_config.ship_load_lock_pavilion = config.ship_load_lock_pavilion
            sch_config.customized_lock_config_of_crane.MergeFrom(config.customized_lock_config_of_crane)
            for fleet in config.fleets_id:
                sch_config.fleet_id = fleet
                config_dict[int(fleet)] = copy.deepcopy(sch_config)

        return config_dict

    def set_ship_load_sch_back_config(self, request):
        """ 备份装船调度配置
        """
        return self._redis.set_proto(SHIP_LOAD_CONFIG_BACK_PREFIX, request)

    def get_ship_load_sch_back_config(self):
        """ 获取备份装船调度配置
        """
        return self._redis.get_proto(SHIP_LOAD_CONFIG_BACK_PREFIX, cache_pb2.ShipLoadConfigs)

    def disable_ship_load_sch_config(self):
        """ 主动关闭装船调度选项，主要用于调度服务断连时的关闭
        """
        logger.debug("[ShipLoadSch] disable_ship_load_sch_config")
        request = self.get_ship_load_config()
        if request is None:
            logger.debug("[ShipLoadSch] empty ship load config")
            return
        self.set_ship_load_sch_back_config(request)  # 需要备份
        for config in request.ship_load_configs:
            config.enable_config = False
        self.set_ship_load_config(request)
        return

    def recovery_ship_load_sch_config(self):
        """ 恢复装船调度选项，主要用于调度服务再次连接时的恢复
        """
        logger.debug("[ShipLoadSch] recovery_ship_load_sch_config")
        request_back = self.get_ship_load_sch_back_config()
        if request_back is not None:
            self.set_ship_load_config(request_back)

    def get_vehicle_ship_load_sch_config(self, truck_no):
        """ 获取车辆对应装船调度配置
        """
        fleet_id = ConfigCache().get_vehicle_fleet(truck_no)
        if fleet_id is None:
            return None
        dict_config = self.get_fleets_ship_load_config()
        fleet_id = int(fleet_id)
        if fleet_id in dict_config:
            return dict_config[fleet_id]
        else:
            return None

    def get_customized_lock_config_of_crane(self, truck_no):
        """ 获取对应桥吊定制化锁亭配置
        """
        ret_dict = dict()
        fleet_id = ConfigCache().get_vehicle_fleet(truck_no)
        if fleet_id is None:
            return ret_dict
        config_dict = self.get_fleets_ship_load_config()
        fleet_id = int(fleet_id)
        if fleet_id in config_dict and len(config_dict[fleet_id].customized_lock_config_of_crane) > 0:
            for config in config_dict[fleet_id].customized_lock_config_of_crane:
                ret_dict[config.crane_no] = config.lock_pavilion
        return ret_dict

    def get_fleet_ship_load_sch_config(self, fleet_id):
        """ 获取车队对应装船调度配置
        """
        dict_config = self.get_fleets_ship_load_config()
        fleet_id = int(fleet_id)
        if fleet_id in dict_config:
            return dict_config[fleet_id]
        else:
            return None

    def is_governed_by_ship_load_sch(self, truck_no):
        """
            两种情况下受调度服务调度：
            1. 云控进行了相关车队配置，此时调度配置车队
            2. 调度服务输出 tos_control == True，此时调度所有车队
        """
        if self.is_ship_load_sch_by_tos_control():
            return True

        config = self.get_vehicle_ship_load_sch_config(truck_no)
        if config is None:
            return False
        if config.load_sch_strategy == antenna_pb2.ShipLoadSchedulingConfig.ShipLoadSchedulingStrategy.DISABLE_SHIP_LOAD_SCHEDULING:
            return False
        return True

    def set_ship_load_status_of_vehicles_in_fence(self, status_list) -> bool:
        """ 针对电子围栏内的车辆集合设置装船调度状态
        """
        # 清空当前数据重新设置
        self._redis.delete(VEHICLE_IN_FENCE_STATUS_PREFIX)

        # 设置最新数据
        hash_map = dict()
        vehicle_set_in_fence = []  # 记录电子围栏内的所有车辆
        for status in status_list:
            truck_no = NameConverter().to_no(status.vehicle_id)
            vehicle_set_in_fence.append(truck_no)
            hash_map[truck_no] = status.status
        # 更新车辆状态 hash
        self._redis.hmset(VEHICLE_IN_FENCE_STATUS_PREFIX, hash_map)
        self._redis.expire(VEHICLE_IN_FENCE_STATUS_PREFIX, self._expire_time)

        return True

    def get_ship_load_status_of_vehicles_in_fence(self):
        """ 获取电子围栏内的车辆集合装船调度状态
        """
        value_map = self._redis.hgetall(VEHICLE_IN_FENCE_STATUS_PREFIX)
        ret_dict = dict()
        for k, v in value_map.items():
            ret_dict[k.decode()] = antenna_pb2.VehicleInFenceStatus.Status.Name(int(v.decode()))
        return ret_dict

    def is_vehicle_released(self, truck_no):
        """ 车辆是否非悬空
        """
        value = self._redis.hget(VEHICLE_IN_FENCE_STATUS_PREFIX, truck_no)
        if value is None:
            return False
        return int(value.decode()) == antenna_pb2.VehicleInFenceStatus.RELEASED

    def is_vehicle_blocked(self, truck_no) -> bool:
        """ 车辆是否悬空
        """
        value = self._redis.hget(VEHICLE_IN_FENCE_STATUS_PREFIX, truck_no)
        if value is None:
            return False
        return int(value.decode()) != antenna_pb2.VehicleInFenceStatus.RELEASED

    def get_vehicle_blocked_sequence(self, truck_no):
        """ 车辆是否悬空
        """
        value = self._redis.hget(VEHICLE_IN_FENCE_STATUS_PREFIX, truck_no)
        if value is None:
            return 0
        fence_status = int(value.decode())
        fence_status_to_seq = {antenna_pb2.VehicleInFenceStatus.RELEASED:0,\
                               antenna_pb2.VehicleInFenceStatus.BLOCKED:100,\
                               antenna_pb2.VehicleInFenceStatus.SINGLE_BUFFER:200,\
                               antenna_pb2.VehicleInFenceStatus.BLOCKED_AND_SINGLE_BUFFER:300}
        return fence_status_to_seq.get(fence_status,0)

    def set_ship_load_dependency(self, dep_list):
        """ 设置悬空车辆阻塞关系
        """
        # 清空当前数据重新设置
        self._redis.delete(SHIP_LOAD_DEPENDENCY_PREFIX)

        hash_map = dict()
        for dep in dep_list:
            truck_no = NameConverter().to_no(dep.vehicle_id)
            dependent_truck_no = dep.dependent_vehicle_id
            if truck_no not in hash_map:
                hash_map[truck_no] = dependent_truck_no
            else:
                hash_map[truck_no] += ',' + str(dependent_truck_no)
        self._redis.hmset(SHIP_LOAD_DEPENDENCY_PREFIX, hash_map)
        self._redis.expire(SHIP_LOAD_DEPENDENCY_PREFIX, self._expire_time)
        return True

    def get_ship_load_dependency(self):
        """ 获取悬空车辆阻塞关系
        """
        value_map = self._redis.hgetall(SHIP_LOAD_DEPENDENCY_PREFIX)
        ret_dict = dict()
        for k, v in value_map.items():
            ret_dict[k.decode()] = v.decode()
        return ret_dict


class UturnPointSchCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._expire_time = 15  # seconds

    def set_uturn_point_info(self, request):
        # hash key是增量更新，先删除历史数据
        self.del_uturn_point_info()

        for vehicle_info in request.vehicle_info:
            self._redis.hset_proto(UTURN_POINT_SCHEDULING_PREFIX, vehicle_info.vehicle_id, vehicle_info)

        self._redis.expire(VEHICLE_IN_FENCE_STATUS_PREFIX, self._expire_time)

        return True

    def get_vehicle_uturn_point_info(self, truck_no):
        value = self._redis.hget_proto(UTURN_POINT_SCHEDULING_PREFIX, truck_no,
                                       web_service_pb2.UturnPointVehicleInfo)
        if value:
            return value
        else:
            return None

    def del_uturn_point_info(self):
        return self._redis.delete(UTURN_POINT_SCHEDULING_PREFIX)


if __name__ == '__main__':
    '''
    # test
    tos_command_id = '46'
    lock_name = '10Y1'
    print('query ###################')
    print(LockSchedulingCache().QuerySchedulingStrategy(tos_command_id))

    print('update ###################')
    scheduling_lock = scheduler_pb2.LockResponse()
    scheduling_lock.lock_name = lock_name
    LockSchedulingCache().UpdateLockSchedulingStrategy(tos_command_id, scheduling_lock)
    print(LockSchedulingCache().QuerySchedulingStrategy(tos_command_id))

    print('delete ###################')
    LockSchedulingCache().DeleteSchedulingStrategy(tos_command_id)
    print(LockSchedulingCache().QuerySchedulingStrategy(tos_command_id))
    # print(ShipLoadSchedulingCache().is_governed_by_ship_load_sch('AT801'))

    ShipLoadSchedulingCache().set_ship_load_sch_is_tos_control(True)
    print(ShipLoadSchedulingCache().get_ship_load_sch_is_tos_control())
    ShipLoadSchedulingCache().set_ship_load_sch_is_tos_control(False)
    print(ShipLoadSchedulingCache().get_ship_load_sch_is_tos_control())
    ShipLoadSchedulingCache().set_ship_load_sch_is_tos_control(True)
    time.sleep(3)
    print(ShipLoadSchedulingCache().get_ship_load_sch_is_tos_control())
    '''
    print(ShipLoadSchedulingCache().get_ship_load_config())
    print(ShipLoadSchedulingCache().get_customized_lock_config_of_crane('AT802'))
