import base64
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from common.logger import logger
from cryptography.hazmat.primitives import padding as sym_padding

def get_max_chunk_size(pub_key):
    key_size_bytes = pub_key.key_size // 8
    return key_size_bytes - 2 * hashes.SHA256().digest_size - 2

def rsa_encrypt_long(public_key, plaintext: bytes) -> str:
    key_size = public_key.key_size // 8
    max_chunk_size = key_size - 11
    encrypted_data = b''

    logger.debug(f"[RSA加密] 分块加密开始：总长度 {len(plaintext)}，最大块大小 {max_chunk_size}，共 {((len(plaintext) - 1) // max_chunk_size + 1)} 块")

    for i in range(0, len(plaintext), max_chunk_size):
        chunk = plaintext[i:i + max_chunk_size]
        try:
            encrypted_chunk = public_key.encrypt(
                chunk,
                padding.PKCS1v15()
            )
            encrypted_data += encrypted_chunk
            logger.debug(f"[RSA加密] 第{i // max_chunk_size + 1}块加密成功")
        except Exception as e:
            logger.debug(f"[RSA加密失败] 第{i // max_chunk_size + 1}块失败，错误信息: {e}")
            raise

    encrypted_b64 = base64.b64encode(encrypted_data).decode()
    logger.debug(f"[RSA加密] 加密完成，总加密后长度: {len(encrypted_b64)}（base64）")
    return encrypted_b64


def rsa_decrypt_long(private_key, encrypted_b64_str):
    try:
        encrypted_data = base64.b64decode(encrypted_b64_str)
    except Exception as e:
        logger.debug(f"[Base64解码失败] 错误信息: {e}")
        raise

    key_size = private_key.key_size // 8
    if len(encrypted_data) % key_size != 0:
        logger.debug(f"[警告] 密文长度 {len(encrypted_data)} 不是块大小 {key_size} 的整数倍，可能存在数据错误")

    decrypted_data = b''
    total_chunks = len(encrypted_data) // key_size

    logger.debug(f"[RSA解密] 分块解密开始：总长度 {len(encrypted_data)}，块大小 {key_size}，共 {total_chunks} 块")

    for i in range(0, len(encrypted_data), key_size):
        chunk = encrypted_data[i:i + key_size]
        try:
            decrypted_chunk = private_key.decrypt(
                chunk,
                padding.PKCS1v15()
            )
            decrypted_data += decrypted_chunk
            logger.debug(f"[RSA解密] 第{i // key_size + 1}块解密成功")
        except Exception as e:
            logger.debug(f"[RSA解密失败] 第{i // key_size + 1}块失败，错误信息: {e}")
            raise
    return decrypted_data

# PKCS7 加密
def aes_encrypt_cbc_base64(key: bytes, iv: bytes, plaintext: bytes) -> str:
    try:
        logger.debug(f"[AES加密] 明文长度: {len(plaintext)}，Key: {len(key)}字节，IV: {len(iv)}字节")

        # 1. 填充
        padder = sym_padding.PKCS7(128).padder()
        padded_data = padder.update(plaintext) + padder.finalize()

        # 2. 加密
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        encrypted_bytes = encryptor.update(padded_data) + encryptor.finalize()

        encrypted_b64 = base64.b64encode(encrypted_bytes).decode()
        logger.debug(f"[AES加密] 加密完成，密文长度（base64）: {len(encrypted_b64)}")
        return encrypted_b64

    except Exception as e:
        logger.debug(f"[AES加密失败] 错误信息: {e}")
        raise


# PKCS7 解密
def aes_decrypt_cbc_base64(key: bytes, iv: bytes, encrypted_b64_str: str) -> bytes:
    try:
        encrypted_bytes = base64.b64decode(encrypted_b64_str)
        logger.debug(f"[AES解密] 密文长度: {len(encrypted_bytes)}（解码后）")

        # 1. 解密
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted_padded = decryptor.update(encrypted_bytes) + decryptor.finalize()

        # 2. 去填充
        unpadder = sym_padding.PKCS7(128).unpadder()
        decrypted_data = unpadder.update(decrypted_padded) + unpadder.finalize()

        logger.debug(f"[AES解密] 解密成功，原文长度: {len(decrypted_data)}")
        return decrypted_data

    except Exception as e:
        logger.debug(f"[AES解密失败] 错误信息: {e}")
        raise