import time
from typing import Optional

from cache.client import CacheClient
from proto import cache_pb2

CRANE_CALIB_OFFSET = 'crane-calib-offset'
CRANE_VERIFIED_TIMESTAMP = 'crane-verified-timestamp'
ABRASION_TABLE = 'abrasion-table'
CRANE_NOTICED_INTERVAL_SECOND = 'crane-noticed-interval-second'
ENABLE_AUTO_REVISE_CRANE_CALIB_OFFSET = 'enable-revise-crane-calib-offset'
ENABLE_COLLECT_CRANE_CALIB_DATA = 'enable-collect-crane=calib-data'
# no use


def enable_auto_revise_crane_calib_offset():
    return CacheClient().client().set(ENABLE_AUTO_REVISE_CRANE_CALIB_OFFSET, 1)


def disable_auto_revise_crane_calib_offset():
    return CacheClient().client().delete(ENABLE_AUTO_REVISE_CRANE_CALIB_OFFSET)


def is_auto_revise_crane_calib_offset():
    return CacheClient().client().get(ENABLE_AUTO_REVISE_CRANE_CALIB_OFFSET) is not None


def enable_collect_crane_calib_data():
    CacheClient().set_value(ENABLE_COLLECT_CRANE_CALIB_DATA, 1)


def disable_collect_crane_calib_data():
    return CacheClient().client().delete(ENABLE_COLLECT_CRANE_CALIB_DATA)


def is_enable_collect_crane_calib_data():
    return CacheClient().client().get(ENABLE_COLLECT_CRANE_CALIB_DATA) is not None


def set_crane_verified_timestamp(crane_no, ts):
    key = f"{CRANE_VERIFIED_TIMESTAMP}:{crane_no}"
    CacheClient().set_value(key, ts)


def get_crane_verified_timestamp(crane_no):
    key = f"{CRANE_VERIFIED_TIMESTAMP}:{crane_no}"
    ts = CacheClient().get_value(key, float)
    return ts


def get_crane_calib_timestamp(crane_no):
    proto = CacheClient().get_proto(
        f'{CRANE_CALIB_OFFSET}:{crane_no}', cache_pb2.CraneCalibOffset)
    if proto is not None:
        if proto.HasField('update_timestamp'):
            return proto.update_timestamp
    return None


def set_crane_calib_offset(crane_no, offset):
    proto = cache_pb2.CraneCalibOffset()
    proto.crane_no = crane_no
    proto.offset = offset
    proto.update_timestamp = time.time()
    return CacheClient().set_proto(f'{CRANE_CALIB_OFFSET}:{crane_no}', proto)


def get_crane_calib_offset(crane_no):
    proto = CacheClient().get_proto(
        f'{CRANE_CALIB_OFFSET}:{crane_no}', cache_pb2.CraneCalibOffset)
    return proto.offset if proto is not None else 0


def get_crane_calib_offsets(crane_nos):
    keys = []
    for crane_no in crane_nos:
        keys.append(f'{CRANE_CALIB_OFFSET}:{crane_no}')
    protos = CacheClient().mget_proto(keys, cache_pb2.CraneCalibOffset)
    offsets = []
    for proto in protos:
        offsets.append(proto.offset if proto is not None else 0)
    return offsets

def get_noticed_interval_second():
    ts = CacheClient().get_value(CRANE_NOTICED_INTERVAL_SECOND, int)
    # default value set to 4 hours
    if ts is None:
        ts = 14400
    return ts

def set_noticed_interval_second(interval):
    CacheClient().set_value(CRANE_NOTICED_INTERVAL_SECOND, interval)

def increase_mark_count(mark, inc=1):
    key = f"{ABRASION_TABLE}"
    count = get_mark_count(mark) + inc
    CacheClient().set_field(key, mark, count)


def get_mark_count(mark):
    key = f"{ABRASION_TABLE}"
    mark_state = CacheClient().get_field(key, mark)
    return int(mark_state) if mark_state is not None else 0


def get_abrasion_table():
    key = f"{ABRASION_TABLE}"
    abrasion_table_b = CacheClient().get_dict(key)
    abrasion_table = {}
    for k,v in abrasion_table_b.items():
        mark_state = k.decode('utf-8')
        count = int(v.decode('utf-8'))
        abrasion_table[mark_state] = count

    return abrasion_table


if __name__ == '__main__':
    set_crane_calib_offset(99, 100.111)
    get = get_crane_calib_offset(99)
    print(get)
    get = get_crane_calib_offset(98)
    print(get)
