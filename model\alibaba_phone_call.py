# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys

from typing import List
from Tea.core import TeaCore

from alibabacloud_dyvmsapi20170525.client import Client as Dyvmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dyvmsapi20170525 import models as dyvmsapi_20170525_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient

from common.constant import ALIBABA_CLOUD_PHONE_ACCESS_KEY_ID,ALIBABA_CLOUD_PHONE_ACCESS_KEY_SECRET,ALIBABA_CLOUD_PHONE_ALARM_VOICE_CODE,NB_BACK_WEB_SERVER_01_MAINTAINER_NUMBER,NB_FABU_SERVER_01_MAINTAINER_NUMBER

class AlibabaPhoneCall:
    def __init__(self, access_key_id, access_key_secret):
      self._access_key_id = access_key_id
      self._access_key_secret = access_key_secret

    def create_client(self,
        access_key_id: str,
        access_key_secret: str,
    ) -> Dyvmsapi20170525Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 您的AccessKey ID,
            access_key_id=access_key_id,
            # 您的AccessKey Secret,
            access_key_secret=access_key_secret# 不可泄露
        )
        # 访问的域名
        config.endpoint = 'dyvmsapi.aliyuncs.com'
        return Dyvmsapi20170525Client(config)

    def call(self,called_number,voice_code) -> None:
        client = self.create_client(self._access_key_id, self._access_key_secret)
        single_call_by_voice_request = dyvmsapi_20170525_models.SingleCallByVoiceRequest(
            called_number=called_number, # 这里填手机号，最多支持同时呼叫10个号码
            voice_code=voice_code # 这里填录音ID
        )
        resp = client.single_call_by_voice(single_call_by_voice_request)
        ConsoleClient.log(UtilClient.to_jsonstring(TeaCore.to_map(resp)))

    async def call_async(self,called_number,voice_code) -> None:
        client = self.create_client(self._access_key_id, self._access_key_secret)
        single_call_by_voice_request = dyvmsapi_20170525_models.SingleCallByVoiceRequest(
            called_number=called_number,
            voice_code=voice_code
        )
        resp = await client.single_call_by_voice_async(single_call_by_voice_request)
        ConsoleClient.log(UtilClient.to_jsonstring(TeaCore.to_map(resp)))


if __name__ == '__main__':
    phone_call = AlibabaPhoneCall(ALIBABA_CLOUD_PHONE_ACCESS_KEY_ID, ALIBABA_CLOUD_PHONE_ACCESS_KEY_SECRET)
    print('before call')
    phone_call.call(NB_FABU_SERVER_01_MAINTAINER_NUMBER,ALIBABA_CLOUD_PHONE_ALARM_VOICE_CODE)
    print('after call')
