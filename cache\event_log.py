from common.constant import DEV_ID
from cache.client import CacheClient

LOCAL_EVENT_LOG_QUEUE = "local-event-log-queue:%s" % DEV_ID 

#no use
def push_event_log(value):
    CacheClient().client().lpush(LOCAL_EVENT_LOG_QUEUE, value)


def pop_event_log():
    return CacheClient().client().rpop(LOCAL_EVENT_LOG_QUEUE)


if __name__ == '__main__':
    push_event_log("122")
    print(pop_event_log())
    print(pop_event_log())
