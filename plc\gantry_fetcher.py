import socket
import struct
import time

from proto.cache_pb2 import GantryInfo

msgs = {
    'msg_no': (4701, 1, False),
    'gantry_no': (4702, 1, False),
    'cps_guide': (4703, 1, True),
    'g_pos': (4704, 2, True),#mm
    'h_pos': (4706, 1, True),#mm
    'spr_size': (4707, 1, False),#1:20,3:40,3:invalid
    'spr_state': (4708, 1, False),#true:locked
    't_pos': (4709, 2, True),#mm
    'block_nul': (4711, 1, False),
    'smm1_pos': (4712, 1, True),#mm
}


class GantryFetcher(object):
    def __init__(self, gantry_no, ip, port):
        self._gantry_no = gantry_no
        self._ip = ip
        self._port = port
        self._sock = socket.socket(socket.AF_INET,  # Internet
                                   socket.SOCK_DGRAM)  # UDP
        self._sock.settimeout(0.1)

    def fetch_one(self, arr, addr, size, signed):
        result = 0
        for i in range(addr, addr + size):
            result = result + arr[i] * (2 ** (16 * (i - addr)))
        if signed:
            if result >= (2 ** (16 * size - 1)):
                result = result - (2 ** (16 * size))
        return result

    def fetch(self) -> GantryInfo:
        start = min(msgs[k][0] for k in msgs)
        end = max(msgs[k][0] + msgs[k][1] for k in msgs)
        bin = struct.pack('!BBHH', 1, 3, start, end - start)
        self._sock.sendto(bin, (self._ip, self._port))
        received = self._sock.recvfrom(1024)[0]
        if received[2] != (end - start) * 2:
            raise Exception(f"Gantry fetch size error,{received[2]}:{(end - start) * 2}, {self._gantry_no}")

        rep_str = '!BBB' + 'H' * (end - start) + 'BB'
        arr = struct.unpack(rep_str, received)[3:]

        result = GantryInfo()
        result.cps_guide = 20000
        for k in msgs:
            v = msgs[k]
            setattr(result, k, self.fetch_one(arr, v[0] - start, v[1], v[2]))
        result.recv_timestamp = int(time.time() * 1000000000)
        return result

    def ip(self):
        return self._ip
