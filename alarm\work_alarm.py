import datetime
import json
import math
import time
import traceback
from typing import Dict, List

from google.protobuf.text_format import Message<PERSON><PERSON><PERSON><PERSON>

from cache import vehicle_cache
from cache.command_cache import CommandCache
from cache.config_cache import ConfigCache
from cache.event_cache import EventCache
from cache.map_cache import Map<PERSON>ache
from cache.remote_cache import get_remote_crane
from common.common_util import MyJosnEncoder
from common.constant import (
    CRANE_LANE_THETA,
    DIST_FROM_CENTER_TO_COUPLING_POINT,
    STACKER_VERTICAL_YARDS,
)
from common.logger import logger
from common.periodical_task import PeriodicalTask
from common.singleton import Singleton
from proto.antenna_pb2 import (
    MapShip,
    Position,
    RemoteCommand,
    VehicleStatus,
    VesselDirection,
)
from proto.cache_pb2 import (
    CommandWithStatus,
    OddConfig,
    StartWorkAlarm,
    StartWorkAlarmConfig,
    StartWorkAlarmConfigs,
)
from tos_db import TosDb

"""
    车辆开工配置和ODD配置报警:
    1. 开工配置 start_work
    2. odd配置 odd
"""


class WorkAlarmBase:
    def __init__(self):
        self._running = False
        self._vehicle_cache = vehicle_cache
        self._config_cache = ConfigCache()
        self._event_cache = EventCache()
        self._command_cache = CommandCache()
        self._map_cache = MapCache()
        self._db = TosDb()
        self._work_alarm_interval_time: float = 1000
        self._work_alarm_loop_thread = None
        self._crane_lane_theta: float = CRANE_LANE_THETA
        self._dist_from_center_to_coupling_point: float = (
            DIST_FROM_CENTER_TO_COUPLING_POINT
        )
        self._tos_dipatch_threhold: float = 10  # 10s

    def start(self):
        if not self._running:
            self._work_alarm_loop_thread = PeriodicalTask(
                target=self._work_alarm_loop,
                interval=self._work_alarm_interval_time / 1000,
                master_only=True,
            )
            self._work_alarm_loop_thread.start()
            self._running = True

    def make_start_work_alarm(
        self,
        vehicle_name: str,
        alarm_type: StartWorkAlarm.AlarmType,
        wi_id=None,
        to_pos=None,
        lane_id=None,
        vessel_direction=None,
    ):
        logger.debug(
            f"vehicle_name:{vehicle_name} make alarm:{StartWorkAlarm.AlarmType.Name(alarm_type)}!!!"
        )
        alarm = StartWorkAlarm()
        alarm.vehicle_name = vehicle_name
        alarm.timestamp_ms = int(time.time() * 1000)
        alarm.type = alarm_type
        detail_dict = dict()
        if wi_id is not None:
            detail_dict["ID"] = wi_id
        if to_pos is not None:
            detail_dict["TO_POS"] = to_pos
        if lane_id is not None:
            detail_dict["LANE_ID"] = lane_id
        if vessel_direction is not None:
            detail_dict["VESSEL_DIRECTION"] = vessel_direction
        alarm.detail = json.dumps(detail_dict, cls=MyJosnEncoder)
        return alarm

    def _check_vehicle_in_config_fleet(
        self,
        vehicle_name: str,
        vehicle_fleet: str,
        vehicle_config: StartWorkAlarmConfig.VehicleConfig,
    ):
        if vehicle_fleet is None or vehicle_fleet not in vehicle_config.vehicle_fleets:
            logger.debug(
                f"[_check_vehicle_in_config_fleet]:vehicle_name:{vehicle_name} not in config vehicle_fleets"
                f" in vehicle_fleet:{vehicle_fleet}"
            )
            return False
        logger.debug(
            f"[_check_vehicle_in_config_fleet]:vehicle_name:{vehicle_name} in config vehicle_fleets:{vehicle_config.vehicle_fleets}"
            f" in vehicle_fleet:{vehicle_fleet}"
        )
        return True

    def _need_check_start_work_alarm(
        self,
        vehicle_name: str,
        vehicle_status: VehicleStatus,
        vehicle_config: StartWorkAlarmConfig.VehicleConfig,
    ):
        if vehicle_status is None:
            logger.warning(
                f"@@@[need_check_start_work_alarm]:NO NEED! vehicle_name:{vehicle_name} invalid vehicle_status:{vehicle_status}"
            )
            return False
        logger.debug(
            f"@@@[need_check_start_work_alarm]:vehicle_name:{vehicle_name},business_scene:{vehicle_status.vehicle_mode.business_scene}"
        )
        if (
            vehicle_status.vehicle_mode.business_scene
            not in vehicle_config.business_scenes
        ):
            logger.debug(
                f"[need_check_start_work_alarm]:NO NEED! vehicle_name:{vehicle_name},business_scene:{vehicle_status.vehicle_mode.business_scene}"
                f" not in config business_scenes:{vehicle_config.business_scenes}"
            )
            return False
        versions = [
            host_info.version for host_info in vehicle_status.device_info.host_info
        ]
        logger.debug(
            f"[need_check_start_work_alarm]:vehicle_name:{vehicle_name},versions:{versions}"
        )
        if len(versions) == 0:
            logger.debug(
                f"[need_check_start_work_alarm]:NO NEED! vehicle_name:{vehicle_name} have no versions:{versions}"
            )
            return False
        for version in versions:
            if version not in vehicle_config.versions:
                logger.debug(
                    f"[need_check_start_work_alarm]:NO NEED! vehicle_name:{vehicle_name} versions:{versions}"
                    f" not in config versions:{vehicle_config.versions}"
                )
                return False
        logger.debug(
            f"[need_check_start_work_alarm]:NEED! vehicle_name:{vehicle_name} need check start work alarm"
        )
        return True

    def _parse_crane_in_vessel(
        self,
        vehicle_name: str,
        crane_no: str,
        theta_cos: float,
        theta_sin: float,
        crane_pos: Position,
        ship: MapShip,
    ):
        ret = False
        crane_direction = None
        # 偏移至基准桥吊小车
        crane_center_pos_utm_x = (
            crane_pos.utm_x - self._dist_from_center_to_coupling_point * theta_cos
        )
        crane_center_pos_utm_y = (
            crane_pos.utm_y - self._dist_from_center_to_coupling_point * theta_sin
        )
        prow_map_x = ship.prow.utm_x * theta_cos + ship.prow.utm_y * theta_sin
        stern_map_x = ship.stern.utm_x * theta_cos + ship.stern.utm_y * theta_sin
        crane_center_map_pos_x = (
            crane_center_pos_utm_x * theta_cos + crane_center_pos_utm_y * theta_sin
        )
        logger.debug(
            f"[parse_crane_in_vessel] vehicle_name {vehicle_name},crane_no:{crane_no},crane_pos:({crane_pos.utm_x},{crane_pos.utm_y})"
            f",crane_center_pos:({crane_center_pos_utm_x},{crane_center_pos_utm_y}),crane_center_map_pos_x:{crane_center_map_pos_x})"
            f",prow:({ship.prow.utm_x},{ship.prow.utm_y}),prow_map_x:{prow_map_x}"
            f",stern:({ship.stern.utm_x},{ship.stern.utm_y}),stern_map_x:{stern_map_x}"
        )
        if (
            crane_center_map_pos_x >= prow_map_x
            and crane_center_map_pos_x <= stern_map_x
        ) or (
            crane_center_map_pos_x <= prow_map_x
            and crane_center_map_pos_x >= stern_map_x
        ):
            crane_direction = (
                VesselDirection.RIGHT
                if ship.prow.utm_x < ship.stern.utm_x
                else VesselDirection.LEFT
            )
            ret = True
        return (ret, crane_direction)

    def _assume_crane_vessel_direction(
        self, vehicle_name: str, crane_no: str, ships: List[MapShip]
    ):
        crane_direction = None
        ret = True
        if len(ships) == 0:
            logger.debug(
                f"[asssume_crane_vessel_direction] vehicle_name {vehicle_name},crane_no:{crane_no},no ships"
            )
            return (ret, crane_direction)
        crane = get_remote_crane(int(crane_no))
        crane_pos = (
            crane.approximate_point
            if (crane is not None and crane.approximate_point is not None)
            else None
        )
        if crane_pos is not None and crane and crane.crane_stable:
            theta_cos = math.cos(self._crane_lane_theta)
            theta_sin = math.sin(self._crane_lane_theta)
            for ship in ships:
                (is_find, crane_direction) = self._parse_crane_in_vessel(
                    vehicle_name, crane_no, theta_cos, theta_sin, crane_pos, ship
                )
                if is_find:
                    logger.debug(
                        f"[asssume_crane_vessel_direction] vehicle_name {vehicle_name}, crane_no:{crane_no},"
                        f"crane_direction:{VesselDirection.Name(crane_direction)} "
                        f"FIND IN SHIP:{MessageToString(ship,as_one_line=True)}"
                    )
                    break
        elif crane_pos is None:
            logger.debug(
                f"[asssume_crane_vessel_direction] vehicle_name {vehicle_name}, crane_no:{crane_no},crane_pos is null"
            )
        else:
            logger.debug(
                f"[asssume_crane_vessel_direction] vehicle_name {vehicle_name},crane_no:{crane_no},crane_pos is {crane_pos} or not stable"
            )
            ret = False
        return (ret, crane_direction)

    def _check_crane_command_valid_alarm(
        self,
        vehicle_name: str,
        command_context: RemoteCommand.CommandContext,
        crane_configs: List[StartWorkAlarmConfig.CraneConfig],
        ships: List[MapShip],
    ):
        alarms = []
        configs = {str(config.crane_no): config for config in crane_configs}
        crane_no = command_context.transfer_point[2:]
        if len(configs) == 0 or (crane_no not in configs):
            alarm = self.make_start_work_alarm(
                vehicle_name,
                StartWorkAlarm.CRANE_MISMATCH,
                command_context.tos_command_id,
                command_context.transfer_point,
            )
            logger.debug(
                f"[check_crane_command_valid_alarm]:vehicle_name {vehicle_name},crane_no:{crane_no},"
                f"make alarm:{MessageToString(alarm,as_one_line=True)}"
            )
            alarms.append(alarm)
            return alarms
        config = configs[crane_no]
        if command_context.lane_id not in config.lane_ids:
            alarm = self.make_start_work_alarm(
                vehicle_name,
                StartWorkAlarm.CRANE_LANE_MISMATCH,
                command_context.tos_command_id,
                command_context.transfer_point,
                command_context.lane_id,
            )
            logger.debug(
                f"[check_crane_command_valid_alarm]:vehicle_name {vehicle_name},lane_id:{command_context.lane_id},"
                f"make alarm:{MessageToString(alarm,as_one_line=True)}"
            )
            alarms.append(alarm)

        if command_context.vessel_direction != config.vessel_direction:
            alarm = self.make_start_work_alarm(
                vehicle_name,
                StartWorkAlarm.CRANE_VESSEL_MISMATCH,
                command_context.tos_command_id,
                command_context.transfer_point,
                command_context.lane_id,
                VesselDirection.Name(command_context.vessel_direction),
            )
            logger.debug(
                f"[check_crane_command_valid_alarm]:vehicle_name {vehicle_name},"
                f"tos vessel_direction:{command_context.vessel_direction},config vessel_direction:{config.vessel_direction},"
                f"make alarm:{MessageToString(alarm,as_one_line=True)}"
            )
            alarms.append(alarm)
        else:
            (ret, assume_vessel_direction) = self._assume_crane_vessel_direction(
                vehicle_name, crane_no, ships
            )
            if ret and (
                assume_vessel_direction != config.vessel_direction
                or assume_vessel_direction != command_context.vessel_direction
            ):
                alarm = self.make_start_work_alarm(
                    vehicle_name,
                    StartWorkAlarm.CRANE_VESSEL_MISMATCH,
                    command_context.tos_command_id,
                    command_context.transfer_point,
                    command_context.lane_id,
                    VesselDirection.Name(command_context.vessel_direction),
                )
                logger.debug(
                    f"[check_crane_command_valid_alarm]:vehicle_name {vehicle_name},vessel_direction:{assume_vessel_direction},"
                    f"tos vessel_direction:{command_context.vessel_direction},config vessel_direction:{config.vessel_direction},"
                    f"make alarm:{MessageToString(alarm,as_one_line=True)}"
                )
                alarms.append(alarm)
        return alarms

    def _parse_yard_bay(self, vehicle_name: str, destination: str):
        # 如果是给FLD直接不考虑
        # TODO 屏蔽堆高机场地
        invalid_yards = ["FLD"]
        if destination in invalid_yards:
            return (False, destination, "")
        else:
            return (True, destination[:2], destination[2:4])

    def _check_yard_command_valid_alarm(
        self,
        vehicle_name: str,
        command_context: RemoteCommand.CommandContext,
        yard_configs: List[StartWorkAlarmConfig.YardConfig],
    ):
        alarms = []
        (valid, yard_no, yard_bay) = self._parse_yard_bay(
            vehicle_name, command_context.transfer_point
        )
        logger.debug(
            f"[check_yard_command_valid_alarm]:vehicle_name:{vehicle_name},valid:{valid},"
            f"yard_no:{yard_no},yard_bay:{yard_bay},transfer_point:{command_context.transfer_point}"
        )
        if valid:
            yard_configs_nos = [
                yard for yard_config in yard_configs for yard in yard_config.yards
            ]
            if yard_no not in yard_configs_nos:
                alarm = self.make_start_work_alarm(
                    vehicle_name,
                    StartWorkAlarm.YARD_NO_MISMATCH,
                    command_context.tos_command_id,
                    command_context.transfer_point,
                )
                logger.debug(
                    f"[check_yard_command_valid_alarm]:make alarm:{MessageToString(alarm,as_one_line=True)}"
                )
                alarms.append(alarm)
            # len(yard_bay) = 0 说明是不带贝位的
            for yard_config in yard_configs:
                if yard_no in yard_config.yards:
                    if (
                        yard_config.HasField("max_bay")
                        and yard_config.HasField("min_bay")
                        and len(yard_bay) > 0
                        and (
                            int(yard_bay) > yard_config.max_bay
                            or int(yard_bay) < yard_config.min_bay
                        )
                    ):
                        alarm = self.make_start_work_alarm(
                            vehicle_name,
                            StartWorkAlarm.YARD_BAY_MISMATCH,
                            command_context.tos_command_id,
                            command_context.transfer_point,
                        )
                        logger.debug(
                            f"[check_yard_command_valid_alarm]:make alarm:{MessageToString(alarm,as_one_line=True)}"
                        )
                        alarms.append(alarm)
        return alarms

    def _check_command_valid_alarm(
        self,
        vehicle_name: str,
        commands: List[CommandWithStatus],
        config: StartWorkAlarmConfig,
        ships: List[MapShip],
    ):
        if len(commands) == 0:
            logger.debug(
                f"[check_command_valid_alarm] vehicle_name:{vehicle_name} has no commands"
            )
            return []
        command = commands[0].command
        logger.debug(
            f"[check_command_valid_alarm] vehicle_name:{vehicle_name},id:{command.command_context.tos_command_id},"
            f"transfer_point:{command.command_context.transfer_point},command_type:{RemoteCommand.CommandType.Name(command.type)},"
            f"vessel_direction:{command.command_context.vessel_direction},command_type:{command.command_context.lane_id},"
            f"command_source_type:{RemoteCommand.CommandSourceType.Name(command.command_source_type)},"
            f"command_idx_type:{RemoteCommand.CommandContext.CommandIdxType.Name(command.command_context.command_idx_type)},"
            f"action_type:{RemoteCommand.CommandContext.ActionType.Name(command.command_context.action_type)}"
        )
        if (
            command.command_source_type == RemoteCommand.CommandSourceType.REMOTE
            or command.type == RemoteCommand.CANCEL
            or (
                command.type == RemoteCommand.STOP
                and command.command_context.command_idx_type
                == RemoteCommand.CommandContext.LAST_IDX
            )
            or (
                command.command_context.action_type != RemoteCommand.CommandContext.LOAD
                and command.command_context.action_type
                != RemoteCommand.CommandContext.UNLOAD
            )
        ):
            logger.debug(
                f"[check_command_valid_alarm] vehicle_name:{vehicle_name} no need to check command valid"
            )
            return []
        alarms: List[StartWorkAlarm] = []
        transfer_point = command.command_context.transfer_point
        if transfer_point.startswith("CR"):
            alarms = alarms + self._check_crane_command_valid_alarm(
                vehicle_name, command.command_context, list(config.crane_configs), ships
            )
        else:
            alarms = alarms + self._check_yard_command_valid_alarm(
                vehicle_name, command.command_context, list(config.yard_configs)
            )
        return alarms

    def _is_tos_work_over(self, vehicle_name: str, wi: Dict):
        wi_status = wi.get("WI_STATUS")
        confirmed_time = wi.get("CONFIRMED_TIME")
        cancel_time = wi.get("CANCEL_TIME")
        truck_status = wi.get("TRUCK_STATUS")
        truck_update_time = wi.get("TRUCK_UPDATE_TIME")
        tos_id = wi.get("ID")
        threhold_time = datetime.datetime.now() - datetime.timedelta(
            seconds=self._tos_dipatch_threhold
        )
        logger.debug(
            f"[is_tos_work_over] vehicle_name:{vehicle_name} id:{tos_id},wi_status:{wi_status},"
            f"confirmed_time:{confirmed_time}, cancel_time:{cancel_time},truck_status:{truck_status},"
            f"truck_update_time:{truck_update_time},threhold_time:{threhold_time}"
        )
        confirmed_time = (
            confirmed_time
            if confirmed_time is not None
            else (threhold_time - datetime.timedelta(seconds=1))
        )
        cancel_time = (
            cancel_time
            if cancel_time is not None
            else (threhold_time - datetime.timedelta(seconds=1))
        )
        truck_update_time = (
            truck_update_time
            if truck_update_time is not None
            else (threhold_time - datetime.timedelta(seconds=1))
        )
        if wi_status == "FINISH" and confirmed_time < threhold_time:
            return True
        elif wi_status == "CANCEL" and cancel_time < threhold_time:
            return True
        elif truck_status == "FINISH" and truck_update_time < threhold_time:
            logger.debug(
                f"[is_tos_work_over] vehicle_name:{vehicle_name} should not happen,wi_status is not finish but truck_status is finish!!"
            )
            return False
        else:
            return False

    def _check_have_dispatch_tos(
        self, vehicle_name: str, command_context: RemoteCommand.CommandContext
    ):
        ret = True
        need_check = False
        wis = []
        wi_id = command_context.tos_command_id
        if (
            wi_id > 0
            and command_context.vms_type != RemoteCommand.CommandContext.FABU
            and ((wi := self._db.get_wi_info_by_id(vehicle_name, wi_id)) is not None)
        ):
            if self._is_tos_work_over(vehicle_name, wi):
                need_check = True
        else:  # wid_id <= 0 or wi is None:
            need_check = True
        if need_check:
            wis = self._db.get_dispatch_wis(vehicle_name)
            ret = len(wis) > 0
        new_ids = [new_wi.get("ID") for new_wi in wis]
        logger.debug(
            f"[check_have_dispatch_tos] vehicle_name:{vehicle_name},wi_id:{wi_id},need_check:{need_check},"
            f"get wis num:{len(wis)},have?:{ret},new_ids:{new_ids}"
        )
        return ret

    def _check_dispatch_tos_alarm(
        self,
        vehicle_name: str,
        commands: List[CommandWithStatus],
        vehicle_status: VehicleStatus,
    ):
        if VehicleStatus.NORMAL_WORK != vehicle_status.work_mode:
            logger.debug(
                f"[check_dispatch_tos_alarm] vehicle_name:{vehicle_name},vehicle status work mode:"
                f"{VehicleStatus.WorkMode.Name(vehicle_status.work_mode)} is not NORMAL_WORK"
            )
            return []
        if len(commands) > 0:
            command = commands[0]
            logger.debug(
                f"[check_dispatch_tos_alarm] vehicle_name:{vehicle_name} have commands now,use current command"
            )
        else:
            command = self._command_cache.get_last_command(vehicle_name)
            logger.debug(
                f"[check_dispatch_tos_alarm] vehicle_name:{vehicle_name} have commands now,use last command"
            )
        alarms = []
        if command is not None:
            command_context = command.command.command_context
            if not self._check_have_dispatch_tos(vehicle_name, command_context):
                alarm = self.make_start_work_alarm(
                    vehicle_name,
                    StartWorkAlarm.TOS_NO_DISPATCH,
                    command_context.tos_command_id,
                )
                logger.debug(
                    f"[check_dispatch_tos_alarm]:make alarm:{MessageToString(alarm,as_one_line=True)}"
                )
                alarms.append(alarm)
        else:
            logger.debug(
                f"[check_dispatch_tos_alarm] vehicle_name:{vehicle_name} have no command at all!!"
            )
            alarm = self.make_start_work_alarm(
                vehicle_name, StartWorkAlarm.TOS_NO_DISPATCH, "None"
            )
            logger.debug(
                f"[check_dispatch_tos_alarm]:make alarm:{MessageToString(alarm,as_one_line=True)}"
            )
            alarms.append(alarm)
        return alarms

    def _check_odd_alarm(
        self,
        vehicle_name: str,
        commands: List[CommandWithStatus],
        config: StartWorkAlarmConfig,
        vehicle_status: VehicleStatus,
    ):
        if VehicleStatus.NORMAL_WORK != vehicle_status.work_mode:
            logger.debug(
                f"[check_odd_alarm] vehicle_name:{vehicle_name},vehicle status work mode:"
                f"{VehicleStatus.WorkMode.Name(vehicle_status.work_mode)} is not NORMAL_WORK"
            )
            return []
        if len(commands) == 0:
            logger.debug(
                f"[check_odd_alarm] vehicle_name:{vehicle_name} has no commands"
            )
            return []

        command = commands[0].command
        logger.debug(
            f"[check_odd_alarm] vehicle_name:{vehicle_name},id:{command.command_context.tos_command_id},"
            f"transfer_point:{command.command_context.transfer_point},command_type:{RemoteCommand.CommandType.Name(command.type)},"
            f"vessel_direction:{command.command_context.vessel_direction},command_type:{command.command_context.lane_id},"
            f"command_source_type:{RemoteCommand.CommandSourceType.Name(command.command_source_type)},"
            f"command_idx_type:{RemoteCommand.CommandContext.CommandIdxType.Name(command.command_context.command_idx_type)},"
            f"action_type:{RemoteCommand.CommandContext.ActionType.Name(command.command_context.action_type)}"
        )
        if (
            command.command_source_type == RemoteCommand.CommandSourceType.REMOTE
            or command.type == RemoteCommand.CANCEL
            or (
                command.type == RemoteCommand.STOP
                and command.command_context.command_idx_type
                == RemoteCommand.CommandContext.LAST_IDX
            )
            or (
                command.command_context.action_type != RemoteCommand.CommandContext.LOAD
                and command.command_context.action_type
                != RemoteCommand.CommandContext.UNLOAD
            )
        ):
            logger.debug(
                f"[check_odd_alarm] vehicle_name:{vehicle_name} no need to check command valid"
            )
            return []
        alarms: List[StartWorkAlarm] = []
        transfer_point = command.command_context.transfer_point
        if transfer_point.startswith("CR"):
            pass
        else:
            alarms = alarms + self._check_yard_command_valid_alarm(
                vehicle_name, command.command_context, list(config.yard_configs)
            )
        return alarms

    def _get_vehicle_start_work_config_map(
        self, vehicle_fleet_map: Dict[str, str], configs: List[StartWorkAlarmConfig]
    ):
        vehicle_config_map = {}
        for vehicle_name, vehicle_fleet in vehicle_fleet_map.items():
            for config in configs:
                if self._check_vehicle_in_config_fleet(
                    vehicle_name, vehicle_fleet, config.vehicle_config
                ):
                    vehicle_config_map[vehicle_name] = config
                    break
        return vehicle_config_map

    def _get_vehicle_odd_config_map(
        self, vehicle_fleet_map: Dict[str, str], config: OddConfig
    ):
        vehicle_config_map = {}
        for vehicle_name, vehicle_fleet in vehicle_fleet_map.items():
            odd_yards: List[str] = []
            fleet_config = config.fleet_configs.get(vehicle_fleet)
            # 有车队使用车队和全局交集，无车队用全局
            global_config = config.global_config
            if fleet_config:
                odd_yards.extend(set(fleet_config.yards) & set(global_config.yards))
            else:
                odd_yards.extend(global_config.yards)
            if not odd_yards:
                continue
            vehicle_config = StartWorkAlarmConfig()
            yard_config = vehicle_config.yard_configs.add()
            yard_config.yards.extend(odd_yards)
            vehicle_config_map[vehicle_name] = vehicle_config
        return vehicle_config_map

    def _work_alarm_loop(self):
        start = time.time()
        start_work_configs = self._config_cache.get_start_work_alarm_config()
        start_work_configs = (
            list(
                filter(
                    lambda config: config and config.enable, start_work_configs.configs
                )
            )
            if start_work_configs
            else []
        )
        odd_config = self._config_cache.get_odd_config()
        if len(start_work_configs) > 0 or odd_config:
            logger.debug(
                f"[work_alarm_loop] start_work_configs:{[MessageToString(start_work_config, as_one_line=True) for start_work_config in start_work_configs]}\n"
                f"odd_config:{MessageToString(odd_config, as_one_line=True)}"
            )
            vehicle_fleet_map: Dict[str, str] = {
                vehicle_name: str(fleet_id)
                for vehicle_name, fleet_id in self._config_cache.get_vehicle_fleet_map(
                    sorted(self._vehicle_cache.get_online_trucks())
                ).items()
            }
            vehicle_start_work_config_map = self._get_vehicle_start_work_config_map(
                vehicle_fleet_map, start_work_configs
            )
            ships = (
                self._map_cache.get_all_map_ship()
                if len(vehicle_start_work_config_map) > 0
                else []
            )
            vehicle_odd_config_map = self._get_vehicle_odd_config_map(
                vehicle_fleet_map, odd_config
            )
            for vehicle_name, fleet_id in vehicle_fleet_map.items():
                try:
                    alarms = []
                    commands, vehicle_status = None, None
                    if (
                        config := vehicle_start_work_config_map.get(vehicle_name)
                    ) is not None:
                        vehicle_status = self._vehicle_cache.get_vehicle_status(
                            vehicle_name
                        )
                        if self._need_check_start_work_alarm(
                            vehicle_name, vehicle_status, config.vehicle_config
                        ):
                            commands = self._command_cache.query_commands(vehicle_name)
                            alarms = alarms + self._check_command_valid_alarm(
                                vehicle_name, commands, config, ships
                            )
                            alarms = alarms + self._check_dispatch_tos_alarm(
                                vehicle_name, commands, vehicle_status
                            )
                    if (config := vehicle_odd_config_map.get(vehicle_name)) is not None:
                        if commands is None:
                            commands = self._command_cache.query_commands(vehicle_name)
                        if vehicle_status is None:
                            vehicle_status = self._vehicle_cache.get_vehicle_status(
                                vehicle_name
                            )
                        alarms = alarms + self._check_odd_alarm(
                            vehicle_name, commands, config, vehicle_status
                        )
                    self._event_cache.update_start_work_alarms(vehicle_name, alarms)
                except Exception as e:
                    logger.warning(
                        f"[work_alarm_loop] vehicle_name:{vehicle_name} start work alarm error: {e}, trace:{traceback.format_exc()}"
                    )
        interval_time = time.time() - start
        logger.debug(f"[work_alarm_loop] cost time:{interval_time} s")
        if interval_time > 1.0:
            logger.warning(
                f"[work_alarm_loop] over cost execeed round time:{round(interval_time)}, execeed time:{interval_time}"
            )


class WorkAlarm(WorkAlarmBase, metaclass=Singleton):
    pass
