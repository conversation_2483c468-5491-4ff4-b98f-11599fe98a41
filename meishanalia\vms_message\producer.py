import os
import time
import json
import traceback
from threading import Thread

from meishanalia.common.constant import KAFKA_NB_VMS_SERVERS_LIST,VMS_CONTROL_TYPE,VMS_CRANE_TYPE,VMS_TRUCK_TYPE,VMS_TRUCK_REQUEST_TYPE,VMS_WI_TYPE,VMS_AHT_TRUCK_INFO_TOPIC,VMS_AHT_CONTROL_INFO_TOPIC
from common.logger import logger,init_vms_producer_log
from common.singleton import Singleton
from model.kafka_server import KafkaMsgProducer,KafkaMsgPartitioner
from common.periodical_task import PeriodicalTask
from meishanalia.vms_message.msgs import from_json, VmsMsg
from meishanalia.vms_message.vms_control import vms_control_handler
from meishanalia.vms_message.vms_crane import vms_crane_handler
from meishanalia.vms_message.vms_truck import vms_truck_handler
from meishanalia.vms_message.vms_wi import vms_wi_handler
from meishanalia.vms_message.vms_request import vms_request_handler
from config.config_manage import ConfigManage

class MyEncoder(json.JSONEncoder):
    def default(self, o):
        return json.loads(json_format.MessageToJson(o,including_default_value_fields=True,preserving_proto_field_name=True))

class VmsProducers(metaclass=Singleton):
    def __init__(self):
        # 梅山非开发环境都打开kafka功能
        if ConfigManage().is_iecs_scene() and not ConfigManage().is_dev_env():
            self._server = KAFKA_NB_VMS_SERVERS_LIST
            self._topic = VMS_AHT_TRUCK_INFO_TOPIC
            self._interval_time = 1000 #ms
            self._partitioner_count = 0
            self._handlers = {}
            self._producer = None
            self._producer_thread = PeriodicalTask(target=self.__handle_message, interval=1, master_only=True)

    def __init_connect(self):
        #logger.info(f'kafak producer server {self._server}')
        self._producer = KafkaMsgProducer(self._server)
        producer_partitioner = KafkaMsgPartitioner(self._server, self._topic)
        if self._partitioner_count != 0:
            if producer_partitioner.set_partitioner(self._partitioner_count):
                logger.info(f'set partitioner:{self._partitioner_count} count sucessfully')
            else:
                logger.warning(f'set partioner:{self._partitioner_count} count failed!!')

    def __handle_message(self):
        if self._producer is not None and self._producer.connect():
            start = time.time()
            for msg_type in self._handlers:
                msgs = self._handlers[msg_type]()
                for msg in msgs:
                    try:
                        msg_key = msg[0]
                        msg_context = msg[1]
                        logger.info(f'vmsproducer:send topic:{self._topic} msg_key:{msg_key},msg_context:{msg_context}')
                        self._producer.send(self._topic, msg_context, msg_key)
                    except Exception as e:
                        logger.warning(f"[handle_message] err:{e}, trace:{traceback.format_exc()}")
            interval_time = (time.time() - start) * 1000
            if interval_time > 500:
                logger.warning(f'[handle_message] send overtime:{interval_time} ms')
        else:
            logger.warning('[handle_message] err:fail to connect')


    def __register_handler(self, typ: str, handler):
        self._handlers[typ] = handler

    def start_producer(self):
        # 梅山非开发环境都打开kafka功能
        if ConfigManage().is_ms_scene() and not ConfigManage().is_dev_env():
            # self.__register_handler(VMS_TRUCK_TYPE, vms_truck_handler)
            self.__register_handler(VMS_TRUCK_REQUEST_TYPE, vms_request_handler)
            self.__init_connect()
            self._producer_thread.start()
        else:
            logger.info('no need to vms start producer')


if __name__ == '__main__':
    init_vms_producer_log()
    VmsProducers().start_producer()
