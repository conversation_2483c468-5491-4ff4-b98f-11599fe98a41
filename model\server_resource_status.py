import time
import traceback
import random
from sqlalchemy import Column, Integer, String, BIGINT, Float
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession


Base = declarative_base()


class ServerResourceStatus(Base):
    __tablename__ = 'server_resource_status'
    id = Column('id', Integer, primary_key=True, autoincrement=True)
    name = Column('name', String(255))
    cpu_used = Column('cpu_used', Float)
    memory_used = Column('memory_used', Float)
    disk_used = Column('disk_used', Float)
    network_used = Column('network_used', Float)
    create_time = Column('create_time', BIGINT) #long ms
    update_time = Column('update_time', BIGINT) #long ms

def get_one():
    session = MysqlSession().acquire()
    try:
        info = session.query(ServerResourceStatus).first()
        return info
    finally:
        session.close()

def query_by_name(name):
    session = MysqlSession().acquire()
    try:
        info = session.query(ServerResourceStatus).filter(ServerResourceStatus.name == name).first()
        return info
    except Exception as e:
        logger.warning(f"Insert query_by_name err{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def update_by_name(name,cpu_used, memory_used, disk_used, network_used):
    start = time.time()
    session = MysqlSession().acquire()
    try:
        update_column = {"cpu_used":cpu_used, "memory_used":memory_used, "disk_used":disk_used, "network_used":network_used, "update_time":int(time.time() * 1000)}
        res = session.query(ServerResourceStatus).filter(ServerResourceStatus.name == name).update(update_column)
        session.commit()
        return res
    except Exception as e:
        logger.warning(f"Insert update_by_name err{e}, trace:{traceback.format_exc()}")
        return 0
    finally:
        session.close()
    end = time.time()
    if (end - start) > 1.0:
        logger.warning(f"ServerResourceStatus update_by_name over cost execeed round time:{round(end - start)}, execeed time:{end - start}")

def insert(name, cpu_used, memory_used, disk_used, network_used):
    session = MysqlSession().acquire()
    try:
        status = ServerResourceStatus(name=name,
                              cpu_used=cpu_used,
                              memory_used=memory_used,
                              disk_used=disk_used,
                              network_used=network_used,
                              create_time=int(time.time() * 1000),
                              update_time=int(time.time() * 1000))
        logger.info(f"Insert ServerResourceStatus :{status.__dict__}")
        session.add(status)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert ServerResourceStatus err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()


def create_talbe():
    import pymysql
    pymysql.install_as_MySQLdb()
    from sqlalchemy_utils import database_exists, create_database, drop_database
    print(f"now start create_talbe ServerResourceStatus....")
    engine=MysqlSession().engine()
    # 1, 删除数据库
    # if  database_exists(engine.url):
    #     drop_database(engine.url)

    # 2, 创建新数据库
    if  not database_exists(engine.url):
        create_database(engine.url)

    ServerResourceStatus.metadata.drop_all(engine) # Create a table with multiple items
    ServerResourceStatus.metadata.create_all(engine)
    print(f"now end create_talbe ServerResourceStatus....")

  
if __name__ == '__main__':
    #insert("fabu01",0.1,1.1,2.1,4.1)
    #create_talbe()
    while True:
        cpu_used = random.random()
        memory_used = random.random()
        disk_used = random.random()
        network_used = random.random()
        ret = update_by_name("fabu01", cpu_used, memory_used, disk_used, network_used)
        print('ret:' + str(ret))
        info = query_by_name("fabu01")
        if info is not None:
            print(info.__dict__)
        else:
            print('get one query_by_name')
        time.sleep(1)
