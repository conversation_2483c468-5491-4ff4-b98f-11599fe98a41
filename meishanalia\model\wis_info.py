import traceback
import sys
import time
from typing import Dict
import datetime
import sqlalchemy
from sqlalchemy import Column, Integer, String, DateTime,and_,or_

from common.logger import logger
from meishanalia.communication.db import TosSession
from meishanalia.common.constant import TABLE_TOS_CMD
from meishanalia.model.table_info import TosWisInfo, DcWiTnfo


def query_execute(vehicle_name, time_delta=2):
    session = TosSession().acquire()
    try:
        sql = session.query(TosWisInfo, DcWiTnfo.TRUCK_STATUS, DcWiTnfo.UPDATE_TIME).\
                outerjoin(DcWiTnfo,DcWiTnfo.ID == TosWisInfo.ID).\
                filter(TosWisInfo.TRUCK_NO == vehicle_name).\
                filter(or_(TosWisInfo.INSERT_TIME > datetime.datetime.now() - datetime.timedelta(hours=time_delta), TosWisInfo.UPDATE_TIME > datetime.datetime.now() - datetime.timedelta(hours = time_delta),
                    DcWiTnfo.INSERT_TIME > datetime.datetime.now() - datetime.timedelta(hours=time_delta), DcWiTnfo.UPDATE_TIME > datetime.datetime.now() - datetime.timedelta(hours = time_delta))).\
                filter(or_(TosWisInfo.WI_STATUS == 'DISPATCH', TosWisInfo.WI_STATUS == 'CANCEL'),or_(TosWisInfo.CTRL_STATUS == 'DISPATCH',TosWisInfo.CTRL_STATUS == 'WAIT_ROUTE'), or_(DcWiTnfo.TRUCK_STATUS.is_(None), DcWiTnfo.TRUCK_STATUS != 'FINISH')).\
                filter(or_(DcWiTnfo.VERSION.is_(None), DcWiTnfo.VERSION != TosWisInfo.VERSION, DcWiTnfo.TRUCK_STATUS.like('ABANDON%'))).\
                order_by(sqlalchemy.asc(TosWisInfo.ID))
        # logger.info(f'query_execute:{sql}')
        curs = sql.all()
        results = list()
        for cur in curs:       
            dc_dict = {'TRUCK_STATUS':cur[1],'DC_UPDATE_TIME':cur[2]}
            results.append(dict(cur[0].to_dict(), **dc_dict))
        return results
    except Exception as e:
        logger.warning(f"query all DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()

def query_recent(vehicle_name, time_delta = 2):
    session = TosSession().acquire()
    try:
        sql = session.query(TosWisInfo, DcWiTnfo).\
                  outerjoin(DcWiTnfo, DcWiTnfo.ID == TosWisInfo.ID).\
                  filter(TosWisInfo.TRUCK_NO == vehicle_name).\
                  filter(or_(TosWisInfo.INSERT_TIME > datetime.datetime.now() - datetime.timedelta(hours=time_delta), TosWisInfo.UPDATE_TIME > datetime.datetime.now() - datetime.timedelta(hours = time_delta),
                      DcWiTnfo.INSERT_TIME > datetime.datetime.now() - datetime.timedelta(hours=time_delta), DcWiTnfo.UPDATE_TIME > datetime.datetime.now() - datetime.timedelta(hours = time_delta))).\
                  order_by(sqlalchemy.asc(TosWisInfo.ID))
        #logger.info(f'query_recent_by_name-sql:{sql}')
        curs = sql.all()
        results = list()
        for cur in curs:
            '''
            if cur[0] is not None:
                print(f'cur[0]:{cur[0].to_dict()}')
            if cur[1] is not None:
                print(f'cur[1]:{cur[1].to_dict()}')
            '''
            if cur[1] is not None:
                dc_dict = {'TRUCK_STATUS':cur[1].TRUCK_STATUS}
            else:
                dc_dict = {'TRUCK_STATUS':None}
            if cur[0] is not None:
                results.append(dict(cur[0].to_dict(), **dc_dict))
        return results
    except Exception as e:
        logger.warning(f"query all DcWiTnfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()

def query_by_id(id):
    session = TosSession().acquire()
    try:
        info = session.query(TosWisInfo).filter(TosWisInfo.ID == id).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_id err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def get_by_wi_act(vehicle_name, wi_id, wi_act):
    session = TosSession().acquire()
    try:
        # 2022.03.09
        info = session.query(TosWisInfo).filter(TosWisInfo.TRUCK_NO == vehicle_name,TosWisInfo.WI_ID == wi_id,TosWisInfo.WI_ACT == wi_act).filter(or_(TosWisInfo.WI_STATUS=='DISPATCH',TosWisInfo.WI_STATUS=='CANCEL')).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_wi_act err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def query_by_wi_ctn(vehicle_name, wi_id, wi_act, ctn_no):
    session = TosSession().acquire()
    try:
        # 2022.03.09
        # info = session.query(TosWisInfo).filter(TosWisInfo.TRUCK_NO == vehicle_name,TosWisInfo.WI_NO == wi_no,TosWisInfo.WI_ACT == wi_act, TosWisInfo.CTN_NO == ctn_no).first()
        info = session.query(TosWisInfo).filter(TosWisInfo.TRUCK_NO == vehicle_name,TosWisInfo.WI_ID == wi_id,TosWisInfo.WI_ACT == wi_act, TosWisInfo.CTN_NO == ctn_no).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_wi_ctn err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()


def query_by_id_version(id, version):
    session = TosSession().acquire()
    try:
        info = session.query(TosWisInfo).filter(TosWisInfo.ID == id,TosWisInfo.VERSION > version,TosWisInfo.WI_STATUS != 'CONFIRMED').first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"query_by_id_version err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()


def update(id, update_items:dict):
    session = TosSession().acquire()
    try:
        session.query(TosWisInfo).\
                filter_by(ID = id).\
                update(update_items)
        session.commit()
    except Exception as e:
        logger.warning(f"Update TosWisInfo Err: {e},trace:{traceback.format_exc()}")
    finally:
        session.close()
    return


def insert(record: TosWisInfo):
    session = TosSession().acquire()
    try:
        # 2022.03.09
        # cur = session.query(TosWisInfo).filter_by(TRUCK_NO = record.TRUCK_NO, WI_NO = record.WI_NO, WI_ACT = record.WI_ACT).first()
        cur = session.query(TosWisInfo).filter_by(TRUCK_NO=record.TRUCK_NO, WI_ID=record.WI_ID, WI_ACT=record.WI_ACT).first()
        if cur is None:
            record.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            record.VERSION = 1
            record.RESET_VERSION = 0
            logger.info(f"Insert TosWisInfo table to_dict:{record.to_dict()}") #here record.__dict__ will error,if not Base.to_dict = to_dict will ok
            logger.info(f"Insert TosWisInfo table __dict__:{record.__dict__}")
            session.add(record)  #
        else:
            record.VERSION = cur.VERSION + 1
            record.UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            logger.info(f"Update TosWisInfo table origin:{cur.__dict__}")
            logger.info(f"Update TosWisInfo table to_dict():{record.to_dict()}")
            logger.info(f"Update TosWisInfo table __dict__:{record.__dict__}")
            #session.query(TosWisInfo).filter(TosWisInfo.TRUCK_NO == record.TRUCK_NO,TosWisInfo.WI_NO == record.WI_NO,TosWisInfo.WI_ACT == record.WI_ACT).update(update_record)
            for key in record.__dict__:
                if key == '_sa_instance_state':
                    continue
                setattr(cur, key, getattr(record, key))
        session.commit()
    except Exception as e:
        logger.warning(f"Insert TosWisInfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def query_all():
      session = TosSession().acquire()
      try:
          curs = session.query(TosWisInfo).\
                          order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
          results = list()
          for cur in curs:
              results.append(cur.to_dict())
          return results
      except Exception as e:
          logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
          return []
      finally:
          session.close()

def query_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        curs = session.query(TosWisInfo).\
                        filter_by(TRUCK_NO=vehicle_name).\
                        order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
        results = list()
        for cur in curs:
            results.append(cur.to_dict())
        return results
    except Exception as e:
        logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()

def query_by_info(id, truck_no, cancel_time, wi_act, wi_type):
    cancel_max_time = cancel_time + datetime.timedelta(seconds=1)
    session = TosSession().acquire()
    try:
        info = session.query(TosWisInfo).filter(TosWisInfo.ID > id, TosWisInfo.TRUCK_NO == truck_no,
                                                TosWisInfo.WI_ACT == wi_act, TosWisInfo.WI_TYPE == wi_type,
                                                TosWisInfo.CONFIRMED_TIME >= cancel_time,
                                                TosWisInfo.CONFIRMED_TIME <= cancel_max_time,
                                                TosWisInfo.INSERT_TIME >= cancel_time,
                                                TosWisInfo.INSERT_TIME <= cancel_max_time).order_by(TosWisInfo.ID).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"Query TosWisInfo by info Error: {e},trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()


def query_by_WI_ID(vehicle_name, WI_ID):
    session = TosSession().acquire()
    try:
        info = session.query(TosWisInfo).\
                        filter_by(TRUCK_NO=vehicle_name, WI_ID=WI_ID).\
                        order_by(sqlalchemy.desc(TosWisInfo.INSERT_TIME)).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"Query TosWisInfo by WI_ID Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()


def query_info_by_ID(vehicle_name, ID):
    session = TosSession().acquire()
    try:
        sql = session.query(TosWisInfo, DcWiTnfo.TRUCK_STATUS, DcWiTnfo.UPDATE_TIME). \
            outerjoin(DcWiTnfo, DcWiTnfo.ID == TosWisInfo.ID).filter(TosWisInfo.ID == ID).first()
        logger.info(f'query_info_by_ID query_execute:{sql, type(sql), len(sql)}')
        if sql:
            dc_dict = {'TRUCK_STATUS': sql[1], 'TRUCK_UPDATE_TIME': sql[2]}
            result = dict(sql[0].to_dict(), **dc_dict)
            return result
        else:
            logger.warning(f"Query TosWis and Dc Info by ID Err: sql is {sql}")
            return None
    except Exception as e:
        logger.warning(f"Query TosWis and Dc Info by ID Err: {e},trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()


def delete_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        session.query(TosWisInfo).filter_by(TRUCK_NO=vehicle_name).delete()
        session.commit()
        return True
    except Exception as e:
        logger.warning(f"Delete TosWisInfo by name Error: {e},trace:{traceback.format_exc()}")
        return False
    finally:
        session.close()

def delete_all():
    session = TosSession().acquire()
    try:
        session.query(TosWisInfo).delete()
        session.commit()
    except Exception as e:
        logger.warning(f"Delete TosWisInfo all Err: {e},trace:{traceback.format_exc()}")
    finally:
        session.close()
    return

def update_by_reset(id):
    session = TosSession().acquire()
    try:
        session.query(TosWisInfo).filter_by(ID=id).update({"RESET_VERSION": TosWisInfo.RESET_VERSION + 1})
        session.commit()
        logger.info(f'reset id:{id}; RESET_VERSION:{TosWisInfo.RESET_VERSION}')
    except Exception as e:
        logger.warning(f"update reset_version TosWisInfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()


if __name__ == '__main__':
    # delete_all()
    # if len(sys.argv) < 2:
    #         print('0x01:query_execute\n')
    #         print('0x02:query_recent\n')
    # else:
    #     mask = 0
    #     if sys.argv[1].startswith('0x'):
    #         mask = int(sys.argv[1], 16)
    #     else:
    #         mask = sys.argv[1]

    #     if mask & 0x1:
    #         wis = query_execute('AT800',8)
    #         print(f'query_execute get wis:{len(wis)}')
    #         for wi in wis:
    #             print(f'wi:{wi}')

    #     if mask & 0x2:
    #         while True:
    #             wis = query_recent('AT800',2)
    #             print(f'query_recent get wis:{len(wis)}')
    #             for wi in wis:
    #                 print(f"wi:{wi},Truck_status:{wi.get('TRUCK_STATUS')}")
    #             time.sleep(2)

    # cancel_time = datetime.datetime.now()
    # query_by_info(1035, "AT501", cancel_time, "UNLOAD", "DSCH")
    result = query_execute('AT501')
    print("query_execute :", result)

    cancel_time = datetime.datetime.now()
    info = query_by_WI_ID("AT501", 1656657672)
    print("info :", info)
