import time
import json
from common.logger import logger
from common.singleton import Singleton
from cache.client import LocalClient
from proto import cache_pb2
from typing import Optional
from datetime import datetime

WI_TO_CS_MSG_QUEUE_PREFIX = 'wi-to-cs-msg-queue'
AUTO_PRE_WI_INFO_PREFIX = 'auto-pre-wi-info'

class WiCache(metaclass=Singleton):
    def __init__(self):
        self._redis = LocalClient()

    def push_msg(self, msg):
        key = f"{WI_TO_CS_MSG_QUEUE_PREFIX}"
        return self._redis.rpush(key, msg)

    def clear_msg(self,message_len=None):
        key = f"{WI_TO_CS_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_msg(self,auto_delete=True):
        key = f"{WI_TO_CS_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange(key, 0, -1)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def hset_pre_wi_info(self, pre_wi_info: cache_pb2.PreWiInfo):
        name = f"{AUTO_PRE_WI_INFO_PREFIX}"
        key = f"{pre_wi_info.truck_no}"
        return self._redis.hset_proto(name, key, pre_wi_info)

    def hget_pre_wi_info(self, truck_no: str) -> Optional[cache_pb2.PreWiInfo]:
        name = f"{AUTO_PRE_WI_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.PreWiInfo)

    def update_pre_wi_status(self, truck_no: str, wi_status: str):
        info = self.hget_pre_wi_info(truck_no)
        if info:
            info.wi_status = wi_status
            info.update_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.hset_pre_wi_info(info)
            return True
        else:
            return False

    def clear_all_pre_wi_info(self):
        name = f"{AUTO_PRE_WI_INFO_PREFIX}"
        return self._redis.delete(name)