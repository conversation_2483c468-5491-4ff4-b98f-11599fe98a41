import time
import json
from common.logger import logger
from common.singleton import Singleton
from common.constant import KAF<PERSON>_DEBUG
from cache.client import Share<PERSON>ache<PERSON><PERSON>,CacheClient
from proto.cache_pb2 import TpArriveMsg
from proto import cache_pb2, antenna_pb2
from typing import Optional

ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX = 'arrive-to-gantry-msg-queue'
ARRIVE_TO_TP_MSG_QUEUE_PREFIX = 'arrive-to-tp-msg-queue'
ARRIVE_TO_TP_INFO_PREFIX = 'arrive-to-tp-info'
ARRIVE_CPS_ALIGN_INFO_PREFIX = 'arrive-cps-align-info'
TP_TO_GANTRY_INFO_PREFIX = 'tp-to-gantry-info'
TP_TO_ARRIVE_GANTRY_MSG_QUEUE_PREFIX = 'tp-to-arrive-gantry-msg-queue'
class ArriveCache(metaclass=Singleton):
    def __init__(self):
        self._share_redis = ShareCacheClient()
        self._redis = CacheClient() if KAFKA_DEBUG else ShareCacheClient()

    def push_msg(self, vehicle_name, gantry_no, status, destination):
        msg = {}
        msg['gantry_no'] = gantry_no
        msg['vehicle_name'] = vehicle_name
        msg['status'] = status
        msg['destination'] = destination
        msg_value = str(msg)
        queue_name =ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX
        self._share_redis.lpush(queue_name, msg_value)
        logger.debug(f"PushRequest into ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX: {msg}")

    def pop_msg(self, timeout=None):
        queue_name =ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX
        if timeout is None:
            msg = self._share_redis.rpop(queue_name)
        else:
            msg = self._share_redis.brpop(queue_name, timeout = timeout)
        if msg is not None:
            msg_value = eval(str(msg, encoding = "utf-8")  )
            logger.debug(f"PopRequest from ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX: {msg_value}")
            return msg_value
        else:
            return msg

    def clear_all_msg(self):
        queue_name =ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX
        while self._share_redis.llen(queue_name) > 0:
            self._share_redis.ltrim(queue_name,1, 0)
  
    def pop_all_msg(self):
        queue_name =ARRIVE_TO_GATRY_MSG_QUEUE_PREFIX
        msg_list = []
        while self._share_redis.llen(queue_name) > 0:
            msg_list = msg_list + [self._share_redis.rpop(queue_name)]
        return msg_list


    def push_arrive_msg(self, msg):
        key = f"{ARRIVE_TO_TP_MSG_QUEUE_PREFIX}"
        return self._redis.rpush_proto(key, msg)

    def clear_arrive_msg(self,message_len=None):
        key = f"{ARRIVE_TO_TP_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_arrive_msg(self,auto_delete=True):
        key = f"{ARRIVE_TO_TP_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange_proto(key, 0, -1, TpArriveMsg)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def push_arrive_gantry_msg(self, msg):
        key = f"{TP_TO_ARRIVE_GANTRY_MSG_QUEUE_PREFIX}"
        return self._redis.rpush_proto(key, msg)

    def clear_arrive_gantry_msg(self,message_len=None):
        key = f"{TP_TO_ARRIVE_GANTRY_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_arrive_gantry_msg(self,auto_delete=True):
        key = f"{TP_TO_ARRIVE_GANTRY_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange_proto(key, 0, -1, TpArriveMsg)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def hset_tp_arrive_info(self, tp_arrive_info: cache_pb2.TpArriveInfo):
        name = f"{ARRIVE_TO_TP_INFO_PREFIX}"
        key =  f"{tp_arrive_info.truck_no}"
        return self._redis.hset_proto(name, key, tp_arrive_info)

    def hget_arrive_info(self, truck_no: str) -> Optional[cache_pb2.TpArriveInfo]:
        name = f"{ARRIVE_TO_TP_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.TpArriveInfo)

    def hget_all_arrive_info(self):
        name = f"{ARRIVE_TO_TP_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.TpArriveInfo)
        results = [result for result in results if result is not None]
        return results

    def hset_tp_gantry_info(self, tp_gantry_info: cache_pb2.TpToGantryInfo):
        name = f"{TP_TO_GANTRY_INFO_PREFIX}"
        key =  f"{tp_gantry_info.truck_no}"
        return self._redis.hset_proto(name, key, tp_gantry_info)

    def hget_gantry_info(self, truck_no: str) -> Optional[cache_pb2.TpToGantryInfo]:
        name = f"{TP_TO_GANTRY_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.TpToGantryInfo)

    def hget_all_gantry_info(self):
        name = f"{TP_TO_GANTRY_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.TpToGantryInfo)
        results = [result for result in results if result is not None]
        return results

    def hset_cps_align_arrive_info(self, truck_no: str, cps_align_arrive_info:antenna_pb2.CpsAlignArrivedMsg):
        name = f"{ARRIVE_CPS_ALIGN_INFO_PREFIX}"
        key =  truck_no
        return self._redis.hset_proto(name, key, cps_align_arrive_info)

    def hget_cps_align_arrive_info(self, truck_no: str) -> Optional[antenna_pb2.CpsAlignArrivedMsg]:
        name = f"{ARRIVE_CPS_ALIGN_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, antenna_pb2.CpsAlignArrivedMsg)

    def hget_all_cps_align_arrive_info(self):
        name = f"{ARRIVE_CPS_ALIGN_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, antenna_pb2.CpsAlignArrivedMsg)
        results = [result for result in results if result is not None]
        return results

    def hdel_cps_align_arrive_info(self, truck_no: str):
        name = f"{ARRIVE_CPS_ALIGN_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hdel(name, key)
