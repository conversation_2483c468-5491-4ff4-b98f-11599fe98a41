import time
import uuid

from google.protobuf.text_format import Message<PERSON><PERSON><PERSON><PERSON>

from cache import vehicle_cache
from cache.client import <PERSON><PERSON><PERSON><PERSON>
from common.logger import logger
from common.singleton import Singleton
from proto import antenna_pb2, ecs_command_pb2
from proto.ecs_command_pb2 import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>T<PERSON>, <PERSON>csCommandExecuteStatus, <PERSON>csCommandStatus, <PERSON><PERSON><PERSON>om<PERSON><PERSON><PERSON>
from proto.fabupilot.antenna.task_state_pb2 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TaskState

SLOW_BRAKE_COMMAND_SET_PREFIX = 'SLOW_BRAKE_COMMAND_SET'
ALL_SLOW_BRAKE_COMMAND_SET_PREFIX = 'ALL_SLOW_BRAKE_COMMAND_SET'
EMERGENCY_BRAKE_COMMAND_SET_PREFIX = 'EMERGENCY_BRAKE_COMMAND_SET'
START_WORK_COMMAND_SET_PREFIX = 'START_WORK_COMMAND_SET'
START_DORMANCY_COMMAND_SET_PREFIX = 'START_DORMANCY_COMMAND_SET'
CONTROL_WORK_MODE_COMMAND_SET_PREFIX = 'CONTROL_WORK_MODE_COMMAND_SET'
ECS_COMMAND_INFO_PREFIX = 'ECS_COMMAND_INFO_PREFIX'
EXECUTING_ECS_COMMAND_INFO_PREFIX = 'EXECUTING_ECS_COMMAND_INFO_PREFIX'
ECS_COMMAND_EXECUTE_STATUS_PREFIX = 'ECS_COMMAND_EXECUTE_STATUS_PREFIX'
ECS_COMMAND_EXPIRE_TIME = 7 * 24 * 60 * 60

class EcsCommandCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        #集合形式缓存
        self._ecs_remote_brake_command_type = [ecs_command_pb2.EcsCommandType.SLOW_BRAKE,
                                               ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE,
                                               ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE,
                                               ecs_command_pb2.EcsCommandType.LOCK_STATION_EMERGENCY_BRAKE,
                                               ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE,
                                               ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE,
                                               ecs_command_pb2.EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE]
        #集合形式缓存
        self._ecs_remote_control_command_type = [ecs_command_pb2.EcsCommandType.START_WORK,
                                                 ecs_command_pb2.EcsCommandType.START_DORMANCY,
                                                 ecs_command_pb2.EcsCommandType.CONTROL_WORK_MODE,
                                                 ecs_command_pb2.EcsCommandType.HMI,
                                                 ecs_command_pb2.EcsCommandType.UPLOAD_COMMAND,
                                                 ecs_command_pb2.EcsCommandType.CALIBRATION_CHECK,
                                                 ecs_command_pb2.EcsCommandType.AIR_PUMP_CONTROL,
                                                 ecs_command_pb2.EcsCommandType.PAD_MSG,
                                                 ecs_command_pb2.EcsCommandType.LOCALIZATION_RESET,
                                                 ecs_command_pb2.EcsCommandType.OPERATE_PROCESS_CMD,
                                                 ecs_command_pb2.EcsCommandType.EXIT_UNRECOVERABLE_EMERGENCY,
                                                 ecs_command_pb2.EcsCommandType.CHECK_CONTAINER_SLOT,
                                                 ecs_command_pb2.EcsCommandType.ADJUST_UPLOAD_FREQUENCY,
                                                 ecs_command_pb2.EcsCommandType.DEVICE_MODE_CONTROL,
                                                 ecs_command_pb2.EcsCommandType.INITIALIZE_LOCATION]
        #value形式缓存,PASSBY_LANE_COMMAND调度直接存redis,取消调度直接取消.FMS只读
        self._ecs_remote_cache_command_type = [ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND]

        self._ecs_command_type_in_redis = [ecs_command_pb2.EcsCommandType.SLOW_BRAKE,
                                           ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE,
                                           ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE,
                                           ecs_command_pb2.EcsCommandType.START_WORK,
                                           ecs_command_pb2.EcsCommandType.START_DORMANCY,
                                           ecs_command_pb2.EcsCommandType.MOVE,
                                           ecs_command_pb2.EcsCommandType.ADJUST_OFFSET,
                                           ecs_command_pb2.EcsCommandType.CONTROL_WORK_MODE,
                                           ecs_command_pb2.EcsCommandType.MOVE_STATION,
                                           ecs_command_pb2.EcsCommandType.CONTROL_DOOR,
                                           ecs_command_pb2.EcsCommandType.HMI,
                                           ecs_command_pb2.EcsCommandType.LOCK_STATION_EMERGENCY_BRAKE,
                                           ecs_command_pb2.EcsCommandType.UPLOAD_COMMAND,
                                           ecs_command_pb2.EcsCommandType.CALIBRATION_CHECK,
                                           ecs_command_pb2.EcsCommandType.AIR_PUMP_CONTROL,
                                           ecs_command_pb2.EcsCommandType.PAD_MSG,
                                           ecs_command_pb2.EcsCommandType.LOCALIZATION_RESET,
                                           ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND,
                                           ecs_command_pb2.EcsCommandType.OPERATE_PROCESS_CMD,
                                           ecs_command_pb2.EcsCommandType.EXIT_UNRECOVERABLE_EMERGENCY,
                                           ecs_command_pb2.EcsCommandType.CHECK_CONTAINER_SLOT,
                                           ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE,
                                           ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE,
                                           ecs_command_pb2.EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE,
                                           ecs_command_pb2.EcsCommandType.ADJUST_UPLOAD_FREQUENCY,
                                           ecs_command_pb2.EcsCommandType.DEVICE_MODE_CONTROL,
                                           ecs_command_pb2.EcsCommandType.INITIALIZE_LOCATION]
        self._ecs_command_type_in_db = [ecs_command_pb2.EcsCommandType.LEAVE_SPACE,
                                        ecs_command_pb2.EcsCommandType.STOP_WORK]

    def is_command_storage_in_redis(self, command_type):
        return command_type in self._ecs_command_type_in_redis

    def is_command_storage_in_db(self, command_type):
        return command_type in self._ecs_command_type_in_db

    def is_remote_brake_command(self, command_type):
        return command_type in self._ecs_remote_brake_command_type

    def is_remote_control_command(self, command_type):
        return command_type in self._ecs_remote_control_command_type

    def is_remote_cache_command(self, command_type):
        return command_type in self._ecs_remote_cache_command_type

    def get_key_by_cache_command_type(self, command_type, vehicle_name=None):
        command_type_name = ecs_command_pb2.EcsCommandType.Name(command_type)
        command_key = f"{command_type_name}_COMMAND_CACHE_{vehicle_name}"
        return command_key

    def query_bassby_lane_command(self, command_type, vehicle_name):
        key = self.get_key_by_cache_command_type(command_type, vehicle_name)
        info = self._redis.get_proto(key, ecs_command_pb2.PassbyLaneCommand)
        if info:
            info.passby_status = ecs_command_pb2.PassbyLaneCommand.DISPATCHED if info.passby_lane else ecs_command_pb2.PassbyLaneCommand.CANCELED
            info.dispatch_timestamp = info.update_timestamp / 1000
        return info

    def query_remote_command_cache_by_type(self, command_type, vehicle_name):
        ecs_command = None
        if command_type == ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND:
            if (info:=self.query_bassby_lane_command(command_type,vehicle_name)) is not None:
                cur_time = time.time()
                ecs_command = ecs_command_pb2.EcsCommand()
                ecs_command.vehicle_name = vehicle_name
                ecs_command.command_type = command_type
                #dispatch_timestamp要实时更新,如果DISPATCHED则将timestamp更新成实时最新,如果为CANCELED则更新为dispatch_timestamp+3
                #+3是为了保证timestamp能够大于上次的timestamp
                ecs_command.timestamp = int(cur_time) \
                  if info.passby_status == ecs_command_pb2.PassbyLaneCommand.DISPATCHED else int(info.dispatch_timestamp + 3)
                ecs_command.passby_lane_cmd.CopyFrom(info)
                ecs_command.passby_lane_cmd.dispatch_timestamp = cur_time
        else:
            key = self.get_key_by_cache_command_type(command_type, vehicle_name)
            ecs_command = self._redis.get_proto(key, ecs_command_pb2.EcsCommand)
        return ecs_command


    def delete_history_set_commands(self,command_key):
        key = f"{command_key}"
        current_length = self._redis.scard(key)
        future_length = current_length + 1
        delete_max_length = 100
        pop_max_length = 50
        if future_length > delete_max_length:
            logger.warning(f"delete_history_set_commands:delete command_key:{command_key},current_length:{current_length},"
                           f"future_length:{future_length},delete_max_length:{delete_max_length}")
            self._redis.delete(key)
        elif future_length > pop_max_length:
            logger.warning(f"delete_history_set_commands:spop command_key:{command_key},current_length:{current_length},"
                           f"future_length:{future_length},spop num:{future_length - pop_max_length},pop_max_length:{pop_max_length}")
            self._redis.spop(key, future_length - pop_max_length)
        return

    ''' remote brake '''

    # dispatch remote brake command
    def get_key_by_brake_type(self, command_type, vehicle_name=None):
        brake_type = ecs_command_pb2.EcsCommandType.Name(command_type)
        if brake_type == "ALL_SLOW_BRAKE":
            set_key = ALL_SLOW_BRAKE_COMMAND_SET_PREFIX
        else:
            set_key = f"{brake_type}_COMMAND_SET_{vehicle_name}"
        return set_key

    def dispatch_all_slow_brake_command(self, brake_reason):
        command = ecs_command_pb2.EcsCommand()
        command.uuid = str(uuid.uuid4())
        command.timestamp = int(time.time())
        command.user_id = 'antenna-server'
        command.command_type = ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE
        command.execute_type = ecs_command_pb2.EcsCommandExecuteType.DISPATCH
        command.all_slow_brake_command.brake_reason = brake_reason
        self.dispatch_brake_command(command)

    def dispatch_brake_command(self, ecs_command):
        set_key = self.get_key_by_brake_type(ecs_command.command_type, ecs_command.vehicle_name)
        self.delete_history_set_commands(set_key)
        return self._redis.sadd_proto(set_key, ecs_command) == 1

    # cancel remote brake command
    def cancel_brake_command(self, ecs_command):
        set_key = self.get_key_by_brake_type(ecs_command.command_type, ecs_command.vehicle_name)
        # uuid为空则清空所有数据
        if not ecs_command.HasField('uuid'):
            return self._redis.delete(set_key) == 1
        # uuid不空则删除指定指令
        else:
            commands = self._redis.smembers_proto(set_key, ecs_command_pb2.EcsCommand)
            for command in commands:
                if command.uuid == ecs_command.uuid:
                    return self._redis.srem_proto(set_key, command) == 1
        return False

    def cancel_brake_command_by_type(self, brake_type, vehicle_name=None):
        set_key = self.get_key_by_brake_type(brake_type, vehicle_name)
        return self._redis.delete(set_key)

    # query remote brake command
    def query_brake_status_by_type(self, brake_type, vehicle_name=None):
        set_key = self.get_key_by_brake_type(brake_type, vehicle_name)
        return True if self._redis.scard(set_key) > 0 else False

    def query_brake_command_set_by_type(self, brake_type, vehicle_name=None):
        set_key = self.get_key_by_brake_type(brake_type, vehicle_name)
        return self._redis.smembers_proto(set_key, ecs_command_pb2.EcsCommand)

    def query_brake_reason_by_type(self, brake_type, vehicle_name=None):
        set_key = self.get_key_by_brake_type(brake_type, vehicle_name)
        commands = self._redis.smembers_proto(set_key, ecs_command_pb2.EcsCommand)
        ret = set()
        for command in commands:
            if command.command_type == ecs_command_pb2.EcsCommandType.SLOW_BRAKE:
                ret.add(command.slow_brake_command.brake_reason)
            elif command.command_type == ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE:
                ret.add(command.emergency_brake_command.brake_reason)
            elif command.command_type == ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE:
                ret.add(command.all_slow_brake_command.brake_reason)
        return ret

    ''' remote control '''

    def get_key_by_control_type(self, command_type, vehicle_name=None):
        brake_type = ecs_command_pb2.EcsCommandType.Name(command_type)
        set_key = f"{brake_type}_COMMAND_SET_{vehicle_name}"
        return set_key

    # dispatch
    def dispatch_remote_control_command(self, ecs_command):
        key = self.get_key_by_control_type(ecs_command.command_type, ecs_command.vehicle_name)
        # HMI指令帧率较高，保持最新的即可，先把之前的删掉
        if ecs_command.command_type == ecs_command_pb2.EcsCommandType.HMI:
            self.delete_remote_control_command_set_by_type(ecs_command.command_type, ecs_command.vehicle_name)
        else:
            self.delete_history_set_commands(key)
        return self._redis.sadd_proto(key, ecs_command) == 1

    # cancel
    def cancel_remote_control_command(self, ecs_command):
        set_key = self.get_key_by_control_type(ecs_command.command_type, ecs_command.vehicle_name)
        # uuid为空则清空所有数据
        if ecs_command.uuid is None:
            return self._redis.delete(set_key) == 1
        # uuid不空则删除指定指令
        else:
            commands = self._redis.smembers_proto(set_key, ecs_command_pb2.EcsCommand)
            for command in commands:
                if command.uuid == ecs_command.uuid:
                    return self._redis.srem_proto(set_key, command) == 1
        return False

    def delete_remote_control_command_set_by_type(self, command_type, vehicle_name):
        key = self.get_key_by_control_type(command_type, vehicle_name)
        return self._redis.delete(key) == 1

    # query
    def query_remote_control_command_set_by_type(self, remote_control_type, vehicle_name):
        key = self.get_key_by_control_type(remote_control_type, vehicle_name)
        return self._redis.smembers_proto(key, ecs_command_pb2.EcsCommand)

    def query_executing_ecs_command(self, vehicle_name, command_type: EcsCommandType):
        command_type_name = EcsCommandType.Name(command_type)
        key = f"{EXECUTING_ECS_COMMAND_INFO_PREFIX}:{vehicle_name}:{command_type_name}"
        return self._redis.get_proto(key, EcsCommand)

    def set_executing_ecs_command(self, vehicle_name, command):
        command_type_name = EcsCommandType.Name(command.command_type)
        key = f"{EXECUTING_ECS_COMMAND_INFO_PREFIX}:{vehicle_name}:{command_type_name}"
        return self._redis.set_proto(key, command, ex=ECS_COMMAND_EXPIRE_TIME)

    def delete_executing_ecs_command(self, vehicle_name, command_type):
        command_type_name = EcsCommandType.Name(command_type)
        key = f"{EXECUTING_ECS_COMMAND_INFO_PREFIX}:{vehicle_name}:{command_type_name}"
        return self._redis.delete(key)

    def query_ecs_command(self, vehicle_name, command_keys: list):
        ecs_commands = []
        cur_time = int(round(time.time()))
        # 批量查询 Redis
        redis_keys = [f"{ECS_COMMAND_INFO_PREFIX}:{command_key.uuid}" for command_key in command_keys if command_key.uuid]
        redis_results = self._redis.mget_proto(redis_keys, EcsCommand) if redis_keys else []
        redis_command_map = {key: cmd for key, cmd in zip(redis_keys, redis_results) if cmd is not None}

        for command_key in command_keys:
            uuid = command_key.uuid
            command_type = command_key.command_type
            command = None
            if self.is_remote_control_command(command_type):
                remote_control_commands = self.query_remote_control_command_set_by_type(command_type, vehicle_name)
                if remote_control_commands:
                    self.delete_remote_control_command_set_by_type(command_type, vehicle_name)
                    ecs_commands.extend([
                        cmd for cmd in remote_control_commands
                        if cur_time - cmd.timestamp <= 10  # 过滤掉时间过久的
                    ])
            elif self.is_remote_brake_command(command_type):
                ecs_commands.extend(self.query_brake_command_set_by_type(command_type, vehicle_name))
            elif self.is_remote_cache_command(command_type):
                if (command_cache := self.query_remote_command_cache_by_type(command_type, vehicle_name)) is not None:
                    ecs_commands.append(command_cache)
            else:
                if not uuid:
                    command = self.query_executing_ecs_command(vehicle_name, command_type)
                    if command:
                        uuid = command.uuid
                if uuid and (cached_command := redis_command_map.get(f"{ECS_COMMAND_INFO_PREFIX}:{uuid}")):
                    command = cached_command
                if command:
                    ecs_commands.append(command)
        return ecs_commands

    def query_all_ecs_commands(self, vehicle_name):
        key_pattern = f"{ECS_COMMAND_INFO_PREFIX}*"
        keys = self._redis.get_pattern_keys(key_pattern)
        ecs_commands = []
        for key in keys:
            command = self._redis.get_proto(key, EcsCommand)
            if command is not None:
                ecs_commands.append(command)
        return ecs_commands

    def get_ecs_command_status(self, vehicle_name, command_keys: list):
        status_list = []
        ecs_commands = self.query_ecs_command(vehicle_name, command_keys)
        for command in ecs_commands:
            command_status = EcsCommandStatus()
            command_status.uuid = command.uuid
            command_status.command_type = command.command_type
            command_status.status = command.execute_type
            status_list.append(command_status)
        return status_list

    def dispatch_ecs_command(self, vehicle_name, command: EcsCommand):
        logger.info(f'[dispatch_ecs_command] command:{MessageToString(command, as_one_line=True)}')
        error_string = ""
        if len(command.uuid) == 0:
            error_string = f"invalid uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name} dispatch adjust offset {error_string}")
            return (False, error_string)
        if command.command_type == EcsCommandType.ADJUST_OFFSET and \
                (ecs_command := self.query_executing_ecs_command(vehicle_name, command.command_type)) is not None:
            error_string = f"exists adjust off ecs command,fail to insert new adjust off ecs command for uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name},{error_string},now command:{ecs_command}")
            return (False, error_string)
        key = f"{ECS_COMMAND_INFO_PREFIX}:{command.uuid}"
        ret = self._redis.set_proto(key, command, ex=ECS_COMMAND_EXPIRE_TIME)
        if ret:
            ret = self.set_executing_ecs_command(vehicle_name, command)
            if ret:
                ret = self.update_ecs_command_execute_status(vehicle_name, command.uuid, command.command_type,
                                                             EcsCommandExecuteStatus.DISPATCH)
                if not ret:
                    self.delete_executing_ecs_command(vehicle_name, command.command_type)
                    error_string = f"fail to dispatch ecs command for uuid:{command.uuid}:update ecs command execute status"
                    logger.warning(f"vehicle_name:{vehicle_name},{error_string}")
            else:
                error_string = f"fail to dispatch ecs command for uuid:{command.uuid}:set executing ecs command"
                logger.warning(f"vehicle_name:{vehicle_name},{error_string}")
        else:
            error_string = f"fail to dispatch ecs command for uuid:{command.uuid}:set proto"
            logger.warning(f"vehicle_name:{vehicle_name},{error_string}")
        return (ret, error_string)

    def update_ecs_command(self, vehicle_name, command):
        logger.info(f'[update_ecs_command] command:{MessageToString(command, as_one_line=True)}')
        error_string = ""
        if len(command.uuid) == 0:
            error_string = f"invalid uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name} fail to update ecs command for {error_string}")
            return (False, error_string)
        key = f"{ECS_COMMAND_INFO_PREFIX}:{command.uuid}"
        ecs_command = self._redis.get_proto(key, EcsCommand)
        if ecs_command is None:
            error_string = f"no command by uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name} fail to cancel ecs command for {error_string}")
            return (False, error_string)
        if ecs_command.command_type != command.command_type:
            error_string = f"no command type by uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name},{error_string},"
                           f"ecs_command.command_type:{ecs_command.command_type},command.command_type:{command.command_type}")
            return (False, error_string)
        ret = self._redis.set_proto(key, command, ex=ECS_COMMAND_EXPIRE_TIME)
        if ret:
            self.set_executing_ecs_command(vehicle_name, command)
            error_string = f"successful to update ecs command for uuid:{command.uuid}"
        else:
            error_string = f"fail to update ecs command for uuid:{command.uuid}"
            logger.warning(f"vehicle_name:{vehicle_name} {error_string}")
        return (ret, error_string)

    def get_brake_command_status(self, brake_type, vehicle_name):
        status = EcsCommandExecuteStatus()
        status.command_type = brake_type
        is_brake = self.query_brake_status_by_type(brake_type, vehicle_name)
        status.status = EcsCommandExecuteStatus.DISPATCH if is_brake else EcsCommandExecuteStatus.CANCELED
        status.timestamp_ms = int(round(time.time() * 1000))
        brake_reasons = self.query_brake_reason_by_type(brake_type, vehicle_name)
        for brake_reason in brake_reasons:
            status.general_note.append(brake_reason)

        return status

    def get_hmi_command_status(self, vehicle_name):
        status = EcsCommandExecuteStatus()
        status.timestamp_ms = int(round(time.time() * 1000))
        high_frame_vehicle_status = vehicle_cache.get_high_frame_vehicle_status(vehicle_name)
        if high_frame_vehicle_status is None or not high_frame_vehicle_status.HasField('task_state'):
            status.status = EcsCommandExecuteStatus.UNKNOW
            logger.warning(f'get_hmi_command_status error, empty high_frame_vehicle_status or task_state')
            return status
        task_state = high_frame_vehicle_status.task_state
        if not task_state.HasField('hmi_task_mode'):
            logger.warning(f'get_hmi_command_status error, empty hmi_task_mode')
            status.status = EcsCommandExecuteStatus.UNKNOW
        else:
            if task_state.hmi_task_mode == HmiTaskMode.HMIRUNNING:
                status.status = EcsCommandExecuteStatus.DISPATCH
            elif task_state.hmi_task_mode == HmiTaskMode.HMIFAIL:
                status.status = EcsCommandExecuteStatus.FAILED
                status.error_code = TaskState.HmiErrorCode.Name(task_state.hmi_error_code)
            else:
                status.status = EcsCommandExecuteStatus.UNKNOW
                logger.warning(f'get_hmi_command_status error, unknown hmi_task_mode')
        return status

    def get_ecs_command_execute_status(self, vehicle_name, command_keys: list):
        status_list = []
        brake_command_types = [EcsCommandType.SLOW_BRAKE, EcsCommandType.EMERGENCY_BRAKE, EcsCommandType.ALL_SLOW_BRAKE,
                               EcsCommandType.LOCK_STATION_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE]
        for command_key in command_keys:
            # logger.info(f'debug command_key : {command_key}')
            # 由于redis中存储的刹车指令与其他指令结构不同，故分开查询
            # 查询redis中的刹车指令
            if command_key.command_type in brake_command_types:
                brake_type = command_key.command_type
                status = self.get_brake_command_status(brake_type, vehicle_name)
                status_list.append(status)
            elif command_key.command_type == ecs_command_pb2.EcsCommandType.HMI:
                status = self.get_hmi_command_status(vehicle_name)
                status_list.append(status)
            else:
                # 查询redis中的非刹车指令
                key = f"{ECS_COMMAND_EXECUTE_STATUS_PREFIX}:{command_key.uuid}"
                status = self._redis.get_proto(key, EcsCommandExecuteStatus)
                if status is not None:
                    status_list.append(status)
                else:
                    logger.warning(
                        f"vehicle_name:{vehicle_name} fail to get execute status for uuid:{command_key.uuid}")
        return status_list

    def get_ecs_command_execute_status_v2(self, vehicle_name_list: list, command_key: EcsCommandKey):
        status_list = []
        brake_command_types = [EcsCommandType.SLOW_BRAKE, EcsCommandType.EMERGENCY_BRAKE, EcsCommandType.ALL_SLOW_BRAKE,
                               EcsCommandType.LOCK_STATION_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE,
                               EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE]
        if command_key.command_type not in brake_command_types:
            status_list = [None] * len(vehicle_name_list)
        else:
            brake_type = command_key.command_type
            with self._redis.pipeline() as pipe:
                for vehicle_name in vehicle_name_list:
                    brake_set_key = self.get_key_by_brake_type(brake_type, vehicle_name)
                    pipe.scard(brake_set_key)
                is_brake_list = pipe.execute()
            is_brake_list = [brake_count > 0 for brake_count in is_brake_list]
            for is_brake in is_brake_list:
                status = EcsCommandExecuteStatus()
                status.command_type = brake_type
                status.status = EcsCommandExecuteStatus.DISPATCH if is_brake else EcsCommandExecuteStatus.CANCELED
                status.timestamp_ms = int(round(time.time() * 1000))
                # 暂时不查询刹车原因，等改写方式
                # brake_reasons = self.query_brake_reason_by_type(brake_type, vehicle_name)
                # for brake_reason in brake_reasons:
                #     status.general_note.append(brake_reason)
                status_list.append(status)
        return status_list

    def get_all_ecs_commands_execute_status(self, vehicle_name):
        key_pattern = f"{ECS_COMMAND_EXECUTE_STATUS_PREFIX}*"
        keys = self._redis.get_pattern_keys(key_pattern)
        status_list = []
        for key in keys:
            status = self._redis.get_proto(key, EcsCommandExecuteStatus)
            if status is not None:
                status_list.append(status)
            else:
                logger.warning(f"vehicle_name:{vehicle_name} fail to get execute status for key:{key}")
        return status_list

    def update_ecs_command_execute_status(self, vehicle_name, uuid, command_type: EcsCommandType,
                                          status: EcsCommandExecuteStatus.CommandStatus):
        ret = True
        key = f"{ECS_COMMAND_EXECUTE_STATUS_PREFIX}:{uuid}"
        new_status = EcsCommandExecuteStatus()
        new_status.vehicle_name = vehicle_name
        new_status.uuid = uuid
        new_status.command_type = command_type
        new_status.status = status
        new_status.timestamp_ms = int(time.time() * 1000)
        logger.info(f'[update_ecs_command_execute_status] status:{MessageToString(new_status, as_one_line=True)}')
        status_name = EcsCommandExecuteStatus.CommandStatus.Name(status)
        if command_type == EcsCommandType.ADJUST_OFFSET:
            if status_name in ["CANCELED", "ABORTED", "FAILED", "FINISHED"]:
                ret = self.delete_executing_ecs_command(vehicle_name, command_type)
            if ret:
                ret = self._redis.set_proto(key, new_status, ex=ECS_COMMAND_EXPIRE_TIME)
            else:
                logger.warning(f"vehicle_name: {vehicle_name} fail to update executing ecs command,uuid:{uuid},"
                               f"command_type:{command_type},status:{status_name}")
                ret = False
        elif command_type in self.is_remote_control_command():
            if status_name == "FINISHED" or status_name == "CANCELED":
                self.delete_remote_control_command_set_by_type(command_type, vehicle_name)
                ret = True
            else:
                logger.warning(f"vehicle_name: {vehicle_name} fail to update executing ecs command,uuid:{uuid},"
                               f"command_type:{command_type},status:{status_name}")
                ret = False
        else:
            logger.warning(f"vehicle_name: {vehicle_name} fail to update executing ecs command,uuid:{uuid},"
                           f"command_type:{command_type},status:{status_name}")
            ret = False
        return ret

    # 依据刹车类型获取指定车辆刹车指令
    def get_remote_brake_command_by_type(self, ecs_command_type, vehicle_name):
        ecs_command_info = antenna_pb2.EcsCommandInfo()
        ecs_command_info.ecs_command_type = ecs_command_type
        if ecs_command_type == EcsCommandType.SLOW_BRAKE:
            ecs_command_info.slow_brake_status = self.query_brake_status_by_type(ecs_command_type, vehicle_name)
        elif ecs_command_type == EcsCommandType.EMERGENCY_BRAKE:
            ecs_command_info.emergency_brake_status = self.query_brake_status_by_type(ecs_command_type, vehicle_name)
        elif ecs_command_type == EcsCommandType.ALL_SLOW_BRAKE:
            ecs_command_info.all_slow_brake_status = self.query_brake_status_by_type(ecs_command_type)
        else:
            logger.warning(f"get_ecs_command error, vehicle_name: {vehicle_name}, ecs_command_type: {ecs_command_type}")
            return None
        brake_reasons = self.query_brake_reason_by_type(ecs_command_type, vehicle_name)
        for brake_reason in brake_reasons:
            ecs_command_info.remote_brake_reason.append(brake_reason)
        return ecs_command_info

    # 获取指定车辆所有刹车类型指令
    def get_remote_brake_command(self, vehicle_name):
        commands = []
        command_types = [EcsCommandType.SLOW_BRAKE, EcsCommandType.EMERGENCY_BRAKE, EcsCommandType.ALL_SLOW_BRAKE]
        for command_type in command_types:
            command = self.get_remote_brake_command_by_type(command_type, vehicle_name)
            commands.append(command)
        return commands


    def check_and_delete_history_set_commands(self, vehicle_name,command_type,auto_delete=False,delete_threhold_length = 100):
        ret = True
        try:
            if self.is_remote_control_command(command_type):
                key = self.get_key_by_control_type(command_type, vehicle_name)
            elif self.is_remote_brake_command(command_type):
                key = self.get_key_by_brake_type(command_type, vehicle_name)
            else:
                logger.warning(f"fail to get key:vehicle_name:{vehicle_name},command_type:{ecs_command_pb2.EcsCommandType.Name(command_type)}")
                return ret
            current_length = self._redis.scard(f"{key}")
            if auto_delete and current_length >= delete_threhold_length:
                self._redis.delete(key)
                logger.warning(f"$$$$$delete:vehicle_name:{vehicle_name},command_type:{ecs_command_pb2.EcsCommandType.Name(command_type)},key:{key}"
                               f",current_length:{current_length},delete_threhold_length:{delete_threhold_length}")
            logger.info(f"vehicle_name:{vehicle_name},command_type:{ecs_command_pb2.EcsCommandType.Name(command_type)},key:{key},"
                        f",current_length:{current_length},>100:{current_length>100},>1000:{current_length>1000},>10000:{current_length>10000}")
            if current_length > 100:
                logger.warning(f"!!!!!vehicle_name:{vehicle_name},command_type:{ecs_command_pb2.EcsCommandType.Name(command_type)},key:{key},"
                        f"current_length:{current_length} over length")
        except Exception as e:
            logger.warning(f"vehicle_name:{vehicle_name} fail to check,e:{e}, trace:{traceback.format_exc()}")
            ret = False
        return ret

    def check_and_delete_all_history_set_commands(self):
        from common.logger import init_logger
        import traceback
        init_logger(f"check_ecs_command_cache")
        logger.info(f"##########################start check and delete now###########################################")
        check_command_types = [
                      ecs_command_pb2.EcsCommandType.SLOW_BRAKE,
                      ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE,
                      ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE,
                      ecs_command_pb2.EcsCommandType.LOCK_STATION_EMERGENCY_BRAKE,
                      ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE,
                      ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE,
                      ecs_command_pb2.EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE,
                      ecs_command_pb2.EcsCommandType.CALIBRATION_CHECK,
                      ecs_command_pb2.EcsCommandType.LOCALIZATION_RESET,
                      ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND,
                      ]
        from common.name_converter import NameConverter
        truck_nos = list(sorted(NameConverter().all_truck_no()))
        for truck in truck_nos:
            logger.info(f"@@@@@@@@@@@@@@@@@@@@@check truck:{truck}@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
            time.sleep(1)
            for command_type in check_command_types:
                if not self.check_and_delete_history_set_commands(truck,command_type,auto_delete=False,delete_threhold_length=100):
                    return

if __name__ == '__main__':
    cache = EcsCommandCache()
    vehicle_name = 'AT801'
    ''' cancel remote brake '''
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, 'AT801'))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, 'AT801'))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE, 'AT801'))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE, 'AT801'))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))
    # print(cache.cancel_brake_command_by_type(ecs_command_pb2.EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))

    ''' query remote brake '''
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, vehicle_name))
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, vehicle_name))
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE))
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.LOCK_STATION_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))
    # print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.LOCK_PAD_OFFLINE_UNRECOVERABLE_EMERGENCY_BRAKE, vehicle_name))

    ''' cancel all remote control '''
    # print(cache.delete_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.START_WORK, vehicle_name))
    # print(cache.delete_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.START_DORMANCY, vehicle_name))
    # print(cache.delete_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.CONTROL_WORK_MODE, vehicle_name))

    ''' query remote control '''
    # print(cache.query_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.START_WORK, vehicle_name))
    # print(cache.query_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.START_DORMANCY, vehicle_name))
    # print(cache.query_remote_control_command_set_by_type(ecs_command_pb2.EcsCommandType.CONTROL_WORK_MODE, vehicle_name))

    ''' query remote passby lane command cache '''
    print(f"query ecs command:{cache.query_remote_command_cache_by_type(ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND, vehicle_name)}")
    print(f"query in redis:vehicle_name:{vehicle_name}:{cache.query_bassby_lane_command(ecs_command_pb2.EcsCommandType.PASSBY_LANE_COMMAND, vehicle_name)}")

    # check_and_delete
    # cache.check_and_delete_all_history_set_commands()