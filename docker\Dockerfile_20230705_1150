FROM docker.fabu.ai:5000/antenna-server/antenna-server:v3

##################
# add cuda support
##################
#newer:3bf863cc.pub
RUN apt-get update && apt-get install -y --no-install-recommends \
    gnupg2 curl ca-certificates && \
    curl -fsSL https://mirrors.aliyun.com/nvidia-cuda/ubuntu1804/x86_64/7fa2af80.pub | apt-key add - && \
    echo "deb https://mirrors.aliyun.com/nvidia-cuda/ubuntu1804/x86_64 /" > /etc/apt/sources.list.d/cuda.list && \
    echo "deb https://developer.download.nvidia.com/compute/machine-learning/repos/ubuntu1804/x86_64 /" > /etc/apt/sources.list.d/nvidia-ml.list && \
    apt-get purge --autoremove -y curl

ENV CUDA_VERSION 10.1.243
ENV CUDA_PKG_VERSION 10-1=$CUDA_VERSION-1
ENV NCCL_VERSION 2.8.3
ENV CUDNN_VERSION 7.6.5.32

# For libraries in the cuda-compat-* package: https://docs.nvidia.com/cuda/eula/index.html#attachment-a
RUN apt-get update && apt-get install -y --no-install-recommends \
    cuda-cudart-$CUDA_PKG_VERSION \
    cuda-compat-10-1 \
    cuda-libraries-$CUDA_PKG_VERSION \
    cuda-npp-$CUDA_PKG_VERSION \
    cuda-nvtx-$CUDA_PKG_VERSION \
    libcublas10=10.2.1.243-1 \
    libnccl2=$NCCL_VERSION-1+cuda10.1 \
    cuda-nvml-dev-$CUDA_PKG_VERSION \
    cuda-command-line-tools-$CUDA_PKG_VERSION \
    cuda-nvprof-$CUDA_PKG_VERSION \
    cuda-npp-dev-$CUDA_PKG_VERSION \
    cuda-libraries-dev-$CUDA_PKG_VERSION \
    cuda-minimal-build-$CUDA_PKG_VERSION \
    libcublas-dev=10.2.1.243-1 \
    libnccl-dev=2.8.3-1+cuda10.1 \
    libcudnn7=$CUDNN_VERSION-1+cuda10.1 \
    libcudnn7-dev=$CUDNN_VERSION-1+cuda10.1 \
    libgl1-mesa-glx \
    && ln -s cuda-10.1 /usr/local/cuda \
    && apt-mark hold libnccl2 libcublas10 libnccl-dev libcublas-dev libcudnn7 \
    && rm -rf /var/lib/apt/lists/*


# Required for nvidia-docker v1
RUN echo "/usr/local/nvidia/lib" >> /etc/ld.so.conf.d/nvidia.conf && \
    echo "/usr/local/nvidia/lib64" >> /etc/ld.so.conf.d/nvidia.conf

ENV PATH /usr/local/nvidia/bin:/usr/local/cuda/bin:${PATH}
ENV LD_LIBRARY_PATH /usr/local/nvidia/lib:/usr/local/nvidia/lib64
ENV LIBRARY_PATH /usr/local/cuda/lib64/stubs

# nvidia-container-runtime
ENV NVIDIA_VISIBLE_DEVICES all
ENV NVIDIA_DRIVER_CAPABILITIES compute,utility
ENV NVIDIA_REQUIRE_CUDA "cuda>=10.1 brand=tesla,driver>=396,driver<397 brand=tesla,driver>=410,driver<411 brand=tesla,driver>=418,driver<419"


##################
# onnx, opencv, onvif
##################
RUN pip --no-cache-dir install -i  https://mirrors.aliyun.com/pypi/simple \
    onnx==1.7 \
    onnxruntime-gpu==1.4.0 \
    opencv-python==******** \
    onvif_zeep \
    setproctitle \
    sqlalchemy_utils \
    pymysql \
    supervisor \
    shapely \
    matplotlib \
    py-spy \
    pyrasite \
    objgraph \
    scikit-learn \
    pyyaml \
    ratelimit \
    backoff-utils \
    dataclasses_json


RUN rm -rf /usr/local/arc \
    && mkdir /usr/local/arc \
    && wget http://release.fabu.ai/deps/arcanist.tar.gz \
    && tar zxvf arcanist.tar.gz -C /usr/local/arc \
    && wget http://release.fabu.ai/deps/libphutil.tar.gz \
    && tar zxvf libphutil.tar.gz -C /usr/local/arc \
    && ln -s /usr/local/arc/arcanist/bin/arc /usr/bin/arc

RUN rm -rf /etc/apt/sources.list.d/*.list
RUN apt-get update && apt-get install -y --no-install-recommends \
    xterm \
    curl \
    less \
    lsof \
    gdb \
    strace \
    php7.3 \
    php7.3-curl \
    silversearcher-ag && \
    apt-get clean autoclean && \
    rm -rf /var/lib/apt/lists/*

##################
#python3.8-dbg
##################
RUN rm -rf /etc/apt/sources.list.d/*.list && \
    echo "deb http://security.ubuntu.com/ubuntu bionic-security main universe" > /etc/apt/sources.list.d/gdb.list
RUN gpg --keyserver keyserver.ubuntu.com --recv-keys 3B4FE6ACC0B21F32 && \
    gpg --export --armor 3B4FE6ACC0B21F32 | apt-key add -
RUN apt-get update && apt-get install -y --no-install-recommends python3.8-dbg && \
    apt-get clean autoclean && \
    rm -rf /var/lib/apt/lists/*
RUN mkdir /usr/share/gdb/auto-load/usr/local && \
    ln -s /usr/share/gdb/auto-load/usr/bin /usr/share/gdb/auto-load/usr/local/bin && \
    rm -rf /etc/apt/sources.list.d/*.list