#!/usr/bin/env bash

# Fail on first error.
set -e

cd "$(dirname "${BASH_SOURCE[0]}")"

ARC_URI="http://release.fabu.ai/deps/arcanist.tar.gz"
PHUTIL_URL="http://release.fabu.ai/deps/libphutil.tar.gz"

rm -rf /usr/local/arc
mkdir /usr/local/arc
cd /usr/local/arc
wget ${ARC_URI}
wget ${PHUTIL_URL}
# cp /tmp/installers/arcanist.tar.gz .
# cp /tmp/installers/libphutil.tar.gz .
tar zxvf arcanist.tar.gz
tar zxvf libphutil.tar.gz
rm -rf arcanist.tar.gz libphutil.tar.gz

