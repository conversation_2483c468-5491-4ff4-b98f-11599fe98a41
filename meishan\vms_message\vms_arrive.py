import json
from dataclasses import dataclass
#from dataclasses_json import dataclass_json
import datetime
import time
import json
from enum import Enum
import google.protobuf.text_format as text_format
from common.logger import logger
from common.common_util import MyJosnEncoder2 as MyJosnEncoder
from cache.arrive_cache import ArriveCache
from meishan.vms_message.msgs import FMS_ARRIVE_TYPE, FMS_TO_NTOS_TOPIC, VmsArriveMsg, make_msg, \
    VMS_IECS_TRUCK_ARRIVE_TOPIC


def get_all_arrive_msgs(topic):
    if topic == VMS_IECS_TRUCK_ARRIVE_TOPIC:
        return ArriveCache().pop_all_arrive_gantry_msg()
    else:
        return ArriveCache().pop_all_arrive_msg()

def vms_arrive_handler(job_type, topic):
    msgs = list()
    infos = get_all_arrive_msgs(topic)
    for info in infos:
        vms_msg = VmsArriveMsg()
        for attr in vms_msg.__dict__:
            if hasattr(info, attr.lower()):
                setattr(vms_msg, attr, getattr(info, attr.lower()))
        vms_msg.WI_NO = str(vms_msg.WI_NO) if vms_msg.WI_NO else ""
        if vms_msg.WI_NO and info.twin_wi_no > 0:
            vms_msg.WI_NO = str(vms_msg.WI_NO) + ',' + str(info.twin_wi_no)
        if vms_msg.CNT_NO and info.twin_cnt_no:
            vms_msg.CNT_NO = vms_msg.CNT_NO + ',' + str(info.twin_cnt_no)
        vms_msg.ARRIVE_TIME = datetime.datetime.fromtimestamp(info.timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        msg = make_msg(job_type,vms_msg.__dict__)
        msg_json = json.dumps(msg.__dict__,cls=MyJosnEncoder).encode('utf-8')
        key_json = info.truck_no.encode('utf-8')
        logger.debug(f'key_json:{key_json},msg_json:{msg_json},info:{text_format.MessageToString(info, as_one_line=True)}')
        msgs.append((topic,key_json,msg_json))
    return msgs



if __name__ == '__main__':
    from proto.cache_pb2 import TpArriveMsg
    msg = TpArriveMsg()
    msg.truck_no = 'AT500'
    msg.timestamp = time.time()
    msg.arrive_type = 6
    msg.block_code = 'XF10'
    msg.arrive_state = 1
    msg.wi_no = 1000

    #梅山
    msg.cnt_no =  'CNT1'
    msg.twin_wi_no =  1001
    msg.twin_cnt_no =  'CNT2'
    while True:
        logger.info(f"@@@@@@@@")
        msgs = vms_arrive_handler(FMS_ARRIVE_TYPE, FMS_TO_NTOS_TOPIC)
        time.sleep(1)
        '''
        ArriveCache().push_arrive_msg(msg)
        time.sleep(10)
        '''

