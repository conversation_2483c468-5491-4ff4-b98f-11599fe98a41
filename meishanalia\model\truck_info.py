import traceback
from typing import Dict
import datetime
import sys

from sqlalchemy import Column, Integer, String, DateTime,Float
from dataclasses import dataclass
from meishanalia.common.constant import TABLE_TRUCK_INFO
from common.logger import logger
from meishanalia.communication.db import TosSession
from meishanalia.model.table_info import TruckInfo

def query_by_name(truck_no):
    session = TosSession().acquire()
    try:
        info = session.query(TruckInfo).filter(TruckInfo.TRUCK_NO == truck_no).first()
        return info
    except Exception as e:
        logger.warning(f"get TruckInfo err:{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def query_all():
    session = TosSession().acquire()
    try:
        info = session.query(TruckInfo).all()
        return info
    except Exception as e:
        logger.warning(f"get TruckInfo err:{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()


def insert(record: TruckInfo):
    session = TosSession().acquire()
    try:
        cur = session.query(TruckInfo).filter(TruckInfo.TRUCK_NO == record.TRUCK_NO).first()
        if cur is None:
            record.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            record.UPDATE_TIME = record.INSERT_TIME
            logger.debug(f"Insert TruckInfo table:{record.__dict__}")
            session.add(record)  #
        else:
            record.UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            logger.debug(f"Update TruckInfo table new:{record.__dict__}")
            for key in record.__dict__:
                if key == '_sa_instance_state':
                    continue
                setattr(cur, key, getattr(record, key))
        session.commit()
    except Exception as e:
        logger.warning(f"Insert TruckInfo err:{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def update_by_truck(truck_no,update_column:dict):
    session = TosSession().acquire()
    try:
        update_column['UPDATE_TIME'] = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
        logger.info(f'[update_by_truck] update_column:{update_column}')
        session.query(TruckInfo).filter(TruckInfo.TRUCK_NO == truck_no).update(update_column)
        session.commit()
    except Exception as e:
        logger.warning(f"update TruckInfo err:{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def update_truck_offline(truck_no):
    update_column = dict()
    update_column['TRUCK_MODE'] = 0
    update_by_truck(truck_no,update_column)


if __name__ == '__main__':
  if len(sys.argv) < 2:
      print('please set fun bits mask:\n')
      print('0xFF:all test\n')
      print('0x01:update_truck_offline\n')
  else:
      mask = 0
      if sys.argv[1].startswith('0x'):
          mask = int(sys.argv[1], 16)
      else:
          mask = int(sys.argv[1])

      if mask & 0x1:
          print("update_truck_offline########################")
          update_truck_offline('AT800')
