import datetime
import json
import time
import traceback

from common.constant import KAFKA_DEBUG, TOPIC_JOB_TYPE_MAPPING, FMS_PRE_WI_TYPE
from common.logger import logger, init_vms_consumer_log
from common.periodical_task import PeriodicalTask
from common.singleton import Singleton
from config.config_manage import ConfigManage
from meishan.vms_message.vms_gps_info import vms_gps_info_handler
from meishan.vms_message.vms_wi_info import vms_wi_info_handler
from model.kafka_consumer import KafkaConsumers
from meishan.vms_message.msgs import VMS_IECS_INFO_TOPIC, VMS_STACK_INFO_TYPE, NTOS_TO_FMS_TOPIC, FMS_RTGWORKPOS_TYPE, \
    VMS_GPS_INFO_TOPIC, VMS_GPS_INFO_TYPE
from meishan.vms_message.vms_stack_info import vms_stack_info_handler


#新的kafka,带认证
class VmsConsumers(KafkaConsumers, metaclass=Singleton):
    def __init__(self):
        self._enable = ConfigManage().get_config_kafka_consumer_enable()
        self._server_list = ConfigManage().get_config_kafka_nodes()
        self._group_id = ConfigManage().get_config_kafka_group_id()  # KAFKA_GROUP_ID
        self._sasl_config = ConfigManage().get_config_kafka_sasl()
        # self._topics_offset_ms = {FMS_AHT_CONTROL_INFO_TOPIC:int(time.time() * 1000) - 3600*1000} #设置一个小时之前的偏移
        self._topics_offset_ms = {}
        self._topics = [VMS_IECS_INFO_TOPIC,NTOS_TO_FMS_TOPIC,VMS_GPS_INFO_TOPIC]
        KafkaConsumers.__init__(self, server_list=self._server_list, topics=self._topics,\
                                group_id=self._group_id, topics_offset_ms=self._topics_offset_ms, sasl_config=self._sasl_config, topic_job_type_mapping=TOPIC_JOB_TYPE_MAPPING)

    def start_consumer(self):
        if self._enable and len(self._server_list) > 0:
            handlers = dict()
            handlers[VMS_STACK_INFO_TYPE] = vms_stack_info_handler
            handlers[VMS_GPS_INFO_TYPE] = vms_gps_info_handler
            handlers[FMS_PRE_WI_TYPE] = vms_wi_info_handler
            self.start_work(handlers)


if __name__ == '__main__':
    init_vms_consumer_log()
    VmsConsumers().start_consumer()
