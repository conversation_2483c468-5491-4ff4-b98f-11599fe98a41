from common.singleton import Singleton
from common.logger import logger
from plc.crane_receiver import CraneInfoContainer
from plc.gantry_receiver import GantryInfoContainer
from plc.remote_info import PlcRemoteInfo
from ship.map_ships import MapShips
from meishanalia.tos_db_alia import TosDb


class PlcRemoteReceiver(PlcRemoteInfo, metaclass=Singleton):
    def __init__(self):
        self._db = TosDb()
        self._crane_info_container = CraneInfoContainer()
        self._gantry_info_container = GantryInfoContainer()
        self._map_ships = MapShips()
        self._running = False
        self._crane_interval_time = 0.05
        self._gantry_interval_time = 0.05
        self._crane_update_lanes_interval = 0.1
        self._crane_update_lanes_count = 0
        self._crane_lanes = dict()
        self._crane_lanes_debug = dict()
        self._crane_update_lanes_loop_thread = None
        self._crane_loop_thread = None
        self._gantry_loop_thread = None
        self._debug_crane_enable = True

   
if __name__ == '__main__':
    init_logger("test_remote_info")
    PlcRemoteReceiver().start()
