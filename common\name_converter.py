from common.singleton import Singleton
from common.logger import logger
from config.config_manage import ConfigManage

class NameConverter(metaclass=Singleton):
    def __init__(self):
        self._name_to_no = dict()
        self._no_to_name = dict()
        self._max_name_num = 0
        if ConfigManage().is_ms_scene() or ConfigManage().is_iecs_scene():
            self.init_nb_name()


        elif <PERSON>().is_yz_scene() or ConfigManage().is_yz_cs_scene():
            self.init_yz_name()
        else:
            logger.warning("UNKNOWN ENVIRONMENT of NameConverter")

    def add(self, name, no):
        if name is None or no is None:
            logger.warning(f'invalid vehicle name:{name} or no:{no}')
            return
        self._name_to_no[name] = no
        self._no_to_name[no] = name

    def to_name(self, no):
        if no is None:
            return None
        return self._no_to_name[no] if no in self._no_to_name else None

    def to_no(self, name):
        if name is None:
            return None
        return self._name_to_no[name] if name in self._name_to_no else None

    def all_truck_no(self):
        return self._no_to_name.keys()

    def is_truck_no_exist(self, no):
        return no in self._no_to_name

    def all_truck_name(self):
        return self._name_to_no.keys()

    def is_truck_name_exist(self, name):
        return name in self._name_to_no

    @staticmethod
    def is_debug_truck_no(truck_no):
        if ConfigManage().is_yz_scene() or ConfigManage().is_yz_cs_scene():
            return truck_no.startswith('IGV') or truck_no.startswith('AT')
        else:
            return truck_no.startswith('AT8') or truck_no.startswith('AT9')

    def is_debug_vehicle(self, vehicle_name):
        return vehicle_name in self._name_to_no and self.is_debug_truck_no(self._name_to_no[vehicle_name])

    @staticmethod
    def is_prod_truck_no(truck_no):
        if ConfigManage().is_yz_scene() or ConfigManage().is_yz_cs_scene():
            return truck_no.startswith('T')
        else:
            return truck_no.startswith('AT5') or truck_no.startswith('AT6')

    def is_prod_vehicle(self, vehicle_name):
        return vehicle_name in self._name_to_no and self.is_prod_truck_no(self._name_to_no[vehicle_name])

    def init_nb_name(self):
        if ConfigManage().is_sim_mode():
            vehicle_num = 150
        else:
            vehicle_num = 120
        for i in range(0, vehicle_num):
            howo_name = 'howo' + str((i+1))
            at_name = 'AT' + str((500+i))
            self.add(howo_name, at_name)
            howo_name_test = 'howo' + str((i+1)) + '_test'
            at_name_test = 'AT' + str((800+i+1))
            self.add(howo_name_test,at_name_test)
            # logger.info(f'add AT5:{howo_name},{at_name}')
            # logger.info(f'add AT8:{howo_name_test},{at_name_test}')
        self.add('fabutest', 'AT800')

    def init_yz_name(self):
        if ConfigManage().is_sim_mode():
            vehicle_num = 99
        else:
            vehicle_num = 99
        for i in range(1, vehicle_num):
            # 13,14被yzzhenhua占用
            if i in (13, 14):
                continue
            nt_name = 'yzguotang' + str(i)
            at_name = 'T8' + '%02d' % i
            self.add(nt_name, at_name)
            nt_name = 'yzguotang' + str(i) + '_test'
            at_name = 'IGV8' + '%02d' % i
            self.add(nt_name, at_name)
            #logger.info(f'nt_name:{nt_name},at_name:{at_name}')
        # fabuhowo测试车，只有1辆
        # fabuhowo1已经不用了，以下代码注释
        # self.add('fabuhowo1', 'T599')
        # self.add('fabuhowo1_test', 'AT599')
        # 兼容旧的版本甬舟集卡
        for i in range(1, 10):
            nt_name = 'yzhowo' + str(i)
            at_name = 'T3' + '%02d' % i
            self.add(nt_name, at_name)
            nt_name = 'yzhowo' + str(i) + '_test'
            at_name = 'AT3' + '%02d' % i
            self.add(nt_name, at_name)
        # 正式甬舟集卡
        for i in range(61, 150):
            # # 599暂时跳过
            # if i == 99:
            #     continue
            nt_name = 'yzhowo' + str(i)
            at_name = 'T' + '%03d' % (i + 500)
            self.add(nt_name, at_name)
            nt_name = 'yzhowo' + str(i) + '_test'
            at_name = 'AT' + '%03d' % (i + 500)
            self.add(nt_name, at_name)
        # 振华IGV
        for i in range(1, 3):
            nt_name = 'yzzhenhua' + str(i)
            at_name = 'T8' + '%02d' % (i + 12)
            self.add(nt_name, at_name)
            nt_name = 'yzzhenhua' + str(i) + '_test'
            at_name = 'IGV8' + '%02d' % (i + 12)
            self.add(nt_name, at_name)
        self.add('fabutest', 'IGV800')

    @staticmethod
    def to_vehicle_type(no):
        if str(no).startswith('IGV') or str(no).startswith('T8'):
            return 'igv'
        else:
            return 'howo'

if __name__ == '__main__':
    print(NameConverter.to_vehicle_type('IGV800'))
    print(NameConverter.is_debug_truck_no('AT800'))
    print(NameConverter().is_debug_vehicle('howo22_test'))
    print(NameConverter().all_truck_no())
