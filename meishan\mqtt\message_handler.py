import json
import os
import re
import time
from datetime import datetime

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend

from cache import config_cache
from cache.map_cache import MapCache
from common.constant import CHANGE_STATION_ID_MSBSS02
from common.crypto_utils import rsa_decrypt_long, rsa_encrypt_long, aes_encrypt_cbc_base64, aes_decrypt_cbc_base64
from common.logger import logger
from common.name_converter import NameConverter
from config.config_manage import ConfigManage
from meishan.mqtt.msgs import *
from os import urandom
from proto.cache_pb2 import ChangeReqInfo
from cache import vehicle_cache

current_dir = os.path.dirname(__file__)
private_key_path = os.path.abspath(
    os.path.join(current_dir, "../../data/meishan/key/private_key.pem")
)


# 加载私钥
with open(private_key_path, "rb") as key_file:
    private_key = serialization.load_pem_private_key(
        key_file.read(),
        password=None,
        backend=default_backend()
    )

# 生成 AES 密钥和 向量
def generate_aes_key_and_iv():
    aes_key = urandom(16)  # 16 字节 AES-128
    aes_iv = urandom(16)
    return aes_key, aes_iv


def encrypt_key_req_handle(client, userdata, message):
    response = {
        "result": 0,
        "error": "",
        "encryptKey": "",
        "encryptVector": "",
        "signKey": "",
    }
    index = 0
    function = ""
    try:
        encrypted_b64_str = message.payload.decode()
        logger.debug(f"收到密钥响应: {encrypted_b64_str}")

        payload_bytes = rsa_decrypt_long(private_key, encrypted_b64_str)
        payload = payload_bytes.decode("utf-8").strip('\x00')
        if not payload.strip():
            return
        data_json = json.loads(payload)

        logger.debug(f"解密后的原文: {data_json}")

        # 提取并存储密钥
        key = data_json["dataBody"]["publicKey"]
        index = data_json["header"]["index"]
        function = data_json["header"]["function"]
        config_cache.ConfigCache().store_platform_key(key)
        aes_key, aes_iv = generate_aes_key_and_iv()
        response["encryptKey"] = aes_key.hex()
        response["encryptVector"] = aes_iv.hex()
        response["result"] = 1

    except Exception as e:
        response["result"] = 2
        response["error"] = str(e)
        logger.debug(f"解密失败: {e}")

    topic = MQTT_ENCRYPT_KEY_RESP_TOPIC
    fms_response_handle(client, function, response, index, topic)
    config_cache.ConfigCache().store_platform_aes_info(response["encryptKey"], response["encryptVector"])


def keep_alive_up_handle(client, userdata, message):
    try:
        keep_alive_msg = message.payload.decode()
        logger.debug(f"收到站控心跳: {keep_alive_msg}")
        time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        client.publish(
            MQTT_KEEPALIVE_DOWN_TOPIC, time
        )
        logger.debug(f"回复站控心跳: topic:{MQTT_KEEPALIVE_DOWN_TOPIC}, {time}")
    except Exception as e:
        logger.debug(f"处理心跳失败: {e}")


def handle_encrypted_message(client, message, handler_map, context_desc=""):
    try:
        encrypted_payload = message.payload.decode()
        logger.debug(f"收到{context_desc}数据: {encrypted_payload}")

        key, iv = config_cache.ConfigCache().get_platform_aes_info()
        payload_bytes = aes_decrypt_cbc_base64(key, iv, encrypted_payload)
        payload = payload_bytes.decode("utf-8")
        logger.debug(f"解密后的{context_desc}数据: {payload}")

        data_json = json.loads(payload)

        header = data_json.get("header", {})
        data_body = data_json.get("dataBody", {})
        function = header.get("function")
        index = header.get("index")

        handler = handler_map.get(function, handle_unknown_function)
        handler(client, data_body, index)
    except json.JSONDecodeError as e:
        logger.error(f"{context_desc}JSON解析失败: {e}, 原始数据: {message.payload}")
    except Exception as e:
        logger.exception(f"处理{context_desc}数据时出错: {e}")


def send_encrypted_message(
    client,
    topic,
    function,
    data_body,
    index=None,
    reason=0,
    is_response=False,
    context_desc="",
    is_rsa=False,
):
    message_json = build_mqtt_message(
        function=function,
        reason=reason,
        dataBody=data_body,
        is_response=is_response,
        index=index,
        is_rsa=is_rsa
    )

    try:
        if is_rsa:
            # 获取站控加密公钥
            platform_key_str = config_cache.ConfigCache().get_platform_public_key()
            if platform_key_str is None:
                logger.error("can't find pub1")
                return

            # 自动添加公钥头尾
            if not platform_key_str.startswith("-----BEGIN PUBLIC KEY-----"):
                platform_key_str = "-----BEGIN PUBLIC KEY-----\n" + platform_key_str
            if not platform_key_str.endswith("-----END PUBLIC KEY-----"):
                platform_key_str = platform_key_str + "\n-----END PUBLIC KEY-----"

            # 转换为公钥对象
            public_key_obj = serialization.load_pem_public_key(
                platform_key_str.encode(),
                backend=default_backend()
            )

            # 用公钥加密整个 JSON 消息
            encrypted_str = rsa_encrypt_long(public_key_obj, message_json.encode("utf-8"))
        else:
            encrypted_str = message_json
        # 发布消息
        result = client.publish(topic, encrypted_str)
        logger.debug(f"{context_desc} 发送加密消息到 {topic}: {encrypted_str}, 原始数据: {message_json}, 返回值:{result}")
    except json.JSONDecodeError as e:
        logger.error(f"{context_desc}JSON处理失败: {e}, 原始数据: {message_json}")
    except Exception as e:
        logger.exception(f"处理{context_desc}数据时出错: {e}")


def station_request_handle(client, userdata, message):
    handle_encrypted_message(
        client, message, function_handler_map, context_desc="站控 request"
    )


def station_response_handle(client, userdata, message):
    handle_encrypted_message(
        client, message, function_handler_map, context_desc="站控 response"
    )


def fms_request_handle(client, function, data_body):
    topic = MQTT_FMS_REQUEST_TOPIC
    send_encrypted_message(
        client,
        topic,
        function,
        data_body,
        index=None,
        reason=0,
        is_response=False,
        context_desc="FMS request",
        is_rsa=False
    )


def fms_response_handle(client, function, data_body, index, topic):
    topic = topic if topic else MQTT_FMS_RESPONSE_TOPIC
    is_rsa = False
    if topic == MQTT_ENCRYPT_KEY_RESP_TOPIC:
        is_rsa = True
    send_encrypted_message(
        client,
        topic,
        function,
        data_body,
        index,
        reason=0,
        is_response=True,
        context_desc="FMS response",
        is_rsa=is_rsa
    )

location_result_index = 0


def handle_location_result_req(client, data_body, index):
    global location_result_index
    location_result_index = index
    logger.debug(f"处理定位检测结果: {data_body}")
    vin = data_body.get("vin", "")
    plateNo = data_body.get("plateNo", "")
    result = data_body.get("result")
    vehicle_pos_dev = data_body.get("vehiclePosDev")
    offset_a, offset_b = vehicle_pos_dev.strip().split(",")
    MapCache().hdel_cps_align_msg(get_env_vehicle_no(plateNo))
    data = {
        "station_id": CHANGE_STATION_ID_MSBSS02,
        "truck_id": get_env_vehicle_no(plateNo),
        "inposition": result,
        "offset": int(offset_a),
        "device_status": result,
        "device_status_timestamp": int(time.time() * 1000),
    }
    MapCache().hset_cps_align_msg_dict(data)
    info = ChangeReqInfo()
    info.vin = vin
    info.plateNo = get_env_vehicle_no(plateNo)
    info.lane = 1
    if result == 1:
        info.result = result
        send_location_result_resp(client, info)
    else:
        send_location_detection_req(client, info)

swap_finish_index = 0


def handle_swap_finish_req(client, data_body, index):
    logger.debug(f"处理自动换电完成请求: {data_body}, index: {index}")
    global swap_finish_index
    swap_finish_index = index
    vin = data_body.get("vin", "")
    plateNo = data_body.get("plateNo", "")
    data = {
        "station_id": CHANGE_STATION_ID_MSBSS02,
        "truck_id": get_env_vehicle_no(plateNo),
        "drive": 1,
        "drive_timestamp": int(time.time() * 1000),
    }
    MapCache().hset_cps_align_msg_dict(data)
    data_body = {
        "vin": vin,
        "plateNo": plateNo,
        "lane": 1,
        "result": 1,
    }
    send_swap_finish_resp(client, data_body)


def handle_location_detection_resp(client, data_body, index):
    logger.debug(f"定位检测车辆位置回复结果, {data_body}, index: {index}")
    plateNo = data_body.get("plateNo", "")
    result = data_body.get("result")
    data = {
        "station_id": CHANGE_STATION_ID_MSBSS02,
        "truck_id": get_env_vehicle_no(plateNo),
        "device_status": result if result == 1 else 0,
        "device_status_timestamp": int(time.time() * 1000),
    }
    MapCache().hset_cps_align_msg_dict(data)


def handle_dispatch_swap_start_resp(client, data_body, index):
    logger.debug(f"调度启动换电回复: {data_body}, index: {index}")
    plateNo = data_body.get("plateNo", "")
    result = data_body.get("result")
    data = {
        "station_id": CHANGE_STATION_ID_MSBSS02,
        "truck_id": get_env_vehicle_no(plateNo),
        "device_status": 2 if result == 1 else 0,
        "device_status_timestamp": int(time.time() * 1000),
    }
    MapCache().hset_cps_align_msg_dict(data)


def handle_unknown_function(client, data_body, index):
    logger.debug(f"未知功能标识数据: {data_body}, index: {index}")


def send_location_detection_req(client, info):
    function = FUNCTION_LOCATION_DETECTION_REQ
    plateNo = to_formal_vehicle_no(info.plateNo)
    data_body = {
        "vin": info.vin,
        "plateNo": plateNo,
        "lane": info.lane,
        "soc": info.soc,
        "batID": info.batID,
    }
    fms_request_handle(client, function, data_body)


def send_location_result_resp(client, info):
    function = FUNCTION_LOCATION_RESULT_RESP
    plateNo = to_formal_vehicle_no(info.plateNo)
    data_body = {
        "vin": info.vin,
        "plateNo": plateNo,
        "lane": info.lane,
        "result": info.result,
    }
    fms_response_handle(client, function, data_body, location_result_index, topic=None)


def send_dispatch_swap_start_req(client, info):
    function = FUNCTION_DISPATCH_SWAP_START_REQ
    plateNo = to_formal_vehicle_no(info.plateNo)
    data_body = {
        "vin": info.vin,
        "plateNo": plateNo,
        "lane": info.lane,
    }
    fms_request_handle(client, function, data_body)


def send_swap_finish_resp(client, data_body):
    function = FUNCTION_SWAP_FINISH_RESP
    fms_response_handle(client, function, data_body, swap_finish_index, topic=None)

def to_formal_vehicle_no(plate_no):
    """
    将车号统一转换为正式车号（如 AT556、AT857 均返回 T556）
    """
    if not plate_no:
        return None
    converter = NameConverter()
    # 如果是调试车牌，比如 AT857
    if converter.is_debug_truck_no(plate_no):
        name = converter.to_name(plate_no)
        base_name = extract_base_name(name)
        if base_name:
            prod_no = converter.to_no(base_name)
            return prod_no[1:]  # e.g., T556
    # 如果本身就是正式车，比如 AT556
    elif converter.is_prod_truck_no(plate_no):
        return plate_no[1:]
    logger.warning(f"Unrecognized or unmapped plate_no: {plate_no}")
    return None

def get_env_vehicle_no(plate_no):
    """
    将正式车号转换为的实际车牌编号：
    """
    if not plate_no:
        return None

    converter = NameConverter()
    online_trucks = vehicle_cache.get_online_trucks()

    if not online_trucks:
        logger.warning("No online trucks found")
        return None
    if plate_no.startswith('T'):
        no = 'AT' + plate_no[1:]
        if no in online_trucks:
            return no
        else:
            name = converter.to_name(no)
            if not name:
                logger.warning(f"Cannot find name for plate_no: {plate_no}")
                return None
            test_name = name + "_test"
            test_no = converter.to_no(test_name)
            if not test_no:
                logger.warning(f"Cannot find test_no for test_name: {test_name}")
                return None
            if test_no not in online_trucks:
                logger.warning(f"Test_no {test_no} not in online trucks")
                return None
            return test_no
    else:
        logger.warning(f"Invalid plate format: {plate_no}")
        return None

def extract_base_name(name):
    if not name:
        return None
    match = re.match(r'(\w+\d+)_test$', name)
    return match.group(1) if match else None

# 递增 index，初始为 0
message_index = 0


def build_mqtt_message(
    function: str = "",
    reason: int = 0,
    dataBody: dict = {},
    is_response: bool = False,
    index: int = None,
    is_rsa: bool = False,
):
    global message_index

    # 如果是响应类消息，index 从请求报文继承
    # 否则，自增 index 并取模 2^32
    if is_response and index is not None:
        msg_index = index
    else:
        message_index = (message_index + 1) % (2**32)
        msg_index = message_index
    header = {
        "version": "V1.0.0",
        "timeStamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "index": msg_index,
        "function": function,
        "reason": reason,
    }

    payload = {
        "header": header,
        "dataBody": dataBody,
    }

    if not is_rsa:
        # 获取 AES 密钥和 IV
        key, iv = config_cache.ConfigCache().get_platform_aes_info()
        if key is None or iv is None:
            logger.error("找不到 AES 密钥或 IV")
            return

        # 使用 AES 加密整个 payload（即 header + dataBody）
        plaintext = json.dumps(payload, ensure_ascii=False).encode("utf-8")
        logger.debug(f"数据明文:{plaintext}")
        encrypted_payload = aes_encrypt_cbc_base64(key, iv, plaintext)
        return encrypted_payload
    else:
        logger.debug(f"数据明文:{json.dumps(payload, ensure_ascii=False)}")
        # RSA 场景下直接返回 JSON 字符串
        return json.dumps(payload, ensure_ascii=False)


MQTT_TOPIC_CALLBACK_MAPPING = {
    MQTT_ENCRYPT_KEY_REQ_TOPIC: encrypt_key_req_handle,
    MQTT_KEEPALIVE_UP_TOPIC: keep_alive_up_handle,
    MQTT_STATION_REQUEST_TOPIC: station_request_handle,
    MQTT_STATION_RESPONSE_TOPIC: station_response_handle,
}

function_handler_map = {
    FUNCTION_LOCATION_RESULT_REQ: handle_location_result_req,
    FUNCTION_SWAP_FINISH_REQ: handle_swap_finish_req,
    FUNCTION_LOCATION_DETECTION_RESP: handle_location_detection_resp,
    FUNCTION_DISPATCH_SWAP_START_RESP: handle_dispatch_swap_start_resp,
    FUNCTION_LOCATION_DETECTION_REQ: send_location_detection_req,
    FUNCTION_LOCATION_RESULT_RESP: send_location_result_resp,
    FUNCTION_DISPATCH_SWAP_START_REQ: send_dispatch_swap_start_req
}

if __name__ == "__main__":
    print()
