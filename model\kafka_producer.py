import time
import json
import time
import traceback
from collections import defaultdict
from common.logger import logger
from common.periodical_task import PeriodicalTask
from model.kafka_server import KafkaMsgProducer, KafkaMsgPartitioner


class MyEncoder(json.JSONEncoder):
    def default(self, o):
        return json.loads(
            json_format.MessageToJson(o, including_default_value_fields=True, preserving_proto_field_name=True))


class KafkaProducers(object):
    def __init__(self,server_list,topics = {},interval_time = 1000,sasl_config = {},master_only = True):
        self._server = server_list
        self._topics = topics
        self._interval_time = interval_time/1000  # ms
        self._master_only = master_only
        self._partitioner_count = 0
        self._handlers = {}
        self._sasl_config = sasl_config
        #self._handlers = defaultdict(dict)
        self._producer = None

    def __init_connect(self):
        # logger.info(f'kafak producer server {self._server}')
        self._producer = KafkaMsgProducer(self._server,sasl_config=self._sasl_config)
        for topic in self._topics:
            producer_partitioner = KafkaMsgPartitioner(self._server, topic)
            if self._partitioner_count != 0:
                if producer_partitioner.set_partitioner(self._partitioner_count):
                    logger.info(f'set partitioner:{self._partitioner_count} count sucessfully')
                else:
                    logger.warning(f'set partioner:{self._partitioner_count} count failed!!')

    def __handle_message(self):
        if self._producer is not None and self._producer.connect():
            start = time.time()
            for msg_type in self._handlers:
                msgs = self._handlers[msg_type]()
                for msg in msgs:
                    try:
                        msg_topic = msg[0]
                        msg_key = msg[1]
                        msg_context = msg[2]
                        logger.debug(f'vmsproducer:send topic:{msg_topic} msg_key:{msg_key}')
                        # logger.debug(f'vmsproducer:send topic:{msg_topic} msg_key:{msg_key},msg_context:{msg_context}')
                        self._producer.send(msg_topic, msg_context, msg_key)
                    except Exception as e:
                        logger.warning(f"[handle_message] err:{e}, trace:{traceback.format_exc()}")
            interval_time = time.time() - start
            logger.debug(f"send handle_message loop cost:{interval_time}")
            if interval_time > 1.1:
                logger.warning(
                    f"send handle_message loop over cost execeed round time:{round(interval_time)}, execeed time:{interval_time}")
        else:
            logger.warning(f'[handle_message] err:fail to connect server:{self._server}')

    def __register_handler(self, handler_type: str, handler):
        self._handlers[handler_type] = handler

    def __start_task(self):
        self.__init_connect()
        self._producer_thread = PeriodicalTask(target=self.__handle_message, interval=self._interval_time, master_only= self._master_only)
        self._producer_thread.start()

    def __register_handlers(self,handlers:dict):
        for handler_type,handler in handlers.items():
            self.__register_handler(handler_type, handler)

    def start_work(self,handlers:dict = {}):
        self.__register_handlers(handlers)
        self.__start_task()

    def start_producer(self):
        self.start_work()


if __name__ == '__main__':
    pass
