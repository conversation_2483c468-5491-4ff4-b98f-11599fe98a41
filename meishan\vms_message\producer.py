import time
import json
import time
import traceback
from collections import defaultdict
from functools import partial

from common.logger import logger, init_vms_producer_log
from common.periodical_task import PeriodicalTask
from common.singleton import Singleton
from config.config_manage import ConfigManage
from model.kafka_producer import KafkaProducers
from meishan.vms_message.msgs import FMS_ARRIVE_TYPE, FMS_TO_NTOS_TOPIC, VMS_IECS_TRUCK_ARRIVE_TYPE, VMS_IECS_TRUCK_ARRIVE_TOPIC
from meishan.vms_message.vms_arrive import vms_arrive_handler

#新的kafka,带认证
class VmsProducers(KafkaProducers, metaclass=Singleton):
    def __init__(self):
        self._enable = ConfigManage().get_config_kafka_producer_enable()
        self._server_list = ConfigManage().get_config_kafka_nodes()
        self._sasl_config = ConfigManage().get_config_kafka_sasl()
        KafkaProducers.__init__(self,server_list = self._server_list,sasl_config = self._sasl_config,master_only = True)

    def start_producer(self):
        if self._enable and len(self._server_list) > 0:
            handlers = {}
            handlers[FMS_ARRIVE_TYPE] = partial(vms_arrive_handler, FMS_ARRIVE_TYPE, FMS_TO_NTOS_TOPIC)
            handlers[VMS_IECS_TRUCK_ARRIVE_TYPE] = partial(vms_arrive_handler, VMS_IECS_TRUCK_ARRIVE_TYPE, VMS_IECS_TRUCK_ARRIVE_TOPIC)
            self.start_work(handlers)
        else:
            logger.info('no need to vms start producer')


if __name__ == '__main__':
    init_vms_producer_log()
    VmsProducers().start_producer()
