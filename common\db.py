import os
import traceback

import cx_Oracle

from common.logger import logger
from common.singleton import Singleton
from config.config_manage import ConfigManage


class SessionPool(metaclass=Singleton):
    def __init__(self):
        self.sql_mx_cons = ConfigManage().get_config_oracle_max_connections()
        dsn_tns = cx_Oracle.makedsn(
            os.environ.get('ANTENNA_DB_IP', ConfigManage().get_config_oracle_ip()),
            os.environ.get('ANTENNA_DB_PORT', ConfigManage().get_config_oracle_port()),
            os.environ.get('ANTENNA_DB_SID', ConfigManage().get_config_oracle_sid()))
        logger.info(f"SessionPool dsn:{dsn_tns},sql_mx_cons:{self.sql_mx_cons}")
        username = ConfigManage().get_config_oracle_username()
        password = ConfigManage().get_config_oracle_password()
        self._pool = cx_Oracle.SessionPool(username, password, dsn_tns, min=1, max=self.sql_mx_cons, increment=1,
                                           threaded=True)

    def acquire(self):
        return self._pool.acquire()

    def release(self, conn):
        self._pool.release(conn)


def makedict(cursor):
    cols = [d[0] for d in cursor.description]

    def createrow(*args):
        return dict(zip(cols, args))

    return createrow

#即使不commit退出execute_dml后会自动解锁,应该是在connection解析的时候,但无法使数据更新有效
def execute_dml(sql, args_table={}):
    connection = SessionPool().acquire()
    try:
        #执行超时时间,即使死锁也可以返回ms
        #connection.callTimeout = 10000
        cur = connection.cursor()
        cur.execute(sql, args_table)
        connection.commit()
        return True
    except Exception as e:
        logger.warning(f"sql: {sql}, arg: {args_table}")
        logger.warning(f"[execute_dml] trace: {traceback.format_exc()}, error:{e}")
        return False
    finally:
        SessionPool().release(connection)


def execute_query(sql, args_table={}, default_ret=[]):
    connection = SessionPool().acquire()
    ret = default_ret
    try:
        cur = connection.cursor()
        cur.execute(sql, args_table)
        cur.rowfactory = makedict(cur)
        ret = cur.fetchall()
        return ret
    except Exception as e:
        logger.warning(f"sql: {sql}, arg: {args_table}")
        logger.warning(f"[execute_query] trace: {traceback.format_exc()}, error:{e}")
        return ret
    finally:
        SessionPool().release(connection)

if __name__ == '__main__':
    oracle_db = SessionPool()
