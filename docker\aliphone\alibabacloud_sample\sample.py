# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys

from typing import List
from Tea.core import TeaCore

from alibabacloud_dyvmsapi20170525.client import Client as Dyvmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dyvmsapi20170525 import models as dyvmsapi_20170525_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client(
        access_key_id: str,
        access_key_secret: str,
    ) -> Dyvmsapi20170525Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 您的AccessKey ID,
            access_key_id="LTAI4GKoMEBYj4HFmh8tpZbk",
            # 您的AccessKey Secret,
            access_key_secret="******************************"   # 不可泄露
        )
        # 访问的域名
        config.endpoint = 'dyvmsapi.aliyuncs.com'
        return Dyvmsapi20170525Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client('ACCESS_KEY_ID', 'ACCESS_KEY_SECRET')
        single_call_by_voice_request = dyvmsapi_20170525_models.SingleCallByVoiceRequest(
            called_number='13567120091', # 这里填手机号，最多支持同时呼叫10个号码
            voice_code='16751298-7325-487f-a741-6db835d90d8b.wav' # 这里填录音ID
        )
        resp = client.single_call_by_voice(single_call_by_voice_request)
        ConsoleClient.log(UtilClient.to_jsonstring(TeaCore.to_map(resp)))

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client('ACCESS_KEY_ID', 'ACCESS_KEY_SECRET')
        single_call_by_voice_request = dyvmsapi_20170525_models.SingleCallByVoiceRequest(
            called_number='13567120091',
            voice_code='16751298-7325-487f-a741-6db835d90d8b.wav'
        )
        resp = await client.single_call_by_voice_async(single_call_by_voice_request)
        ConsoleClient.log(UtilClient.to_jsonstring(TeaCore.to_map(resp)))


if __name__ == '__main__':
    Sample.main(sys.argv[1:])
