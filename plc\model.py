

import dataclasses as dataclasses
import time,datetime
from proto.cache_pb2 import CraneStoreInfo,GantryStoreInfo
from common.constant import SIM_MODE

@dataclasses.dataclass
class PlcGposSnapshot:
    g_pos: int
    timestamp_ns: int


STORE_CRANE_MSGS_12150_START_ADDR = 12150
STORE_CRANE_MSGS_12150 = {
    'header': (STORE_CRANE_MSGS_12150_START_ADDR, 1, Fals<PERSON>),
    'crane_no': (STORE_CRANE_MSGS_12150_START_ADDR + 1, 1, False),
    'msg_no': (STORE_CRANE_MSGS_12150_START_ADDR + 2, 1, False),
    'year': (STORE_CRANE_MSGS_12150_START_ADDR + 3, 1, False),
    'month': (STORE_CRANE_MSGS_12150_START_ADDR + 4, 1, False),
    'day': (STORE_CRANE_MSGS_12150_START_ADDR + 5, 1, False),
    'hour': (STORE_CRANE_MSGS_12150_START_ADDR + 6, 1, <PERSON>als<PERSON>),
    'min': (STORE_CRANE_MSGS_12150_START_ADDR + 7, 1, False),
    'sec': (STORE_CRANE_MSGS_12150_START_ADDR + 8, 1, False),
    'msec': (STORE_CRANE_MSGS_12150_START_ADDR + 9, 1, False),
    'truck_no': (STORE_CRANE_MSGS_12150_START_ADDR + 10, 1, False),
    'wi_id': (STORE_CRANE_MSGS_12150_START_ADDR + 11, 2, False),
    'via_status': (STORE_CRANE_MSGS_12150_START_ADDR + 13, 1, False),
}

STORE_CRANE_MSGS_11100_START_ADDR = 11100
STORE_CRANE_MSGS_11100 = {
    'header': (STORE_CRANE_MSGS_11100_START_ADDR, 1, False),
    'crane_no': (STORE_CRANE_MSGS_11100_START_ADDR + 1, 1, False),
    'msg_no': (STORE_CRANE_MSGS_11100_START_ADDR + 2, 1, False),
    'year': (STORE_CRANE_MSGS_11100_START_ADDR + 3, 1, False),
    'month': (STORE_CRANE_MSGS_11100_START_ADDR + 4, 1, False),
    'day': (STORE_CRANE_MSGS_11100_START_ADDR + 5, 1, False),
    'hour': (STORE_CRANE_MSGS_11100_START_ADDR + 6, 1, False),
    'min': (STORE_CRANE_MSGS_11100_START_ADDR + 7, 1, False),
    'sec': (STORE_CRANE_MSGS_11100_START_ADDR + 8, 1, False),
    'msec': (STORE_CRANE_MSGS_11100_START_ADDR + 9, 1, False),
    'truck_no': (STORE_CRANE_MSGS_11100_START_ADDR + 10, 1, False),
    'wi_id': (STORE_CRANE_MSGS_11100_START_ADDR + 11, 2, False),
    'via_status': (STORE_CRANE_MSGS_11100_START_ADDR + 13, 1, False),
}

#支持回写plc状态的桥吊,川丰
#起始地址为:12150
STORE_CRANE_LIST_12150 = [39, 40, 45, 46]
#起始地址为:11100
STORE_CRANE_LIST_11100 = [41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 53, 54,55,56]
STORE_CRANE_LIST = STORE_CRANE_LIST_12150 + STORE_CRANE_LIST_11100
PLC_CRANE_LIST = [31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56]
#安川{crane_no:ip}
AC_CRANE_IP_MAP = {
      39:"**************",
      40: "**************",
      41: "*************",
      42: "*************",
      43: "*************",
      44: "*************",
      45: "**************",
      46: "**************",
      47: "*************",
      48: "*************",
      49: "*************",
      50: "*************",
      51: "*************",
      52: "*************",
      53: "*************",
      54: "*************",
      55: "*************",
      56: "*************"
}

AC_CRANE_STORE_PORT = 10060
AC_CRANE_FETCHER_PORT = 10050
CRANE_STORE_MAX_MSG_NO = 65535

def make_crane_store_plc(crane_no, msg_no = 0, truck_no = 0, wi_id = 0, mode = 0, update_env = ''):
    info = CraneStoreInfo()
    info.timestamp_ns = int(time.time() * 1000000000)
    localtime = datetime.datetime.now()
    info.year = localtime.year
    info.month = localtime.month
    info.day = localtime.day
    info.hour = localtime.hour
    info.min = localtime.minute
    info.sec = localtime.second
    info.msec = int(localtime.microsecond / 1000)
    info.crane_no = crane_no
    info.msg_no = msg_no
    info.truck_no = truck_no
    info.wi_id = wi_id
    info.via_status = mode
    info.update_env = update_env
    return info

########龙门吊控制信息
STORE_GANTRY_MSGS_TART_ADDR = 0
STORE_GANTRY_MSGS = {
  'header': (STORE_GANTRY_MSGS_TART_ADDR, 1, False),
  'msg_no': (STORE_GANTRY_MSGS_TART_ADDR + 1, 1, False),
  'gantry_no': (STORE_GANTRY_MSGS_TART_ADDR + 2, 1, False),
  'truck_no': (STORE_GANTRY_MSGS_TART_ADDR + 3, 1, False),
  'ctrl_mode': (STORE_GANTRY_MSGS_TART_ADDR + 4, 1, False),
  'res1': (STORE_GANTRY_MSGS_TART_ADDR + 5, 1, False),
  'res2': (STORE_GANTRY_MSGS_TART_ADDR + 6, 1, False),
  'res3': (STORE_GANTRY_MSGS_TART_ADDR + 7, 1, False),
  'res4': (STORE_GANTRY_MSGS_TART_ADDR + 8, 1, False),
  'end': (STORE_GANTRY_MSGS_TART_ADDR + 9, 1, False),
}

GANTRY_STORE_PORT = 3000
GANTRY_STORE_PORT_2 = 5000
GANTRY_STORE_MAX_MSG_NO = 65535

STORE_GANTRY_IP_MAP = {
    101: ("*************",GANTRY_STORE_PORT_2),
    111: ("*************",GANTRY_STORE_PORT_2),
    113: ("*************",GANTRY_STORE_PORT_2),
    115: ("*************",GANTRY_STORE_PORT_2),
    116: ("*************",GANTRY_STORE_PORT_2),
    119: ("*************",GANTRY_STORE_PORT_2),
    120: ("*************",GANTRY_STORE_PORT_2),
    121: ("*************",GANTRY_STORE_PORT_2),
    122: ("*************",GANTRY_STORE_PORT_2),
    123: ("*************",GANTRY_STORE_PORT_2),
    124: ("*************",GANTRY_STORE_PORT_2),
    126: ("*************",GANTRY_STORE_PORT_2),
    128: ("*************",GANTRY_STORE_PORT_2),
    131: ("*************",GANTRY_STORE_PORT_2),
    133: ("*************",GANTRY_STORE_PORT_2),
    134: ("*************",GANTRY_STORE_PORT_2),
    136: ("*************",GANTRY_STORE_PORT_2),
    137: ("*************",GANTRY_STORE_PORT_2),
    147: ("*************",GANTRY_STORE_PORT_2),
    149: ("*************",GANTRY_STORE_PORT_2),
    150: ("*************",GANTRY_STORE_PORT_2),
    152: ("10.101.152.45",GANTRY_STORE_PORT_2),
    154: ("10.101.154.45",GANTRY_STORE_PORT_2),
    156: ("10.101.156.45",GANTRY_STORE_PORT_2),
    160: ("10.101.160.45",GANTRY_STORE_PORT_2),
    161: ("10.101.161.45",GANTRY_STORE_PORT_2),
    162: ("10.101.162.45",GANTRY_STORE_PORT_2),
    163: ("10.168.163.45",GANTRY_STORE_PORT_2),
    164: ("10.168.164.45",GANTRY_STORE_PORT_2),
    165: ("10.101.165.45",GANTRY_STORE_PORT_2),
    166: ("10.101.166.45",GANTRY_STORE_PORT_2),
    167: ("10.101.167.45",GANTRY_STORE_PORT_2),
    168: ("10.101.168.45",GANTRY_STORE_PORT_2),
    169: ("10.101.169.45",GANTRY_STORE_PORT_2),
    170: ("10.101.170.45",GANTRY_STORE_PORT_2),
    171: ("10.101.171.45",GANTRY_STORE_PORT_2),
    172: ("10.101.172.45",GANTRY_STORE_PORT),
    174: ("10.101.174.45",GANTRY_STORE_PORT),
    175: ("10.101.175.45",GANTRY_STORE_PORT_2),
    176: ("10.101.176.45",GANTRY_STORE_PORT),
    177: ("10.101.177.45",GANTRY_STORE_PORT),
    178: ("10.101.178.45",GANTRY_STORE_PORT_2),
    179: ("10.101.179.45",GANTRY_STORE_PORT),
    180: ("10.101.180.45",GANTRY_STORE_PORT),
    181: ("10.101.181.45",GANTRY_STORE_PORT),
    183: ("10.101.183.45",GANTRY_STORE_PORT_2),
    184: ("10.101.184.45",GANTRY_STORE_PORT_2),
    185: ("10.101.185.45",GANTRY_STORE_PORT_2),
    186: ("10.101.186.45",GANTRY_STORE_PORT),
    187: ("10.101.187.45",GANTRY_STORE_PORT),
    188: ("10.101.188.45",GANTRY_STORE_PORT_2),
    189: ("10.101.189.45",GANTRY_STORE_PORT),
    190: ("10.101.190.45",GANTRY_STORE_PORT_2),
}


def make_gantry_store_plc(gantry_no, msg_no = 0, truck_no = 0, mode = 0):
    info = GantryStoreInfo()
    info.timestamp_ns = int(time.time() * 1000000000)
    info.gantry_no = gantry_no
    info.msg_no = msg_no
    info.truck_no = truck_no
    info.ctrl_mode = mode
    return info


#采用磁钉的桥吊
#MAGNETIC_MARKER_CRANE_LIST_DEFAULT = [48,49,50,51,52,53,54,55,56]
MAGNETIC_MARKER_CRANE_LIST_DEFAULT = []
MAGNETIC_MARKER_CRANE_LIST = MAGNETIC_MARKER_CRANE_LIST_DEFAULT if not SIM_MODE else []
DISABLE_AUTO_CALIB_CRANE_LIST = MAGNETIC_MARKER_CRANE_LIST

def is_crane_mm_enable(crane_info):
    #return (True if crane_info.crane_no in MAGNETIC_MARKER_CRANE_LIST else False)
    return crane_info.mm_enable
