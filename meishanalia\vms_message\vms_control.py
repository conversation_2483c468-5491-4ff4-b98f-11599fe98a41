import sys, time
from dataclasses import dataclass
import datetime
from enum import Enum
import json
from meishanalia.vms_message.msgs import from_json, ControlMsg
from meishanalia.model import table_info
from meishanalia.model import control_info

from common.logger import logger

def vms_control_handler(msg):
    control = from_json(ControlMsg, msg)
    if control.TRUCK_NO.startswith('AT'):
        control_record = table_info.ControlInfo()
        for attr in dir(control_record):
            if not attr.startswith('_') and hasattr(control, attr.upper()):
                setattr(control_record, attr, getattr(control, attr))
        control_record.VERSION = 1
        control_record.STATUS = "DISPATCH"
        if control_record.CUR_WIS is not None and len(control_record.CUR_WIS) > 0:
            control_record.CUR_WI = control_record.CUR_WIS.split(',')[0]
        # 2022.03.09
        if control_record.CUR_IDS is not None and len(control_record.CUR_IDS) > 0:
            control_record.CUR_ID = control_record.CUR_IDS.split(',')[0]
        control_info.insert(control_record)

if __name__ == '__main__':

    if len(sys.argv) < 2:
        print('please set control bits mask:\n')
        print('0xFF:all control\n')
        print('0x01:AT800 LOAD UNLOAD\n')
        print('0x02:AT501 DSCH LOAD\n')
        print('0x04:AT501 MOVE\n')
        print('0x08:AT501 CHARGE\n')
        print('0x10:AT501 LOCK\n')
        print('0x20:AT501 UNLOCK\n')
        print('0x40:AT501 STOP\n')
        print('0x80:AT501 YARD UNLOAD\n')
        print('0x100:AT501 YARD LOAD\n')
        exit()
    else:
        mask = 0
        if sys.argv[1].startswith('0x'):
            mask = int(sys.argv[1], 16)
        else:
            mask = int(sys.argv[1])

    if mask & 0x1:
        control = b'{"TRUCK_NO":"AT501","STOP_NODE":"Node1,Node2,Node3","ACTION":"UNLOAD","CUR_WIS":"538898","WAIT_NODE":"Node1","LOCK_NODE":"Node2","QUEUE_NODE":"Node3"}'


    if mask & 0x8:
        control = b'{"TRUCK_NO":"AT800","STOP_NODE":"BK7110","ACTION":"CHARGE","CUR_WIS":"538899","WAIT_NODE":"","LOCK_NODE":"","QUEUE_NODE":""}'

    print(control)
    vms_control_handler(control)