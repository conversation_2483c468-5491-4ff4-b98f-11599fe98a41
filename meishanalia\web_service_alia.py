

from cache import vehicle_cache
from cache.command_cache import Command<PERSON>ache
from cache.ecs_command_cache import <PERSON>cs<PERSON>ommand<PERSON>ache
from cache.map_cache import Map<PERSON>ache
from cache.services_heartbeat_cache import ServicesHeartbeatCache
from web_service import AntennaWebService as AntennaWebServiceBase
from net_service import Antenna<PERSON>Service

from meishanalia.common.diagnose import insert_tos_command_dict
from meishanalia.tos_db_alia import TosDb
from meishanalia.tos_bridge_alia import TosBridge
from ship.map_ships import MapShips


class AntennaWebService(AntennaWebServiceBase):
    def __init__(self):
        self._db = TosDb()
        self._cache = CommandCache()
        self._ecs_command_cache = EcsCommandCache()
        self._services_heartbeat_cache = ServicesHeartbeatCache()
        AntennaNetService.__init__(self, db=self._db, tos_bridge = TosBridge(),insert_tos_command = insert_tos_command_dict,map_ship = MapShips)
