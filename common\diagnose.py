import datetime
import traceback
from common.db import execute_query, execute_dml
from common.logger import logger
from common.name_converter import NameConverter
from config.config_manage import ConfigManage


def insert_tos_command(container_size, container_position, command_type, command_action, destination, priority,
                       lane=None, truck_no='AT800', wi=125, ctn='CTN', twin_wi=0, twin_ctn='None', orientation='R',
                       lock_flag='N', lock_pavilion='', command_source='TEST', device_disable=0, mode = 0,
                       vessel_pos='None', working_bay='None', cycle_mode = 'S', pow_name = 'None', vessel_ref='None', vessel_class="None"):
    ret = True
    try:
        id = execute_query('select SEQ_WI_INFO_4V_TOS.nextval from DUAL')[0]['NEXTVAL']
        if twin_wi == 0:
            twin_flag = 'N'
        else:
            twin_flag = 'T'
        logger.info(
            f"insert_tos_command:truck_no:{truck_no}, command_type:{command_type}, command_action:{command_action},"
            f"destination:{destination}, container_position:{container_position}, container_size:{container_size},"
            f"lane:{lane}, orientation:{orientation}, priority:{priority}, lock_flag:{lock_flag}, "
            f"lock_pavilion:{lock_pavilion},mode:{mode}")
        if not NameConverter.is_debug_truck_no(truck_no) and command_type != 'GOTO':
            logger.warning(f"vehicle name invalid, make sure that vehicle name starts with AT8 or AT9,for example AT801")
            return False
        if command_type not in {'LOAD', 'DSCH', 'YARD', 'None', 'EXCH', 'RECH', 'GOTO'}:
            logger.warning(f"command type invalid:{command_type}")
            return False
        if command_action not in {'LOAD', 'UNLOAD','STOP_WORK', 'LEAVE_SPACE', 'MOVE', 'CHANGE'}:
            logger.warning(f"command action invalid:{command_action}")
            return False
        if destination.startswith('CR') and vessel_pos == 'None':
            now = datetime.datetime.now()
            now_time = now.strftime('%Y-%m-%d %H:%M:%S')
            vessel_pos = '%02d' % now.hour + '%02d' % now.minute + '%02d' % now.second
        teu = '1' if container_size == '22GP' else '2'
        remark1 = 'FB:' + command_source[:1] + str(hex(device_disable))[-1:]
        yc_kind = 1 if (mode == 1 and (not destination.startswith('CR'))) else 0
        systimestamp = lambda: 'systimestamp'
        columns = [
            "ID", "TRUCK_NO", "WI_NO", "CTN_NO", "FROM_POS", "TO_POS", "WI_TYPE", "WI_ACT", "WI_STATUS", "EQUIT_TYPE",
            "TRUCK_POS",
            "INSERT_TIME", "UPDATE_TIME", "TWIN_FLAG", "TWIN_WI_NO", "TWIN_CTN_NO", "TRUCK_SEQ", "VERSION", "LOCK_FLAG",
            "LOCK_PAVILION", "VESSEL_POS", "TEU", "REMARK1", "YC_KIND"
        ]

        values = [
            id, truck_no, wi, ctn, '1234', destination, command_type, command_action, 'DISPATCH', container_size,
            container_position, systimestamp, systimestamp, twin_flag, twin_wi, twin_ctn, str(priority), 1, lock_flag,
            lock_pavilion,
            vessel_pos, teu, remark1, str(yc_kind)
        ]

        if ConfigManage().is_ms_scene():
            columns.extend(["VESSEL_REF", "VESSEL_CLASS"])
            values.extend([vessel_ref, vessel_class])

        if ConfigManage().is_yz_scene() or ConfigManage().is_yz_cs_scene():
            columns.append("POW_NAME")
            values.append(pow_name)

        columns_part = ", ".join(columns)
        values_part = []
        for value in values:
            if isinstance(value, str):
                values_part.append(f"'{value}'")
            elif isinstance(value, int) or isinstance(value, float):
                values_part.append(str(value))
            elif isinstance(value, type(lambda: None)):
                values_part.append(str(value()))
            else:
                values_part.append(value)

        values_part = ", ".join(values_part)
        sql_str = f"""INSERT INTO T_WI_INFO_4V_TOS ({columns_part}) VALUES ({values_part})"""
        logger.info(f'start_execute:{sql_str}')
        ret = execute_dml(sql_str)
        logger.info(f'end_execute:{sql_str}')
        if not ret:
            logger.warning(f"fail to insert tos:{sql_str}")
            return False

        if not lane is None and destination.startswith('CR'):
            if working_bay != 'None':
                working_bay = working_bay.split(',')
                bay1 = working_bay[0]
                bay2 = working_bay[1]
                bay3 = working_bay[2]
            else:
                bay1 = bay2 = bay3 = working_bay
            crane_id = destination[:4]
            sql_str = """INSERT INTO T_CRANE_INFO_4V_DEBUG
                (CRANE_ID, LANE_NO, VESSEL_DIRECTION, CYCLE_MODE, INSERT_TIME, UPDATE_TIME, BAY1, BAY2, BAY3)
                VALUES('{}', '{}', '{}', '{}', systimestamp, systimestamp, '{}', '{}', '{}')
                """.format(crane_id, lane, orientation, cycle_mode, bay1, bay2, bay3)
            logger.info(f'{sql_str}')
            ret = execute_dml(sql_str)
            if not ret:
                logger.warning(f"fail to insert crane:{sql_str}")
                return False
    except Exception as e:
        logger.warning(f"Insert TOS command err:{e}, trace:{traceback.format_exc()}")
        ret = False
    return ret


from common.diagnose_base import create_insert_tos_command_handler
insert_tos_command_dict = create_insert_tos_command_handler(insert_tos_command)