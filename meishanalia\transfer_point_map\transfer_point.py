import ast
import math
import time
import os
import traceback
from threading import Thread

from cache.transfer_position_cache import TransferPositionCache
from common.logger import logger
from common.singleton import Singleton
from plc.crane_receiver import CraneInfoContainer
from port_box_map.box_region_interface import get_service_point_loc

SCRIPT_DIR = os.path.dirname(__file__)
TRANSFER_POINT_DATA_PATH = os.path.join(SCRIPT_DIR, "transfer_point.txt")


def load_transfer_point():
    file = open(TRANSFER_POINT_DATA_PATH, "r")
    lines = file.readlines()
    for line in lines:
        data = line.split(";")
        TransferPositionCache().set_tp(data[0], data[1])

load_transfer_point()


class TransferPoint(metaclass=Singleton):
    def get_transfer_point_loc(self, transfer_point: str):
        start = time.time()
        utm_x = None
        utm_y = None
        try:
            if transfer_point.startswith('CR'):
                utm_x, utm_y = CraneInfoContainer().get_position_by_crane_no(int(transfer_point[2:]))
            elif transfer_point.startswith('BK'):  # BK066040 -> 6640
                utm_x, utm_y = get_service_point_loc(transfer_point[3:].replace(transfer_point[3:][2],"",1))
            elif len(transfer_point) == 4:  # 2022.03.04 modified  and transfer_point.isdigit()
                utm_x, utm_y = get_service_point_loc(transfer_point)
            elif len(transfer_point) == 2:  # and transfer_point.isdigit()
                utm_x, utm_y = get_service_point_loc(transfer_point + "06")
            elif transfer_point == "FLD":pass
            else:
                utmxy = ast.literal_eval(TransferPositionCache().get_tp(transfer_point))
                utm_x = utmxy[0]
                utm_y = utmxy[1]
        except Exception as e:
            logger.warning(
                f"fail to get transfer point utm:{transfer_point} point,error:{e}, trace:{traceback.format_exc()}")
        end = time.time()
        if end - start > 1:
            logger.warning(f"get_transfer_point_loc time out , tp: {transfer_point}")
        return (utm_x, utm_y)

    def get_transfer_point_poi_id(self, transfer_point: str):
        start = time.time()
        poi_id = None
        try:
            if transfer_point.startswith('YQ') and (transfer_point.endswith('1') or transfer_point.endswith('4')):  # 锁亭点
                if transfer_point[4] == "U":
                    if transfer_point.endswith('1'):
                        poi_id = str(int(transfer_point[2:4])) + "Y1"
                    elif transfer_point.endswith('4'):
                        poi_id = str(int(transfer_point[2:4])) + "Y2"
                elif transfer_point[4] == "D":
                    if transfer_point.endswith('1'):
                        poi_id = str(int(transfer_point[2:4])) + "Y4"
                    elif transfer_point.endswith('4'):
                        poi_id = str(int(transfer_point[2:4])) + "Y3"
            elif transfer_point.startswith('YQ') and transfer_point.endswith('9'):  # 排队点
                poi_id = "queue_" + str(int(transfer_point[2:4]))
            elif transfer_point.startswith('YQ') and (transfer_point.endswith('2') or transfer_point.endswith('3') or
                                                      transfer_point.endswith('5')):  # 引桥休息点
                # if transfer_point == "YQ06D002":  # 由于6引桥有安全门，休息点不可用，引桥休息点尚未确定，临时替换为7引桥休息点
                #     poi_id = "reset_7"
                # else:
                #     poi_id = "reset_" + str(int(transfer_point[2:4]))
                poi_id = "reset_v_8_1"  # 引桥休息点尚未确定，临时替换为箱区休息点
            elif transfer_point.startswith('VR'):  # 箱区休息点 纵路 VR081013:靠近横二路   VR080013:靠近横一路
                poi_id = "reset_v_" + str(int(transfer_point[2:4])) + "_" + str(int(transfer_point[4])+1)
            elif transfer_point.startswith('HR'):  # 箱区休息点 横路 HR010096
                poi_id = "reset_h_1_1"
            else:
                pass
        except Exception as e:
            logger.warning(
                f"fail to get transfer poi_id:{transfer_point} point,error:{e}, trace:{traceback.format_exc()}")
        end = time.time()
        if end - start > 1:
            logger.warning(f"get_transfer_point_poi_id time out , tp: {transfer_point}")
        return poi_id


if __name__ == '__main__':
    print(TransferPoint().get_transfer_point_loc('YQ08U004'))
    print(TransferPoint().get_transfer_point_poi_id('YQ08U004'))
