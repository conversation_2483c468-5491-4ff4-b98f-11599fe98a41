#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

#$DOCKER_PATH/db_start.sh
source $DOCKER_PATH/common.sh

echo "DOCKER_NAME: $DOCKER_NAME"

function local_volumes() {
  volumes="
           -v $HOME/.ssh:${DOCKER_HOME}/.ssh \
           -v $HOME/.zsh_history:${DOCKER_HOME}/.zsh_history \
           -v /tmp/core:/tmp/core \
           -v /proc/sys/kernel/core_pattern:/tmp/core_pattern:rw \
           -v /media:/media \
           -v /private:/private \
           -v /onboard_data:/onboard_data \
           -v /etc/localtime:/etc/localtime:ro \
           -v /tmp/.ssh-agent-$USER:/tmp/.ssh-agent-$USER \
	   -v /tmp/check_master.info:/tmp/check_master.info"

  volumes="${volumes} -v ${LOCAL_DIR}:/antenna-server"

  echo "${volumes}"
}

if [[  -z $(docker images -q ${IMG}) ]]; then
  echo "----------pull image: ${IMG}"
  docker pull $IMG
fi
bash ./script/download_model.sh

docker ps -a --format "{{.Names}}" | grep "${DOCKER_NAME}" 1>/dev/null
if [ $? == 0 ]; then
docker stop ${DOCKER_NAME} 1>/dev/null
docker rm -f ${DOCKER_NAME} 1>/dev/null
fi

ORACLE_CONFIG=""
if [ $START_ORACLE_DB == "true" ] ; then
  $DOCKER_PATH/db_start.sh
  ORACLE_CONFIG="
    		 -e ANTENNA_DB_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DB_DOCKER_NAME}) \
    		 -e ANTENNA_DB_SID=XE
    		"
fi

mkdir -p /tmp/.ssh-agent-$USER 2>&1 >/dev/null

USER_ID=$(id -u)
GRP=$(id -g -n)
GRP_ID=$(id -g)
LOCAL_HOST=$(hostname)
DOCKER_HOME="/home/<USER>"
if [ "$USER" == "root" ]; then
  DOCKER_HOME="/root"
fi

DOCKER_CMD="nvidia-docker"
if ! [ -x "$(command -v ${DOCKER_CMD})" ]; then
  DOCKER_CMD="docker"
fi

PORTS_CONFIG="
              -p ${DISPATCH_ADDRESS_PORT}:6789
              -p ${FAST_DISPATCH_ADDRESS_PORT}:6788
              -p ${DEBUG_FAST_DISPATCH_ADDRESS_PORT}:6000
              -p ${PLC_CRANE_PORT}:2000
              -p ${PLC_GANTRY_PORT}:1234
              -p ${CHANGE_PORT}:2090
              -p ${WEB_PORT}:6790
             "
HOST_NET_MODE=""
if [ "$HOST_NET_MODE_ENABLE" == "true" ]; then
  PORTS_CONFIG=""
  HOST_NET_MODE=" --net=host"
fi


GPU_CONFIG=""
if [ "$DOCKER_CMD" == "docker" ]; then
  new_docker=$(echo "$(docker -v | cut -c 16-20) >= 19.03" | bc)
  if [ "$new_docker" == "1" ]; then
    GPU_CONFIG="--gpus all"
  fi
fi

#delete ANTENNA_DB_IP and ANTENNA_DB_SID
eval ${DOCKER_CMD} create -it \
    --name ${DOCKER_NAME} \
    -e DOCKER_USER=$USER \
    -e USER=$USER \
    -e DOCKER_USER_ID=$USER_ID \
    -e DOCKER_GRP=$GRP \
    -e DOCKER_GRP_ID=$GRP_ID \
    -e DOCKER_HOME=$DOCKER_HOME \
    -e DOCKER_NAME=$DOCKER_NAME \
    -e DOCKER_HOST_PATH=$(pwd) \
    -e PYTHONPATH=. \
    -e DEV_ID=$(date | md5sum | cut -f 1 -d ' ') \
    -e SSH_AUTH_SOCK=/tmp/.ssh-agent-$USER/agent.sock \
    -e ENVIRONMENT=$ENVIRONMENT \
    -e LOCAL_REDIS_PORT=$LOCAL_REDIS_PORT \
    $(local_volumes) \
    --ulimit core=-1 \
    -w /antenna-server \
    $ORACLE_CONFIG \
    $PORTS_CONFIG \
    $HOST_NET_MODE \
    --add-host in_antenna_dev_docker:127.0.0.1 \
    --add-host ${LOCAL_HOST}:127.0.0.1 \
    --add-host kafka1:************* \
    --add-host kafka2:************* \
    --add-host kafka3:************* \
    --add-host slave1:************** \
    --add-host slave2:************** \
    --add-host slave3:************** \
    --add-host broker1:************* \
    --add-host broker2:************* \
    --add-host broker3:************* \
    --hostname in_antenna_dev_docker \
    --restart always \
    $GPU_CONFIG \
    -v ${DOCKER_PATH}/entrypoint.sh:/tmp/entrypoint.sh \
    --entrypoint /tmp/entrypoint.sh \
    --cap-add=SYS_PTRACE \
    $IMG /bin/bash

docker cp -L ~/.gitconfig ${DOCKER_NAME}:${DOCKER_HOME}/.gitconfig
docker start ${DOCKER_NAME}
docker exec ${DOCKER_NAME} chown ${USER_ID}:${GRP_ID} /antenna-server
