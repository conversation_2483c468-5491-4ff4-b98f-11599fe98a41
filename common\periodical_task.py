import time, traceback
from threading import Thread
from common.master import Master
from common.logger import logger
from common import trace_helper


class PeriodicalTask:
    def __init__(self, target, interval, master_only=False, **kwargs):
        self.__target = target
        self.__kwargs = kwargs
        self.__thread = Thread(target=self.loop)
        self.__master_only = master_only
        self.__interval = interval
        self.__running = False

    def start(self):
        self.__running = True
        self.__thread.start()

    def loop(self):
        while self.__running:
            start = time.time()
            try:
                if self.__master_only and not Master().is_master():
                    time.sleep(self.__interval)
                    continue
                context: trace_helper.SpanContext = trace_helper.get_context()
                op = trace_helper.format_callable_name(self.__target)
                with context.new_entry_span(op=op) as span:
                    span.component = trace_helper.Component.Unknown
                    self.__target(**self.__kwargs)
            except Exception as e:
                logger.warning(f"periodical {self.__target} err {e}, trace:{traceback.format_exc()}")

            interval_time = time.time() - start
            #logger.debug(f"periodical {self.__target} loop cost:{end - start}")
            sleep_time = (self.__interval - interval_time) if self.__interval >= interval_time and interval_time > 0 else self.__interval
            if sleep_time > 0:
                time.sleep(sleep_time)
                
    def stop(self):
        self.__running = False
        self.__thread.join()

if __name__ == '__main__':
    class C:
        def print(self):
            print('class method')
            raise Exception('?')
    c = C()
    def p(args):
        print('static method %d' % args)
    PeriodicalTask(target = c.print, interval = 0.1).start()
    PeriodicalTask(target = lambda:p(10), interval = 0.1).start()
