import os
import cv2
import math
import time
import shutil
import datetime
import numpy as np
from collections import defaultdict
from typing import Optional, <PERSON><PERSON>, List

from cache import crane_calib, plc_cache
from common.constant import CRANE_LANE_THETA
from common.logger import logger
from common.singleton import Singleton
from model.coastmark_detector import Coast<PERSON>arkDetector
from model.crane_visual_locator import CraneVisualLocator
from model.abrasion_table import AbrasionTable
from plc.model import MAGNETIC_MARKER_CRANE_LIST,is_crane_mm_enable
from proto.cache_pb2 import CraneInfo
from config.config_manage import ConfigManage


class CraneCalibrator(metaclass=Singleton):
    def __init__(self):
        self._g_pos_dict = dict()
        self._g_pos_dict[1] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[2] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[3] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[4] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[5] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[6] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[7] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[8] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[9] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[10] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[11] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[12] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[13] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[14] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[15] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[16] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[17] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[18] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[19] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[20] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[21] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[22] = (
            -0.0008736100682355797,
            3318331.048514639,
            0.0004853528518607662,
            387902.1120394841)
        self._g_pos_dict[31] = (
            0.00690037,
            3294525.6360784876,
            0.00745871,
            404680.12028395396)
        self._g_pos_dict[32] = (
            0.00689628,
            3294528.529288389,
            0.00747886,
            404683.10111369967)
        self._g_pos_dict[33] = (
            0.006337413629931447, 3294576.721176432, 0.006861146588419622, 404735.39441487944)
        self._g_pos_dict[34] = (
            0.006337413629931447, 3294576.721176432, 0.006861146588419622, 404735.39441487944)
        self._g_pos_dict[35] = (
            0.006877194300932523,
            3294679.9814967853,
            0.00746602989390269,
            404847.1044100784)
        self._g_pos_dict[36] = (
            0.006876425820278276,
            3294684.5427581635,
            0.007425580870663761,
            404852.15728686674)
        self._g_pos_dict[37] = (
            0.006901469580824279,
            3294798.2994854343,
            0.0074638917254667295,
            404975.01640052104)
        self._g_pos_dict[38] = (
            0.006909918303897905,
            3294801.8910720074,
            0.007471796051660129,
            404978.7808574292)
        self._g_pos_dict[39] = (
            0.007123010849884136, 3294836.5626027933, 0.007711666689908575, 405016.1632326581)
        self._g_pos_dict[40] = (
            0.007172919283823643, 3294838.524663893, 0.0077656996288713006, 405018.2835063614)
        self._g_pos_dict[41] = (
            0.007235513426708262, 3294886.2331088562, 0.00783346664182076, 405070.0404085226)
        self._g_pos_dict[42] = (
            0.007174072575164215, 3294894.4203604255, 0.0077669482296740195, 405079.36237361876)
        self._g_pos_dict[43] = (
            0.007296007157469476, 3294997.032566188, 0.007898959660950151, 405189.96454114764)
        self._g_pos_dict[44] = (
            0.007076717335255518,
            3294991.060099864,
            0.007664682076058962,
            405183.4681833753)
        self._g_pos_dict[45] = (
            0.007230168369085335, 3295199.5785567486, 0.00782767986062119, 405410.0053891212)
        self._g_pos_dict[46] = (
            0.007130912242666609,
            3295203.710808041, # 3295201.2853550413 + 2.425453
            0.007714571946808792,
            405413.52723375015) # 405410.94322275015 + 2.584011
        '''
        #2780-3020
        self._g_pos_dict[47] = (
            0.007281259848194045,
            3295297.2505007808,
            0.007874315598347545,
            405514.66298756724)
        '''
        #3270-3415
        self._g_pos_dict[47] = (
            0.007031021587002111,
            3295298.075065018,
            0.007608680447717638,
            405515.1338162987)
        self._g_pos_dict[48] = (
            0.00713094877010906,
            3295300.537261929,
            0.007674813405167749,
            405518.4709272748)
        self._g_pos_dict[49] = (
            0.0070843779584655285,
            3295412.8545950863,
            0.007694349985163765,
            405639.09160465014)
        self._g_pos_dict[50] = (
            0.0070550348796886994,
            3295411.213707564,
            0.007654126614670176,
            405637.39181412815)
        self._g_pos_dict[51] = (
            0.007256080618224353,
            3295511.7249459485,
            0.007859533264462407,
            405746.7909080249)
        self._g_pos_dict[52] = (
            0.007197304937822655,
            3295515.138137653,
            0.007792206829369486,
            405750.51446262025)
        self._g_pos_dict[53] = (
            0.007064822968398264,
            3295615.455965038,
            0.00765529923471392,
            405859.4015568148)
        self._g_pos_dict[54] = (
            0.007111392537021648,
            3295620.834538914,
            0.007703184513866859,
            405864.79608752026)
        self._g_pos_dict[55] = (
            0.007059578680000317,
            3295720.905720214,
            0.007660230872437412,
            405973.1465328964)
        self._g_pos_dict[56] = (
            0.007083189013830862,
            3295719.6626905617,
            0.007666239889544038,
            405971.9086447142)

        #梅山磁钉标定参数
        self._g_pos_dict['ms_mm'] = (
            0.006781891859081937,
            3293187.2769236206,
            0.007348646805489836,
            403227.82481260254)
        '''
        for crane in MAGNETIC_MARKER_CRANE_LIST:
            self._g_pos_dict[crane] = self._g_pos_dict['ms_mm']
        '''
        self._g_pos_dict[801] = (
            -0.0032446356185466255,
            3550133.042398044,
            0.009470964461878574,
            371088.50177048537)
        self._g_pos_dict[802] = (
            -0.0031696356185466255,
            3550130.557716131,
            0.009470964461878574,
            371087.5692026079)
        self._g_pos_dict[803] = (
            -0.0031696356185466255,
            3550130.557716131,
            0.009470964461878574,
            371087.5692026079)
        self._g_pos_dict[804] = (
            -0.0031696356185466255,
            3550130.557716131,
            0.009470964461878574,
            371087.5692026079)

        #fake
        self._g_pos_dict[71] = self._g_pos_dict[46] #fake same with CR46
        self._g_pos_dict[72] = self._g_pos_dict[46] #fake same with CR46
        self._g_pos_dict[73] = self._g_pos_dict[46] #fake same with CR46
        self._g_pos_dict[74] = self._g_pos_dict[46] #fake same with CR46
        self._g_pos_dict[75] = self._g_pos_dict[46] #fake same with CR46

        self._last_calibration_gpos = defaultdict(int)
        self._last_gpos = defaultdict(int)
        self._calibration_ts = defaultdict(int)
        self._coastline_ts = {}
        self._count_large_offset = defaultdict(int)
        self._offset_min_updated_thresh = 2 # in meter
        self._offset_max_updated_thresh = 30 # in meter
        self._warn_interval = 86400  # 24 hour
        self._coastline_noticed_dur = 86400  # 24 hour
        self._collect_dir = 'crane_images/collect'
        self._outdated_thresh = 30 # in day

    def set_crane_camera_pair_frames(self,
                                     crane_no,
                                     img_pair):
        self._calibration_ts[crane_no] = time.time()
        if img_pair is None or len(img_pair) != 2:
            logger.debug('get illegal image pair!')
            return

        mark_info_left = self.get_mark_by_frame(crane_no, img_pair[0], 'left')
        mark_info_right = self.get_mark_by_frame(
            crane_no, img_pair[1], 'right')
        self.update_crane_offset_by_vision(
            crane_no, mark_info_left, mark_info_right, img_pair)
        self.update_abrasion_table(crane_no, mark_info_left, mark_info_right, img_pair)

        self._record_calibration_gpos(crane_no)

    def crane_need_calibration(self, crane_no):
        crane_info = plc_cache.get_crane(crane_no)
        crane_has_moved = self._crane_has_moved(crane_no, crane_info)
        # crane need immediate calibration if it has moved and either of:
        # - gpos has jump
        # - offset is abnomal
        # - last calibration failed
        need_calibration = False
        if crane_has_moved:
            # if duration is not None and duration > 14400:
            #     logger.debug(
            #         f'crane:{crane_no} has moved and under uncalibration for {datetime.timedelta(seconds=duration)}s')
            #     return True

            if self._gpos_has_jump_after_last_calibration(crane_no):
                logger.debug(f'crane:{crane_no} has moved and gpos has jump  after last calibration')
                need_calibration = True

            if self._offset_is_abnormal(crane_no):
                logger.debug(f'crane:{crane_no} has moved and offset is abnormal')
                need_calibration = True

            if not self._last_calibration_success(crane_no):
                duration = self._get_uncalibration_duration(crane_no, True)
                if duration is not None:
                    logger.debug(
                        f'crane:{crane_no} has moved and under uncalibration for {datetime.timedelta(seconds=duration)}s')
                need_calibration = True

        if crane_info is not None:
            self._last_gpos[crane_no] = crane_info.g_pos
        return need_calibration

    #启用磁钉数据不用offset
    def get_position_v2(self, crane_info: CraneInfo, offset=None) -> Optional[Tuple]:
        if crane_info is not None:
            crane_no = crane_info.crane_no
            if crane_no in self._g_pos_dict:
                if not is_crane_mm_enable(crane_info):
                    g_pos_dict = self._g_pos_dict[crane_no]
                    g_pos = crane_info.g_pos
                    if offset is None:
                        offset = crane_calib.get_crane_calib_offset(crane_no)
                else:
                    g_pos_dict = self._g_pos_dict['ms_mm']
                    g_pos = crane_info.g_pos_b
                    offset = 0
                offset_x = offset * math.cos(CRANE_LANE_THETA)
                offset_y = offset * math.sin(CRANE_LANE_THETA)
                x = g_pos_dict[2] * g_pos + \
                    g_pos_dict[3] - offset_x
                y = g_pos_dict[0] * g_pos + \
                    g_pos_dict[1] - offset_y
                return (x, y)
            else:
                logger.debug(f"crane:{crane_no} not in g_pos_dict")
        return None

    def get_position(self, crane_info: CraneInfo, offset=None) -> Optional[Tuple]:
        if crane_info is not None:
            crane_no = crane_info.crane_no
            if crane_no in self._g_pos_dict:
                g_pos = crane_info.g_pos
                if offset is None:
                    offset = crane_calib.get_crane_calib_offset(crane_no)

                offset_x = offset * math.cos(CRANE_LANE_THETA)
                offset_y = offset * math.sin(CRANE_LANE_THETA)
                x = self._g_pos_dict[crane_no][2] * g_pos + \
                    self._g_pos_dict[crane_no][3] - offset_x
                y = self._g_pos_dict[crane_no][0] * g_pos + \
                    self._g_pos_dict[crane_no][1] - offset_y
                return (x, y)
            else:
                logger.debug(f"crane:{crane_no} not in g_pos_dict")
        return None

    def get_gpos(self,crane_no, x, y):
        if crane_no not in self._g_pos_dict:
            logger.warning(f"crane:{crane_no} has no calib param")
            return None
        offset = crane_calib.get_crane_calib_offset(crane_no)
        offset_x = offset * math.cos(CRANE_LANE_THETA)
        offset_y = offset * math.sin(CRANE_LANE_THETA)
        g_pos_x = int((x + offset_x - self._g_pos_dict[crane_no][3]) // self._g_pos_dict[crane_no][2])
        g_pos_y = int((y + offset_y - self._g_pos_dict[crane_no][1]) // self._g_pos_dict[crane_no][0])
        g_pos = int((g_pos_x + g_pos_y) / 2)
        return (g_pos_x, g_pos_y, g_pos)

    def get_offset_by_loc(self, crane_no, crane_detect_center):
        if crane_no in self._g_pos_dict:
            crane_info = plc_cache.get_crane(crane_no)
            if crane_info is None:
                logger.debug(f'crane:{crane_no} crane_info is none')
                return

            center_without_offset = np.array(self.get_position(crane_info, 0))
            crane_detect_center = np.array(crane_detect_center)
            lane_direction = np.array(
                [np.cos(CRANE_LANE_THETA),
                 np.sin(CRANE_LANE_THETA)])

            offset = (center_without_offset -
                      crane_detect_center).dot(lane_direction)
            return offset

    def get_offet_by_mark_info(self, crane_no, mark_info, is_left):
        if mark_info is not None:
            utm = CraneVisualLocator().get_position_by_mark(
                crane_no, *mark_info, is_left)
            new_offset = self.get_offset_by_loc(crane_no, utm)
            side = 'left' if is_left else 'right'
            logger.debug(
                    f'crane:{crane_no} [{side}] mark={mark_info[0]}, utm=({utm[0]}, {utm[1]}), offset={new_offset}, bbox bias={mark_info[1]:.2f}')

            return new_offset

    def update_crane_offset_by_vision(self, crane_no, mark_info_left, mark_info_right, frames):
        offset_left = self.get_offet_by_mark_info(
            crane_no, mark_info_left, True)
        offset_right = self.get_offet_by_mark_info(
            crane_no, mark_info_right, False)

        update_success = False
        if offset_left is not None and offset_right is not None:
            offset_diff = abs(offset_left - offset_right)
            if offset_diff < 3:
                new_offset = (offset_left + offset_right)/2
                update_success = self.update_crane_offset(crane_no, new_offset)
                if not update_success:
                    self.collect_frame(frames[0],'large_offset',f'{crane_no}', f'left_{mark_info_left[0]}')
                    self.collect_frame(frames[1],'large_offset',f'{crane_no}', f'right_{mark_info_right[0]}')

                    if self._crane_moved_since_last_set(crane_no):
                        self._count_large_offset[crane_no] += 1
                else:
                    self.collect_frame(frames[0],'success_update',f'{crane_no}', f'left_{mark_info_left[0]}')
                    self.collect_frame(frames[1],'success_update',f'{crane_no}', f'right_{mark_info_right[0]}')
                    self._count_large_offset[crane_no] = 0

            else:
                logger.debug(
                    f'crane:{crane_no} too large diff between left and right offset: {offset_left} vs {offset_right}, no update')


        if not update_success and self._gpos_has_jump_after_last_calibration(crane_no):
            logger.warning(
                f'crane:{crane_no} calibrated failed since last gpos jump!')

        # check if crane could be verified by either one camera
        if update_success or self._offset_diff_is_small(crane_no, offset_left) or self._offset_diff_is_small(crane_no, offset_right):
            crane_calib.set_crane_verified_timestamp(crane_no, time.time())
        else:
            duration = self._get_uncalibration_duration(crane_no)
            if duration is not None and duration > self._warn_interval:
                logger.warning(
                    f'crane:{crane_no} can not calibrated for {datetime.timedelta(seconds=duration)}')


    def update_crane_offset_by_loc(self, crane_no, crane_detect_center):
        offset = self.get_offset_by_loc(crane_no, crane_detect_center)
        if offset is not None:
            self.update_crane_offset(crane_no, offset)

    def update_crane_offset(self, crane_no, offset):
        success = True
        old_offset = crane_calib.get_crane_calib_offset(crane_no)
        offset_diff = offset - old_offset
        if abs(offset_diff) < self._offset_min_updated_thresh:
            # still set previous offset to update timestamp
            crane_calib.set_crane_calib_offset(crane_no, old_offset)
            logger.debug(
                f'crane:{crane_no}, offset: {old_offset}->{offset}, skip update!')
        elif self._offset_has_similar_gpos_jump(crane_no, offset_diff):
            crane_calib.set_crane_calib_offset(crane_no, offset)
            logger.debug(
                f'crane:{crane_no}, offset: {old_offset}->{offset}, update because of gpos jump')
        elif not self._offset_is_abnormal(crane_no, offset):
            crane_calib.set_crane_calib_offset(crane_no, offset)
            logger.debug(
                f'crane:{crane_no}, offset: {old_offset}->{offset}, directly update small offset')
        elif self._crane_is_in_order(crane_no, offset) and not self._crane_is_in_order(crane_no, old_offset):
            crane_calib.set_crane_calib_offset(crane_no, offset)
            logger.debug(
                f'crane:{crane_no}, offset: {old_offset}->{offset}, update for order crane')
        else:
            success = False
            logger.warning(
                f'crane:{crane_no}, offset : {old_offset}->{offset}, abnormal offset and no update')

        return success

    def get_mark_by_frame(self, crane_no, frame, side):
        roi_img = CoastMarkDetector().crop_roi_image(
            crane_no, frame, side)
        is_left = side == 'left'
        key = f'{crane_no}_{side}'
        if CoastMarkDetector().check_is_coastline(roi_img):
            self._coastline_ts[key] = time.time()
            mark_result = CoastMarkDetector().detect(roi_img, is_left)
            if mark_result is not None:
                bbox, mark = mark_result
                bbox_bias = CraneVisualLocator().estimate_bias(bbox, is_left)
                return mark, bbox_bias
            else:
                logger.debug(f"crane:{crane_no} [{side}] mark detect failed!")
        else:
            if key not in self._coastline_ts:
                self._coastline_ts[key] = time.time()
            logger.debug(
                f"crane:{crane_no} [{side}] roi is not coastline!")
            self.collect_frame(roi_img,'coastline_roi',f'{crane_no}',side)


    def update_abrasion_table(self, crane_no, mark_info_left, mark_info_right, frames):
        utm = self.get_position(plc_cache.get_crane(crane_no))
        if utm is None:
            return
        if mark_info_left is not None:
            mark_from_camera, bbox_bias = mark_info_left
        else:
            mark_from_camera = 0
            bbox_bias = 2.5
        mark_from_utm = CraneVisualLocator().get_mark_by_position(
            crane_no, utm, bbox_bias, True)
        matched = AbrasionTable().update_mark(mark_from_utm, mark_from_camera)
        if mark_info_left is None:
            self.collect_frame(frames[0],'detect_failed',f'{crane_no}',f'left_{mark_from_utm:.0f}')
        elif not (matched or self._gpos_has_jump_after_last_calibration(crane_no)):
            self.collect_frame(frames[0],'unmatched_marks',f'{crane_no}',f'left_{mark_from_camera}_{mark_from_utm:.0f}')

        if mark_info_right is not None:
            mark_from_camera, bbox_bias = mark_info_right
        else:
            mark_from_camera = 0
            bbox_bias = 2.5

        mark_from_utm = CraneVisualLocator().get_mark_by_position(
            crane_no, utm, bbox_bias, False)
        matched = AbrasionTable().update_mark(mark_from_utm, mark_from_camera)
        if mark_info_right is None:
            self.collect_frame(frames[1],'detect_failed',f'{crane_no}',f'right_{mark_from_utm:.0f}')
        elif not (matched or self._gpos_has_jump_after_last_calibration(crane_no)):
            self.collect_frame(frames[1],'unmatched_marks',f'{crane_no}',f'right_{mark_from_camera}_{mark_from_utm:.0f}')

    def collect_frame(self, img, collect_type, prefix='', suffix=''):
        if not crane_calib.is_enable_collect_crane_calib_data():
            return

        date_time = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
        out_dir = os.path.join(self._collect_dir, collect_type, date_time[:8])
        if not os.path.isdir(out_dir):
            os.makedirs(out_dir)

        if prefix:
            prefix += '_'
        if suffix:
            suffix = '_' + suffix

        imfile = os.path.join(
                out_dir, f'{prefix}{date_time}{suffix}.jpg')
        cv2.imwrite(imfile, img)

    def clean_outdated_frames(self):
        today = datetime.datetime.today()
        # clean outdated frames at 00:00-01:00 on the first day of each month
        if os.path.isdir(self._collect_dir):
            for ctype in os.listdir(self._collect_dir):
                ctype_dir = os.path.join(self._collect_dir, ctype)
                days = [d for d in os.listdir(ctype_dir) if d.isdigit() and len(d) == 8]

                for day in days:
                    # check if dir is outdated
                    dt_diff = today - datetime.datetime.strptime(day, '%Y%m%d')
                    if dt_diff.days > self._outdated_thresh:
                        outdated_dir = os.path.join(ctype_dir, day)
                        frames = [f for f in os.listdir(outdated_dir) if f.endswith('.jpg')]
                        shutil.rmtree(outdated_dir)
                        logger.debug(f'remove outdated dir {outdated_dir}, total {len(frames)} images')


    def notice_uncalibration(self, wetchat_handler, crane_nos, atall_nos):
        '''
        awarn to wechat if :
        1. some cranes are under uncalibration for too long
        2. auto calibration found continuous large offset
        '''
        is_pro = False
        if ConfigManage().is_fat_env():
            env = '【测试环境】'
        elif ConfigManage().is_pre_env():
            env = '【预发环境】'
        elif ConfigManage().is_pro_env():
            env = '【生产环境】'
            is_pro = True
        else:
            env = '【未知环境】'
        crane_nos = sorted(crane_nos)
        crane_info = f'【CR{crane_nos[0]}-CR{crane_nos[-1]}】'
        prefix = env + crane_info + time.strftime("%Y-%m-%d %H:%M:%S")
        notice_infos = [prefix]
        abnormal_nos = set()

        # check uncalibration
        crane_template =  "{}桥超过{:.0f}小时未能校检成功，请人工确认！"
        noticed_dur = crane_calib.get_noticed_interval_second()
        for no in crane_nos:
            # get lastest update time
            calib_ts = crane_calib.get_crane_calib_timestamp(no)
            verif_ts = crane_calib.get_crane_verified_timestamp(no)
            if calib_ts is None and verif_ts is None:
                continue
            elif calib_ts is None:
                dur = time.time() - verif_ts
            elif verif_ts is None:
                dur = time.time() - calib_ts
            else:
                dur = time.time() - max(verif_ts, calib_ts)

            if dur > noticed_dur:
                abnormal_nos.add(no)
                notice_infos.append(crane_template.format(no,dur/3600))

        # check coastline
        coastline_infos = ['--------------']
        for no in crane_nos:
            side_info = []
            for side in [('left', '左'), ('right', '右')]:
                key = f'{no}_{side[0]}'
                if key in self._coastline_ts:
                    dur = time.time() - self._coastline_ts[key]
                else:
                    dur = 0
                if dur > self._coastline_noticed_dur:
                    side_info.append((side[1], dur))

            side_str = '/'.join([s[0] for s in side_info])
            dur_str = '/'.join([f'{s[1]/3600:.0f}' for s in side_info])
            if len(side_info) > 0:
                abnormal_nos.add(no)
                coastline_infos.append("{}桥[{}]相机超过[{}]小时未找到海岸线，请确认相机视角！".format(
                    no, side_str, dur_str))
        if len(coastline_infos) > 1:
            notice_infos += coastline_infos

        # check large offset
        abnormal_infos = ['--------------']
        for no in crane_nos:
            if self._count_large_offset[no] >= 3:
                abnormal_nos.add(no)
                abnormal_infos.append(f'{no}桥自动标定offset过大（>{self._offset_max_updated_thresh}米），自动标定未生效，请人工确认！')
        if len(abnormal_infos) > 1:
            notice_infos += abnormal_infos

        if is_pro and not abnormal_nos.isdisjoint(atall_nos):
            mlist = ["@all"]
        else:
            mlist = []
        wetchat_handler.send_message('\n'.join(notice_infos), mentioned_list=mlist)
        logger.debug('\n'.join(notice_infos))

    def _last_calibration_success(self, crane_no):
        last_update_time = crane_calib.get_crane_calib_timestamp(crane_no)
        return last_update_time and crane_no in self._calibration_ts and self._calibration_ts[crane_no] <= last_update_time

    def _get_uncalibration_duration(self, crane_no, strict_mode=False):
        if strict_mode:
            last_update_time = crane_calib.get_crane_calib_timestamp(
                crane_no)
        else:
            last_update_time = crane_calib.get_crane_verified_timestamp(
                crane_no)
        if last_update_time is None:
            return
        return time.time() - last_update_time

    def _crane_moved_since_last_set(self, crane_no, move_thresh=500):
        if crane_no in self._last_calibration_gpos:
            crane_info = plc_cache.get_crane(crane_no)
            if crane_info is not None:
                gpos_diff = abs(crane_info.g_pos - self._last_calibration_gpos[crane_no])
                if gpos_diff >= move_thresh:
                    return True

    def _crane_has_moved(self, crane_no, crane_info):
        move_thresh = 200 # cm
        if crane_no in self._last_gpos and crane_info is not None:
            gpos_diff = abs(crane_info.g_pos - self._last_gpos[crane_no])
            if gpos_diff >= move_thresh:
                logger.debug(f'crane:{crane_no} gpos {self._last_gpos[crane_no]} => {crane_info.g_pos}')
                return True

    def _crane_is_in_order(self, crane_no, offset):
        utm = self.get_position(plc_cache.get_crane(crane_no), offset)

        # check position after last crane
        last_crane = crane_no - 1
        if last_crane in self._g_pos_dict:
            utm_last = self.get_position(plc_cache.get_crane(last_crane))
            if utm_last is not None and (utm_last[0] > utm[0] or utm_last[1] > utm[1]):
                return False

        # check position before next crane
        next_crane = crane_no + 1
        if next_crane in self._g_pos_dict:
            utm_next = self.get_position(plc_cache.get_crane(next_crane))
            if utm_next is not None and (utm_next[0] < utm[0] or utm_next[1] < utm[1]):
                return False

        return True

    def _offset_diff_is_small(self, crane_no, offset):
        if offset is None:
            return False
        old_offset = crane_calib.get_crane_calib_offset(crane_no)
        return abs(old_offset - offset) < self._offset_min_updated_thresh

    def _offset_is_abnormal(self, crane_no, offset=None):
        if offset is None:
            offset = crane_calib.get_crane_calib_offset(crane_no)
        if offset is not None and abs(offset) > self._offset_max_updated_thresh:
            return True

        return False

    def _offset_has_similar_gpos_jump(self, crane_no, offset_diff):
        # only consider jump after last calibration
        if not self._gpos_has_jump_after_last_calibration(crane_no):
            return False
        jump = plc_cache.get_gpos_jump(crane_no)
        if jump is None:
            return False

        # gpos is in centimeter, while offset is in meter
        jump_min = jump * 0.8 * 0.01
        jump_max = jump * 1.2 * 0.01
        # in case jump is negtive
        if jump_min > jump_max:
            jump_min, jump_max = jump_max, jump_min
        logger.debug(f'crane:{crane_no}, gpos jump: {jump}, offset diff: {offset_diff}')
        if offset_diff > jump_min and offset_diff < jump_max:
            return True
        else:
            return False

    def _gpos_has_jump_after_last_calibration(self, crane_no):
        ts_jump = plc_cache.get_gpos_jump_ts(crane_no)
        if ts_jump is None:
            return False
        ts_calib = crane_calib.get_crane_calib_timestamp(crane_no)
        if ts_calib is None:
            return True

        return ts_jump > ts_calib

    def _record_calibration_gpos(self, crane_no):
        crane_info = plc_cache.get_crane(crane_no)
        if crane_info is not None:
            center = self.get_position(crane_info)
            logger.debug(
                f'crane:{crane_no} gpos@calibration {self._last_calibration_gpos[crane_no]} => {crane_info.g_pos}, current position: {center[0]}, {center[1]}'
            )
            self._last_calibration_gpos[crane_no] = crane_info.g_pos

    def verify_param(self):
        count = []
        print(f'lane theta:  {CRANE_LANE_THETA:.10f}')
        for k in sorted(self._g_pos_dict.keys()):
            p = self._g_pos_dict[k]
            theta = math.atan2(p[0], p[2])
            print(f'{k} {theta-CRANE_LANE_THETA:.6f} {theta:.10f}')
            if theta < 0.75:
                count.append(theta)

        print('mean', sum(count)/len(count))
        print('min:', min(count), 'max:', max(count))


if __name__ == '__main__':
    # left_img = cv2.imread(
    #     '/data/datasets/crane_images/images/raw_images/202105211117/31/left/20210521_141954.jpg')
    # right_img = cv2.imread(
    #     '/data/datasets/crane_images/images/raw_images/202105211117/31/right/20210521_141956.jpg')
    # CraneCalibrator().set_crane_camera_pair_frames(31, [left_img, right_img])
    # CraneCalibrator().verify_param()

    CraneCalibrator()._coastline_ts['50_left'] = 1676204374
    CraneCalibrator()._coastline_ts['50_right'] = 1676377174
    CraneCalibrator()._coastline_ts['51_right'] = 1676549974
    from model.wechat_message import WeChatMessage
    wechat_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7c43c966-6c57-4898-9332-77142805780a"
    wechat = WeChatMessage(wechat_url)
    CraneCalibrator().notice_uncalibration(wechat, range(31,57), range(49,57))
