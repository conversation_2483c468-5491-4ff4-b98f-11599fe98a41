#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
HOME="$(cd ~ && pwd)"

source $DOCKER_PATH/common.sh
echo "MYSQL_DB_DOCKER_NAME:$MYSQL_DB_DOCKER_NAME"

DB_ROOT_DIR=$LOCAL_DIR
#rm -rf $DB_ROOT_DIR/mysql
if [[ ! -e $DB_ROOT_DIR/mysql/data ]];then
    mkdir -p $DB_ROOT_DIR/mysql/data
fi

if [[ ! -e $DB_ROOT_DIR/docker-entrypoint-initdb.d ]];then
    mkdir -p  $DB_ROOT_DIR/docker-entrypoint-initdb.d
fi

if [[ ! -e $DB_ROOT_DIR/data ]];then
    mkdir -p  $DB_ROOT_DIR/data
fi
echo "DB_ROOT_DIR:$DB_ROOT_DIR"
docker pull $MYSQL_DB_IMG
docker stop $MYSQL_DB_DOCKER_NAME
docker rm $MYSQL_DB_DOCKER_NAME
mysql_username=fabutech
mysql_password=fabu1#%
mysql_root_password=fabu1#%
mysql_db=antenna_tos
cp $LOCAL_DIR/common/create_table_yz.sql $DB_ROOT_DIR/docker-entrypoint-initdb.d/
cp $LOCAL_DIR/common/docker_mysql_privileges.sql $DB_ROOT_DIR/docker-entrypoint-initdb.d
#-v $DB_ROOT_DIR/mysql/data:/var/lib/mysql \
docker run -it \
    --name $MYSQL_DB_DOCKER_NAME \
    -v $DB_ROOT_DIR/data:/var/data \
    -v $DB_ROOT_DIR/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d \
    -v /etc/localtime:/etc/localtime:ro \
    --add-host 8100886c2eda:127.0.0.1 --hostname 8100886c2eda \
    -p $ANTENNA_MYSQL_DB_PORT:3306 \
    -e MYSQL_DATABASE=${mysql_db} \
    -e MYSQL_ROOT_PASSWORD=${mysql_root_password} \
    -e MYSQL_USER=${mysql_username} \
    -e MYSQL_PASSWORD=${mysql_password} \
    -d  $MYSQL_DB_IMG\
    --character-set-server=utf8mb4 \
    --collation-server=utf8mb4_general_ci \
    --lower_case_table_names=1 \
    --skip-name-resolve=1 \
    --max_connections=1000 \
    --wait_timeout=240 \
    --interactive_timeout=240 \
    --connect_timeout=60 \
    --net_read_timeout=60 \
    --binlog_expire_logs_seconds=302400 \
    --slow_query_log=ON \
    --long_query_time=3 \
    --thread_cache_size=36 \
    --default_authentication_plugin=mysql_native_password \
    --default-time-zone='+8:00' \

docker start $MYSQL_DB_DOCKER_NAME
docker exec $MYSQL_DB_DOCKER_NAME bash -c 'env'
echo "INFO: mysql database starting finshed."

