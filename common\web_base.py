import json
import logging
import time
import uuid
from json import <PERSON><PERSON><PERSON><PERSON><PERSON>

from google.protobuf import json_format

from cache import vehicle_cache
from cache.command_cache import CommandCache
from cache.map_cache import MapCache
from cache.config_cache import ConfigCache

from common.logger import logger
from model import system_status
from proto.antenna_pb2 import RemoteCommand, MapAdjustment, MapShip
from proto.cache_pb2 import StackForbidInfos
from proto.system_status_cache_pb2 import SystemStatusDetail
from net_service import AntennaNetService
from config.config_manage import ConfigManage
from common.exception_handler import http_expection_catch


class MyEncoder(JSONEncoder):
    def default(self, o):
        return json.loads(json_format.MessageToJson(o))


class WebAppBase(object):
    def __init__(self, db = None, tos_bridge = None, insert_tos_command = None, map_ship = None):
        self._db = db
        self._cache = CommandCache()
        self._map_cache = MapCache()
        self._vehicle_cache = vehicle_cache
        self._config_cache = ConfigCache()
        self._net_server = AntennaNetService(db = db,tos_bridge = tos_bridge,insert_tos_command = insert_tos_command, map_ship = map_ship)

    @http_expection_catch
    def vehicle_status(self):
        res = self._net_server.UtilGetVehicleStatus(False)
        return json.dumps(res, cls=MyEncoder)

    @http_expection_catch
    def command_status(self, request):
        vehicle_names = []
        if 'vehicle_name' in request.args:
            vehicle_names = [request.args['vehicle_name']]
        else:
            vehicle_names = self._vehicle_cache.get_online_trucks()
        res = self._net_server.UtilGetCommandStatus(vehicle_names)
        return json.dumps(res, cls=MyEncoder)

    @http_expection_catch
    def last_command_status(self, request):
        vehicle_names = []
        if 'vehicle_name' in request.args:
            vehicle_names = [request.args['vehicle_name']]
        else:
            vehicle_names = self._vehicle_cache.get_online_trucks()
        res = self._net_server.UtilGetLastCommandStatus(vehicle_names)
        return json.dumps(res, cls=MyEncoder)

    @http_expection_catch
    def remote_command(self, request):
        logger.debug(f'web remote_command:{request.json}')
        vehicle_name = request.json['vehicle_name']
        command = RemoteCommand()
        json_format.Parse(json.dumps(request.json['remote_command'], cls=MyEncoder), command)
        self._net_server.UtilInsertRemoteCommand(vehicle_name, command)
        return 'OK'

    @http_expection_catch
    def tos_command(self, request):
        logger.debug(f'web insert tos_command:{request.json}')
        vehicle_name = request.json['vehicle_name']
        tos_command = request.json['tos_command']
        self._net_server.UtilInsertTosCommand(vehicle_name, tos_command)
        return 'OK'

    @http_expection_catch
    def recent_tos_command(self, request):
        vehicle_name = request.args['vehicle_name']
        filtered = self._net_server.UtilGetRecentTosCommand(vehicle_name)
        return json.dumps(filtered)

    @http_expection_catch
    def recent_tos_command_v1(self, request):
        vehicle_name = request.args['vehicle_name']
        filtered = self._net_server.UtilGetRecentTosCommandV1(vehicle_name)
        return json.dumps(filtered)

    @http_expection_catch
    def reset_tos_command(self, request):
        vehicle_name = request.json['vehicle_name']
        id = int(request.json['id'])
        logger.debug(f'web reset tos_command:{request.json}')
        if self._net_server.UtilResetTosCommand(vehicle_name, id):
            system_status.insert(vehicle_name, SystemStatusDetail.TOS_CMD_RESET, 'by old web manual')
            return 'OK'
        else:
            return 'ERROR'

    @http_expection_catch
    def get_map_adjustment(self):
        return json.dumps(self._net_server.UtilGetMapAdjustment(), cls=MyEncoder)

    @http_expection_catch
    def add_map_adjustment(self, request):
        map_adjustment = MapAdjustment()
        json_format.Parse(json.dumps(request.json, cls=MyEncoder), map_adjustment)
        self._net_server.UtilAddMapAdjustment(map_adjustment)
        return 'OK'

    @http_expection_catch
    def remove_map_adjustment(self, request):
        logger.debug(f'[RemoveAdjustment:] remove map_adjustment:{request.json}')
        self._net_server.UtilRemoveMapAdjustment(request.json['uuid'])
        return 'OK'

    @http_expection_catch
    def update_map_adjustment_description(self, request):
        logger.debug(f'[UpdateAdjustmentDes:] update map_adjustment:{request.json}')
        self._net_server.UtilUpdateMapAdjustmentDescription(request.json['uuid'], request.json['adjustmentReasonDescription'])
        return 'OK'

    @http_expection_catch
    def update_map_adjustment(self, request):
        adjustments, block_id = self._map_cache.get_manual_map_adjustment()
        logger.debug(f'[UpdateAdjustment:] update map_adjustment:{request.json}')
        adj = MapAdjustment()
        json_format.Parse(json.dumps(request.json, cls=MyEncoder), adj)
        self._net_server.UtilUpdateMapAdjustment(adj)
        return 'OK'

    @http_expection_catch
    def get_all_ship(self):
        return json.dumps(self._net_server.UtilGetAllMapShip(), cls=MyEncoder)

    ##下面接口会被启用,实际无用
    @http_expection_catch
    def set_job_offset(self):
        return 'NO_SUPPORT'

    @http_expection_catch
    def add_ship(self, request):
        map_ship = MapShip()
        json_format.Parse(json.dumps(request.json, cls=MyEncoder), map_ship)
        map_ship.uuid = str(uuid.uuid1())
        self._map_cache.set_all_map_ship(self._map_cache.get_all_map_ship() + [map_ship])
        return 'OK'

    @http_expection_catch
    def remove_ship(self, request):
        m = [c for c in self._map_cache.get_all_map_ship() if c.uuid != request.json['uuid']]
        self._map_cache.set_all_map_ship(m)
        return 'OK'

    @http_expection_catch
    def update_ship_description(self, request):
        ships = self._map_cache.get_all_map_ship()
        for ship in ships:
            if ship.uuid == request.json['uuid']:
                ship.reason_description = request.json['description']
        self._map_cache.set_all_map_ship(ships)
        return 'OK'


    ###以下用于仿真
    @http_expection_catch
    def finish_tos_command(self, request):
        id = request.json['id']
        self._db.finish_tos_command(id)
        return 'OK'

    @http_expection_catch
    def update_crane(self, request):
        update_column = {}
        update_column['CRANE_ID'] = request.json["crane_id"]
        update_column['LANE_NO'] = request.json["lane_no"]
        update_column['VESSEL_DIRECTION'] = request.json["vessel_direction"]
        update_column['CYCLE_MODE'] = request.json.get("cycle_mode",'S')
        self._db.update_crane_info(update_column)
        return 'OK'

    @http_expection_catch
    def update_tos_cmd(self, request):
        id = request.json["id"]
        name = request.json["name"]
        value = request.json["value"]
        value_type = request.json["type"]
        self._db.update_tos_command(id,name,value,value_type)
        return 'OK'

    @http_expection_catch
    def set_vehicle_fleet(self, request):
        logger.debug(f'web set_vehicle_fleet:{request.json}')
        for fleet_data in request.json["fleet_data"]:
            fleet_id = fleet_data["fleet_id"]
            vehicle_name = fleet_data["vehicle_name"]
            self._config_cache.set_vehicle_fleet(vehicle_name, fleet_id)
        return 'OK'

    @http_expection_catch
    def set_stack_forbid_areas(self, request):
        logger.debug(f'web set_stack_forbid_areas:{request.json}')
        infos = StackForbidInfos()
        if request.json and request.json != {}:#否则清空
            json_format.Parse(json.dumps(request.json, cls=MyEncoder), infos)
        self._map_cache.hset_stack_forbid_areas(infos.forbids)
        return 'OK'
