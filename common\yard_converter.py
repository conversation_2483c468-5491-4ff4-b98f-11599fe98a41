from common.singleton import Singleton


class YardConverter(metaclass=Singleton):
    def __init__(self):
        self._yard_to_no = dict()
        self._no_to_yard = dict()
        self.add('61', 1)
        self.add('62', 2)
        self.add('63', 3)
        self.add('64', 4)
        self.add('65', 5)
        self.add('66', 6)
        self.add('67', 7)
        self.add('68', 8)
        self.add('69', 9)
        self.add('6A', 10)
        self.add('6B', 11)
        self.add('6C', 12)
        self.add('6D', 13)
        self.add('6E', 14)

        self.add('71', 15)
        self.add('72', 16)
        self.add('73', 17)
        self.add('74', 18)
        self.add('75', 19)
        self.add('76', 20)
        self.add('77', 21)
        self.add('78', 22)
        self.add('79', 23)
        self.add('7A', 24)
        self.add('7B', 25)
        self.add('7L', 26)
        self.add('7M', 27)

        self.add('81', 29)
        self.add('82', 30)
        self.add('83', 31)
        self.add('84', 32)
        self.add('85', 33)
        self.add('86', 34)
        self.add('87', 35)
        self.add('88', 36)
        self.add('89', 37)
        self.add('8A', 38)
        self.add('8B', 39)
        # self.add('8C', 40)
        # self.add('8D', 41)
        # self.add('8E', 42)
        self.add('8L', 40)
        self.add('8M', 41)


        self.add('91', 43)
        self.add('92', 44)
        self.add('93', 45)
        self.add('94', 46)
        self.add('95', 47)
        self.add('96', 48)
        self.add('97', 49)
        self.add('98', 50)
        self.add('9B', 53)
        self.add('9C', 54)
        self.add('9D', 55)
    def add(self, yard, no):
        self._yard_to_no[yard] = no
        self._no_to_yard[no] = yard

    def to_yard(self, no):
        return self._no_to_yard[no] if no in self._no_to_yard else None

    def to_no(self, yard):
        return self._yard_to_no[yard] if yard in self._yard_to_no else None

    def all_yard_no(self):
        return self._no_to_yard.keys()
