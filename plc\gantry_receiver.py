import selectors
import socket
import time
import traceback
import types
import math
from typing import Optional, Dict, Tu<PERSON>, List

from google.protobuf.text_format import MessageToString

from cache import plc_cache
from common.constant import GANTRY_MSG_HEAD, GANTRY_MSG_END, LISTEN_GANTRY_PORT
from common.logger import logger, init_plc_log
from common.singleton import Singleton
from common.master import Master
from common.periodical_task import PeriodicalTask
from plc.gantry_fetcher import GantryFetcher
from plc.gantry_info import parse_from
from plc.model import PlcGposSnapshot
from proto.cache_pb2 import GantryInfo
from config.config_manage import ConfigManage


class GantryInfoContainer(metaclass=Singleton):
    def __init__(self):
        self._app_point_to_center = 3.69  # 龙门吊标定得到的app point距离center差值
        self._g_pos_dict = dict()
        self._g_pos_dir = dict()
        # 改为block_nul对应的_g_pos_dict及_g_pos_dir,block_nul<->yard
        self._g_pos_dict[1] = (774509, 404234.298612, 3294901.25775543)  # 109,1->61
        self._g_pos_dir[1] = (0.722228768192, 0.69165425)
        self._g_pos_dict[2] = (724964, 404166.77264120476, 3294900.1541827926)  # 106,2->62
        self._g_pos_dir[2] = (0.722228768192, 0.69165425)
        self._g_pos_dict[3] = (725199, 404158.67457607988, 3294909.0785563369)  # 105,3->63
        self._g_pos_dir[3] = (0.722228768192, 0.69165425)
        # self._g_pos_dict[4] = (751412, 404145.93858448532, 3294960.296188815) #110,4->64
        # self._g_pos_dir[4] = (0.722228768192, 0.69165425)#?
        self._g_pos_dict[4] = (738304, 404136.43030465336, 3294951.1807613811)  # 108,4->64
        self._g_pos_dir[4] = (0.722228768192, 0.69165425)
        self._g_pos_dict[5] = (686217, 404090.55335174315, 3294923.9588179258)  # 103,5->65
        self._g_pos_dir[5] = (0.722228768192, 0.69165425)
        self._g_pos_dict[6] = (823676, 404158.15130715584, 3295052.1384425336)  # 102,6->66
        self._g_pos_dir[6] = (0.7219711664802281, 0.6919231422428209)
        self._g_pos_dict[7] = (774585, 404114.1389870515, 3295026.9565280853)  # 107,7->67
        self._g_pos_dir[7] = (0.722228768192, 0.69165425)
        self._g_pos_dict[8] = (774603, 404080.0765, 3295062.49111244)  # 104,8->68
        self._g_pos_dir[8] = (0.72300253528, 0.69084538)
        self._g_pos_dict[9] = (0, 403515.167610, 3294537.593380)  # 111,9->69
        self._g_pos_dir[9] = (0.718340, 0.688793)
        self._g_pos_dict[10] = (0, 403479.992459, 3294568.683307)  # 114,10->6A
        self._g_pos_dir[10] = (0.723135, 0.691707)
        self._g_pos_dict[11] = (0, 403474.261539, 3294579.958072)  # 145,11->6B
        self._g_pos_dir[11] = (0.719986, 0.688600)
        self._g_pos_dict[12] = (0, 403440.633312, 3294610.355094)  # 146,12->6C
        self._g_pos_dir[12] = (0.722390, 0.691901)
        self._g_pos_dict[13] = (0, 403432.123597, 3294620.905060)  # 147,13->6D
        self._g_pos_dir[13] = (0.722550, 0.689541)

        self._g_pos_dict[15] = (781009, 404239.0133, 3294905.7598)  # 101,15->71
        self._g_pos_dir[15] = (0.722228768192, 0.69165425)
        self._g_pos_dict[16] = (0, 403643.095330, 3294397.650357)  # 116,16->72
        self._g_pos_dir[16] = (0.722271, 0.692419)
        self._g_pos_dict[17] = (0, 403635.313333, 3294406.989600)  # 117,17->73
        self._g_pos_dir[17] = (0.721752, 0.692283)
        self._g_pos_dict[18] = (0, 403606.038809, 3294440.973092)  # 110,18->74
        self._g_pos_dir[18] = (0.719625, 0.691299)
        self._g_pos_dict[19] = (0, 403594.883041, 3294449.977083)  # 115,19->75
        self._g_pos_dir[19] = (0.722148, 0.691205)
        self._g_pos_dict[20] = (0, 403563.755422, 3294482.667270)  # 118,20->76
        self._g_pos_dir[20] = (0.721695, 0.691291)
        self._g_pos_dict[21] = (885999, 404194.67482833588, 3295103.8969922862)  # 119,21->77
        self._g_pos_dir[21] = (0.722228768192, 0.69165425)
        self._g_pos_dict[22] = (0, 403520.109083, 3294526.268404)  # old114,22->78
        self._g_pos_dir[22] = (0.722814, 0.692044)
        '''
        self._g_pos_dict[23] = (0, 403431.941414, 3294620.153331)  # old123,23->79? go to 7B.acutal result is 7B by lzc 20221019
        self._g_pos_dir[23] = (0.722701, 0.690572)
        '''
        self._g_pos_dict[23] = (0, 403516.676124, 3294539.0398453)  # 79 same with 69 by lzc 20221019，新增2.1m偏移，20250616
        self._g_pos_dir[23] = (0.718340, 0.688793)
        self._g_pos_dict[24] = (0, 403480.633094, 3294568.406753)  # 112,24->7A
        self._g_pos_dir[24] = (0.722243, 0.691801)
        self._g_pos_dict[25] = (0, 403474.877502, 3294579.557029)  # 131,25->7B
        self._g_pos_dir[25] = (0.720565, 0.690214)
        self._g_pos_dict[26] = (0, 403441.463143, 3294611.543467)  # 120,26->7C
        self._g_pos_dir[26] = (0.721385, 0.690656)

        self._g_pos_dict[27] = (0, 403434.24636774, 3294620.9287088597)  # 7D, 基于9D并根据贝位和磁钉表修正
        self._g_pos_dir[27] = (0.721153, 0.690763)

        self._g_pos_dict[28] = (0, 403401.262655, 3294653.320239)  # 136,28->7E
        self._g_pos_dir[28] = (0.722298, 0.691410)
        self._g_pos_dict[29] = (0, 403675.501966, 3294365.891437)  # 139,29->81
        self._g_pos_dir[29] = (0.721921, 0.691486)
        self._g_pos_dict[30] = (0, 403642.393690, 3294398.155796)  # 120,82->39
        self._g_pos_dir[30] = (0.722811, 0.691808)
        self._g_pos_dict[31] = (0, 403637.815925, 3294408.758705)  # 117,83->31
        self._g_pos_dir[31] = (0.720250, 0.690699)
        self._g_pos_dict[32] = (0, 403604.073578, 3294440.719688)  # 120,84->32
        self._g_pos_dir[32] = (0.721560, 0.691305)
        self._g_pos_dict[33] = (0, 403595.179996, 3294448.620091)  # 137,85->33
        self._g_pos_dir[33] = (0.722067, 0.692094)
        self._g_pos_dict[34] = (0, 403563.546713, 3294485.036486)  # 142,86->34
        self._g_pos_dir[34] = (0.722176, 0.689872)
        self._g_pos_dict[35] = (0, 403552.537891, 3294490.680057)  # 141,87->35
        self._g_pos_dir[35] = (0.723710, 0.691929)
        #self._g_pos_dict[36] = (0, 403532.941044, 3294538.065968)  # 136,88->36
        #self._g_pos_dict[36] = (0, 403520.679353768, 3294526.290954108)
        #self._g_pos_dir[36] = (0.720620, 0.690369)
        self._g_pos_dict[36] = (0, 403521.060996, 3294527.508049)  # 136,88->36
        self._g_pos_dir[36] = (0.722057, 0.691043)
        '''
        self._g_pos_dict[37] = (0, 403916.734322, 3294938.950732)  # 117,89->37 x + 400, y + 400
        self._g_pos_dir[37] = (0.725403, 0.695284)
        self._g_pos_dict[38] = (0, 403882.825456, 3294970.569988)  # 116,8A->38 x + 400, y + 400
        self._g_pos_dir[38] = (0.727071, 0.696260)
        '''
        self._g_pos_dict[37] = (0, 403503.5179597985, 3294526.0620877314)  # 121,89->37，实车数据中龙门吊绘制靠前9.9米，已修正
        self._g_pos_dir[37] = (0.7290608900923752, 0.698674555173888)
        self._g_pos_dict[38] = (0, 403483.58184624603, 3294568.768038328)  # 120,8A->38，实车数据中龙门吊绘制靠前9.5米，已修正
        self._g_pos_dir[38] = (0.7212696324103666, 0.6922823373952837)
        self._g_pos_dict[39] = (0, 403473.93563479144, 3294578.0707047554)  # 8B，基于9B并根据贝位和磁钉表修正
        self._g_pos_dir[39] = (0.7213456407177036, 0.6911694604056715)
        self._g_pos_dict[40] = (0, 403440.610649397, 3294609.79050093)  # 8C，基于9C并根据贝位和磁钉表修正
        self._g_pos_dir[40] = (0.722690, 0.692023)
        self._g_pos_dict[41] = (0, 403434.31848304, 3294620.99778516)  # 8D，基于9D并根据贝位和磁钉表修正
        self._g_pos_dir[41] = (0.721153, 0.690763)

        self._g_pos_dict[42] = (0, 403398.8024010, 3294650.086306)  # 150,8E->42
        self._g_pos_dir[42] = (0.721849, 0.6914585)
        self._g_pos_dict[43] = (0, 403675.3987177, 3294365.724327)  # 135,91->43
        self._g_pos_dir[43] = (0.722074, 0.691464)
        self._g_pos_dict[44] = (0, 403644.60608781, 3294399.357574668)  # 136,92->44
        self._g_pos_dir[44] = (0.721599, 0.691255)
        self._g_pos_dict[45] = (0, 403637.72942903, 3294405.103122902)  # 125,93->45
        self._g_pos_dir[45] = (0.720720, 0.692912)
        self._g_pos_dict[46] = (0, 403603.648581153, 3294440.64978572)  # 126,94->46
        self._g_pos_dir[46] = (0.722013, 0.691474)
        self._g_pos_dict[47] = (0, 403589.610384479, 3294447.61119139)  # 147,95->47
        self._g_pos_dir[47] = (0.725214, 0.692540)
        self._g_pos_dict[48] = (0, 403561.491542126, 3294482.85761766)  # 128,96->48
        self._g_pos_dir[48] = (0.723241, 0.691241)
        self._g_pos_dict[49] = (0, 403553.371219153, 3294493.39503034)  # 137,97->49
        self._g_pos_dir[49] = (0.723039, 0.690420)
        self._g_pos_dict[50] = (0, 403521.347659213, 3294528.34219662)  # 134,98->50
        self._g_pos_dir[50] = (0.721912, 0.690843)
        self._g_pos_dict[51] = (0, 403514.4695410859, 3294535.966737329)  # 171,99->51
        self._g_pos_dir[51] = (0.7214571241962101,0.6913887453222356)
        self._g_pos_dict[52] = (0, 403484.8690151968, 3294570.4494637134)  # 168,9A->52
        self._g_pos_dir[52] = (0.720771661988664, 0.6911042425518492)
        self._g_pos_dict[53] = (0, 403474.36844217585, 3294578.485406432)  # 150,9B->53
        self._g_pos_dir[53] = (0.7213456407177036, 0.6911694604056715)
        self._g_pos_dict[54] = (0, 403440.393842397, 3294609.58289403)  # 152,9C->54
        self._g_pos_dir[54] = (0.722690, 0.692023)
        self._g_pos_dict[55] = (0, 403434.534828940, 3294621.20501406)  # 149,9D->55
        self._g_pos_dir[55] = (0.721153, 0.690763)
        self._g_pos_dict[56] = (0, 403402.8455839832, 3294654.1006080154)  # 110,9E->56
        self._g_pos_dir[56] = (0.7214129235458417, 0.690878978460492)
        self._g_pos_dict[57] = (0, 403673.383046494,3294365.9598944797)  # 135,X1->57
        self._g_pos_dir[57] = (0.7229584769209334, 0.6914242078321442)
        self._g_pos_dict[58] = (0, 403642.7464298198,3294398.8652026537)  # 136,X2->58
        self._g_pos_dir[58] = (0.7224631231829041, 0.6915494464853047)
        self._g_pos_dict[59] = (0,403635.5006039675,3294407.57289096)  #115,X3->59
        self._g_pos_dir[59] = (0.7219504677132508,0.6914903873823826)
        self._g_pos_dict[60] = (0,403603.21218681085,3294440.3961306885)  #160,X4->60
        self._g_pos_dir[60] = (0.7223107630723204,0.6916844439144809)
        self._g_pos_dict[61] = (0,403596.3329091931,3294449.6666109487)  #131,X5->61
        self._g_pos_dir[61] = (0.7215586250404739,0.6913682231619824)
        self._g_pos_dict[62] = (0,403564.4406395975,3294482.225755884)  #188,X6->62
        self._g_pos_dir[62] = (0.7217940373454823,0.6916865794538211)
        self._g_pos_dict[63] = (0,403557.4447826561,3294492.665236449)  #113,X7->63
        self._g_pos_dir[63] = (0.7210383571152952,0.6908446059612445)
        self._g_pos_dict[64] = (0,403521.72383036977,3294526.7883548425)  #178,X8->64
        self._g_pos_dir[64] = (0.721876504681133,0.6916433492625408)
        self._g_pos_dict[65] = (0,403512.1034576317,3294537.1715855445)  #119,X9->65
        self._g_pos_dir[65] = (0.7223198452429483,0.6907791876582839)
        self._g_pos_dict[66] = (0, 403481.93300150475, 3294568.9402409387)  # 152,XA->66
        self._g_pos_dir[66] = (0.7217155535807368, 0.6914255446613018)
        self._g_pos_dict[67] = (0, 403472.36700160505, 3294577.2621802464)  # 187,XB->67
        self._g_pos_dir[67] = (0.7223037490604866, 0.691551975755081)
        self._g_pos_dict[68] = (0, 403441.7774440754, 3294612.944891464)  # 116,XC->68
        self._g_pos_dir[68] = (0.7221558632159456, 0.6904813362129533)
        self._g_pos_dict[69] = (0, 403432.47904232884, 3294619.4334525676)  # 111,XD->69
        self._g_pos_dir[69] = (0.7221638693341713, 0.6913625964735626)
        self._g_pos_dict[70] = (0, 403400.84405020054, 3294652.393327914)  # 190,XE->70
        self._g_pos_dir[70] = (0.7222197185817415, 0.6914764410118124)
        self._g_pos_dict[71] = (0, 403674.6508426783, 3294367.389076358)  # 159,Y1->71
        self._g_pos_dir[71] = (0.7224705499004003, 0.6909224499084838)
        self._g_pos_dict[72] = (0, 403643.2750887208, 3294399.8780973554)  # 136,Y2->72
        self._g_pos_dir[72] = (0.7223464558382647, 0.6911532845434847)
        self._g_pos_dict[73] = (0, 403636.7633406688,3294407.110912533)  # 175,Y3->73
        self._g_pos_dir[73] = (0.721497498862027,0.6916267514025506)
        self._g_pos_dict[74] = (0,403604.2937843406,3294441.2862486215)  # 186,Y4->74
        self._g_pos_dir[74] = (0.7218232748584227,0.6911801106844673)
        self._g_pos_dict[75] = (0,403592.6903809367,3294449.1166969324)  # 177,Y5->75
        self._g_pos_dir[75] = (0.7230858640980405,0.6915546045197059)
        self._g_pos_dict[76] = (0,403563.75510592153,3294482.3183797006)  # 176,Y6->76
        self._g_pos_dir[76] = (0.722009445962894,0.6915299109801141)
        self._g_pos_dict[77] = (0,403553.69920958,3294490.8923152536)  # 189,Y7->77
        self._g_pos_dir[77] = (0.7227188785245834,0.6915591421306254)
        self._g_pos_dict[78] = (0,403519.3595286043,3294527.2215833203)  # 172,Y8->78
        self._g_pos_dir[78] = (0.7228033536226781,0.6913401064511875)
        self._g_pos_dict[79] = (0,403510.7186831863,3294535.8578553884)  # 149,Y9->79
        self._g_pos_dir[79] = (0.7229515397923748,0.6914477275332053)
        self._g_pos_dict[80] = (0, 403480.8044639739, 3294568.273359079)  # 152,YA->80
        self._g_pos_dir[80] = (0.7222527627316437, 0.6916895407464628)
        self._g_pos_dict[81] = (0,403471.6606036811,3294578.6993098687)  # 187,YB->81
        self._g_pos_dir[81] = (0.7225235061456847,0.6910174348755683)
        self._g_pos_dict[82] = (0,403441.46483635623,3294610.2589177815)  # 110,YC->82
        self._g_pos_dir[82] = (0.7219078519996668,0.6915474985315838)
        self._g_pos_dict[83] = (0,403433.29042699095,3294617.20113087729)  # 167,YD->83
        self._g_pos_dir[83] = (0.7219418461084609,0.6922906401327565)
        self._g_pos_dict[84] = (0, 403400.9884941442, 3294652.5316232024)  # YE，基于XE并根据贝位和磁钉表修正
        self._g_pos_dir[84] = (0.7222197185817415, 0.6914764410118124)
        # 如果数据采集不是用T500，需要添加偏移
        # 补充：运行箱区道次标定程序时已默认添加偏移，无需额外添加
        # imu_offset = 3.544 ->offset_x,offset_y=(2.6614157, 2.454744)
        # self._g_lane_dir = (0.721275896, 0.692647876)
        # self._lane_theta = 0.765153713

    def gantry_time_valid(self, info, expire_second=5.):
        return time.time() - info.recv_timestamp / 1000000000.0 < expire_second

    def get_gantry_nos(self):
        return plc_cache.get_online_gantry_nos()

    def get_all_gantry_info(self) -> List[GantryInfo]:
        gantry_nos = plc_cache.get_online_gantry_nos()
        infos = plc_cache.get_gantries(gantry_nos)
        results = []
        for info in infos:
            if info is not None:
                if self.gantry_time_valid(info, 5.):
                    results.append(info)
                else:
                    diff = time.time() - info.recv_timestamp / 1000000000.0
                    #logger.debug(f"get_all_gantry_info time not valid, gantry_no: {info.gantry_no}, diff: {diff}")
        return results

    def get_position(self, info) -> Optional[Tuple]:
        if info is None:
            return None
        block_nul = info.block_nul
        if block_nul == 0:
            #logger.debug(f'get_position gantry_no:{info.gantry_no} not find block nul')
            return None
        if block_nul in self._g_pos_dict:
            g_pos = info.g_pos
            g_pos_offset = g_pos - self._g_pos_dict[block_nul][0]
            x = self._g_pos_dict[block_nul][1] + g_pos_offset / 1000 * self._g_pos_dir[block_nul][0]
            y = self._g_pos_dict[block_nul][2] + g_pos_offset / 1000 * self._g_pos_dir[block_nul][1]
            return (x, y)
        return None

    def get_center_position(self, info) -> Optional[Tuple]:
        lane_dir = self.get_gantry_direction(info.block_nul)
        if lane_dir is None:
            return None
        app_point = self.get_position(info)
        if app_point is not None:
            return (app_point[0] + self._app_point_to_center * math.cos(lane_dir),
                    app_point[1] + self._app_point_to_center * math.sin(lane_dir))
        return None

    def get_gantry_direction(self, block_nul):
        if block_nul not in self._g_pos_dict:
            return None
        return math.atan2(self._g_pos_dir[block_nul][1], self._g_pos_dir[block_nul][0])

    def get_spreader_location(self, gantry_info):
        lane_dir = self.get_gantry_direction(gantry_info.block_nul)
        if lane_dir is None:
            return None
        app_point = self.get_position(gantry_info)
        t_pos = gantry_info.t_pos - 1600
        if app_point is not None:
            # 奇数道次向北为正, 偶数道次向南为正. 统一修正回向南为正
            if (gantry_info.block_nul & 1) == 1:
                t_pos = -t_pos
            center_point = (app_point[0] + self._app_point_to_center * math.cos(lane_dir),
                            app_point[1] + self._app_point_to_center * math.sin(lane_dir))
            return (center_point[0] + t_pos / 1000 * math.sin(lane_dir),
                    center_point[1] - t_pos / 1000 * math.cos(lane_dir))

    # only for abb gantry
    def get_gantry_no_matching_self_car(self, vehicle_name):
        gantry_nos = plc_cache.get_online_gantry_nos()
        for gantry_no in gantry_nos:
            gantry_info = plc_cache.get_gantry(gantry_no)
            if gantry_info.agv_id == vehicle_name and gantry_info.job_active:
                logger.debug(f"vehicle:{vehicle_name} match gantry: {gantry_no}")
                return gantry_no
        logger.debug(f"vehicle:{vehicle_name} not found gantry matching")
        return None


class GantryReceiver(metaclass=Singleton):
    def __init__(self):
        self._sel = selectors.DefaultSelector()
        self._gantry_g_pos_snapshot = dict()
        self._running = False
        if ConfigManage().is_pro_env() or ConfigManage().is_dev_env():
            self.register(LISTEN_GANTRY_PORT)
            self._loop_thread = PeriodicalTask(target=self.loop, interval=0)
        if ConfigManage().is_pro_env():
            self._fetch_threads = []
            self._fetchers = [
                GantryFetcher(111, "10.168.171.45", 20010),  # 111
                GantryFetcher(112, "10.168.172.45", 20010),  # 112
                GantryFetcher(113, "10.168.173.45", 20010),  # 113
                GantryFetcher(114, "10.168.174.45", 20010),  # 114
                GantryFetcher(115, "10.168.175.45", 20010),  # 115
                GantryFetcher(116, "10.168.176.45", 20010),  # 116
                GantryFetcher(117, "10.168.177.45", 20010),  # 117
                GantryFetcher(118, "10.168.178.45", 20010),  # 118
                GantryFetcher(125, "10.101.125.45", 20010),  # 125
                GantryFetcher(126, "10.101.126.45", 20010),  # 126
                GantryFetcher(127, "10.101.127.45", 20010),  # 127
                GantryFetcher(128, "10.101.128.45", 20010),  # 128
                GantryFetcher(129, "10.101.129.45", 20010),  # 129
                GantryFetcher(130, "10.101.130.45", 20010),  # 130
                GantryFetcher(131, "10.101.131.45", 20010),  # 131
                GantryFetcher(132, "10.101.132.45", 20010),  # 132
                GantryFetcher(133, "10.101.133.45", 20010),  # 133
                GantryFetcher(134, "10.101.134.45", 20010),  # 134
                GantryFetcher(135, "10.101.135.45", 20010),  # 135
                GantryFetcher(136, "10.101.136.45", 20010),  # 136
            ]
        self._update_gantry_no_thread = PeriodicalTask(target=self.update_online_gantries, interval=1, master_only=True)

    def gantry_time_valid(self, info, expire_second=5.):
        return time.time() - info.recv_timestamp / 1000000000.0 < expire_second

    def update_online_gantries(self):
        gantry_nos = []
        start_time = time.time()
        infos = plc_cache.get_ex_gantries()
        for info in infos:
            if self.gantry_time_valid(info, 5):
                gantry_nos.append(info.gantry_no)
        plc_cache.refresh_online_gantries(gantry_nos)
        end_time = time.time()
        logger.debug(f"gantries info:{len(infos)},{len(gantry_nos)} online_gantries:{sorted(gantry_nos)},{end_time-start_time}")

    def update_gantry_msg(self, msg):
        info = parse_from(msg, int(time.time() * 1000000000))
        if info is not None:
            self.update_gantry_info(info)

    def update_gantry_info(self, info: GantryInfo):
        gantry_no = info.gantry_no
        info.is_stable = False
        if gantry_no in self._gantry_g_pos_snapshot:
            if abs(info.g_pos - self._gantry_g_pos_snapshot[gantry_no].g_pos) > 30:
                self._gantry_g_pos_snapshot[gantry_no] = PlcGposSnapshot(info.g_pos, info.recv_timestamp)
            elif (info.recv_timestamp - self._gantry_g_pos_snapshot[gantry_no].timestamp_ns) > 3000000000:
                info.is_stable = True
        else:
            self._gantry_g_pos_snapshot[gantry_no] = PlcGposSnapshot(info.g_pos, info.recv_timestamp)
        #logger.debug(MessageToString(info, as_one_line=True))
        plc_cache.set_gantry(gantry_no, info, 5)

    def fetch(self, fetcher):
        try:
            info = fetcher.fetch()
            self.update_gantry_info(info)
            fetcher.try_count = 0
        except:
            dinfo = traceback.format_exc()
            if "socket.timeout" in dinfo:
                dinfo = "Catch exception. Gantry fetch failed cause socket.timeout {},try_count:{}".format(fetcher._gantry_no,fetcher.try_count)
                fetcher.try_count = fetcher.try_count + 1
                if fetcher.try_count > 100:
                    time.sleep(5)
            logger.debug(dinfo)

    def register(self, port):
        server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server.bind(('', port))
        server.listen()
        server.setblocking(False)
        self._sel.register(server, selectors.EVENT_READ, data=None)
        server.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)                                                                                                                                       
        server.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 10)                                                                                                                                     
        server.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)                                                                                                                                     
        server.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)

    def start(self):
        if not self._running:
            if ConfigManage().is_pro_env():
                for fetcher in self._fetchers:
                    fetcher.try_count = 0
                    self._fetch_threads.append(PeriodicalTask(
                        target=self.fetch, interval=0.06, master_only=True, fetcher=fetcher))
                for fetch_thread in self._fetch_threads:
                    fetch_thread.start()
            if ConfigManage().is_pro_env() or ConfigManage().is_dev_env():
                self._loop_thread.start()
            self._update_gantry_no_thread.start()
            self._running = True

    def loop(self):
        try:
            events = self._sel.select(timeout=None)
            for key, mask in events:
                if key.data is None:
                    self.accept(key.fileobj)
                else:
                    self.process(key, mask)
        except Exception as e:
            logger.warning(f"gantries loop error: {e}, trace:{traceback.format_exc()}")

    def accept(self, sock):
        conn, addr = sock.accept()
        logger.debug('accepted connection from: ' + str(addr))
        conn.setblocking(False)
        data = types.SimpleNamespace(addr=addr, buffer=b'')
        events = selectors.EVENT_READ
        self._sel.register(conn, events, data=data)

    def process(self, key, mask):
        sock = key.fileobj
        data = key.data
        if mask & selectors.EVENT_READ:
            try:
                buffer = sock.recv(1024)
                #logger.debug('--------read %d' % len(buffer))
                if buffer:
                    data.buffer += buffer
                    for msg in self.parse_buffer(data):
                        self.update_gantry_msg(msg)
                else:
                    logger.warning('closing connection to ' + str(data.addr))
                    self._sel.unregister(sock)
                    sock.close()
            except (ConnectionResetError,TimeoutError) as e:
                #except Exception as e:
                logger.warning(f"gantries process closing connection.error: {e}, trace:{traceback.format_exc()}")
                logger.warning('closing connection to' + str(data.addr))
                self._sel.unregister(sock)
                sock.close()
            except Exception as e:
                logger.warning(f"gantry loop err{e}, trace:{traceback.format_exc()}")

    def parse_buffer(self, data):
        ret = []
        while True:
            left = data.buffer.find(GANTRY_MSG_HEAD)
            if left == -1:
                break
            right = data.buffer.find(GANTRY_MSG_END, left)
            if right == -1:
                break
            ret.append(data.buffer[left + 2:right])
            data.buffer = data.buffer[right:]
        # 默认只读取单帧最后一条数据用于更新，但由于仿真是所有gantry复用同一端口，可能有单帧多条有效数据的情况
        if not ConfigManage().is_sim_mode():
            ret = ret[-1:]
        return ret


if __name__ == '__main__':
    print(GantryInfoContainer().get_gantry_no_matching_self_car('T801'))
    info = GantryInfo()
    info.block_nul = 72
    info.g_pos = 2469312
    print(f"block_nul:{info.block_nul},g_pos:{info.g_pos},pos:{GantryInfoContainer().get_position(info)}")
    print(GantryInfoContainer().get_spreader_location(31))
