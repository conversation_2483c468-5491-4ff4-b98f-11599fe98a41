import traceback
import uuid

from google.protobuf.text_format import Message<PERSON><PERSON><PERSON>tring

from antenna_server import AntennaServer as AntennaServerBase
from cache import vehicle_cache
from cache.danger_warning import DangerWarningCache
from cache.command_cache import CommandCache
from cache.map_cache import MapCache
from cache.ecs_command_cache import EcsCommandCache
from cache.lock_cache import Lock<PERSON>ache
from cache.event_cache import Event<PERSON>ache
from cache.cloud_cache import Cloud<PERSON>ache
from common.logger import logger
from common.name_converter import Name<PERSON>onverter
from common.constant import NODE_NAME_PREFIX
from model import system_status
from proto.system_status_cache_pb2 import SystemStatusDetail
from proto.antenna_pb2 import RemoteCommand
from meishanalia.cache.vms_request_cache import VmsRequestCache
from meishanalia.tos_bridge_alia import TosBridge
from meishanalia.tos_db_alia import TosDb
from plc.crane_receiver import CraneInfoContainer
from plc.gantry_receiver import GantryInfoContainer
from proto import antenna_pb2
from config.config_manage import ConfigManage


class AntennaServer(AntennaServerBase):
    def __init__(self):
        self._ecs_event = None
        self._tp_cache = None
        self._crane_info_receiver = CraneInfoContainer()
        self._gantry_info_receiver = GantryInfoContainer()
        self._cache = CommandCache()
        self._map_cache = MapCache()
        #self._vms_request_cache = VmsRequestCache()
        self._lock_cache = LockCache()
        self._db = TosDb()
        self._tos_bridge = TosBridge()
        self._ecs_command_cache = EcsCommandCache()
        self._danger_warning_cache = DangerWarningCache()
        self._vehicle_cache = vehicle_cache
        self._event_cache = EventCache()
        self._node_name_prefix = NODE_NAME_PREFIX

    def IsCommandCompleted(self, request, context):
        vehicle_name = NameConverter().to_no(request.vehicle_name)
        response = antenna_pb2.IsCommandCompletedResponse()
        # response.is_completed = False
        response.is_completed = self._tos_bridge.is_command_completed(vehicle_name, request.tos_command_id)
        logger.debug(f'vehicle_name:{vehicle_name},is_completed:{response.is_completed}')
        return response

    def GetTosCommandStatus(self, request, context):
        response = antenna_pb2.GetTosCommandStatusResponse()
        vehicle_name = NameConverter().to_no(request.vehicle_name)
        logger.info(f"GetTosCommandStatus,vehicle_name:{vehicle_name},request: {MessageToString(request, as_one_line=True)}")
        if len(request.uuid) > 0:
            response.is_cancelled = self._tos_bridge.is_command_cancelled(request.uuid)
        if request.tos_command_id > 0:
            # response.is_finished = False #ToDo
            response.is_finished = self._tos_bridge.is_command_completed(vehicle_name, request.tos_command_id)
        response.is_reseted = False
        logger.info(f"GetTosCommandStatus,vehicle_name:{vehicle_name},response: {MessageToString(response, as_one_line=True)}")
        return response

    # def PushChassisInfo(self, request: antenna_pb2.PushChassisInfoRequest, context):
    #     return antenna_pb2.PushVehicleStatusResponse()
    '''
    def ArriveTosCommand(self, request, context):
        response = antenna_pb2.ArriveTosCommandResponse()
        vehicle_name = NameConverter().to_no(request.vehicle_name)
        logger.info(f'ArriveTosCommand request vehicle:{vehicle_name},request:{MessageToString(request, as_one_line=True)}')
        if len(request.commands_info) == 0:
            logger.warning(f"ArriveTosCommand:command_info size=0")
            response.ret = False
            return response
        response.ret = True
        for command in request.commands_info:
            if command.area != antenna_pb2.ArriveTosCommandRequest.DEFAULT_SIDE:
                arrive_area = antenna_pb2.ArriveTosCommandRequest.ArriveAreaType.Name(command.area)
                if not self._db.arrive_command(vehicle_name, command.tos_command_id, arrive_area):
                    response.ret = False
                    logger.warning(f"ArriveTosCommand:vehicle_name:{vehicle_name} fail to arrive "
                                 f"tos command id:{command.tos_command_id},arrive_area:{arrive_area}")
                    break
        logger.info(f"ArriveTosCommand response: {MessageToString(response, as_one_line=True)}")
        return response
    '''


    def GetStackerCommand(self, request, context):
        response = antenna_pb2.GetStackerCommandResponse()
        truck_no = NameConverter().to_no(request.vehicle_name)
        tos_id = request.tos_command_id
        #ToDo
        stacker_id = None
        response.command.stacker_id = str(stacker_id)
        if stacker_id is not None:
            response.command.type = antenna_pb2.StackerCommand.BIND
        else:
            logger.warning(f'''truck_no:{truck_no} can't find tos_id:{tos_id} stacker id''')
            response.command.type = antenna_pb2.StackerCommand.UNBIND
        return response

if __name__ == '__main__':
    request = antenna_pb2.PushTakeOverRecordRequest()
    request.vehicle_name = "howo3"
    for i in range(10):
        request.records.append(
            antenna_pb2.TakeOverRecord(
                detail=antenna_pb2.TakeOverRecord.SYSTEM_ERROR, timestamp_ms=1611903570500 + i))
    AntennaServer().PushTakeOverRecord(request, None)
