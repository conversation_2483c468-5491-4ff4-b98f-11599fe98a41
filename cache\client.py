import time

from common.singleton import Singleton
from redis_client.redis_client import RedisC<PERSON>, ShareRedisClient, LocalRedisClient, PlcShareRedisClient, PscpRedisClient


class LocalClient(LocalRedisClient, metaclass=Singleton):
    pass


class CacheClient(RedisClient, metaclass=Singleton):
    pass


class ShareCacheClient(ShareRedisClient, metaclass=Singleton):
    pass


class PlcShareCacheClient(PlcShareRedisClient, metaclass=Singleton):
    pass


class PscpRedisClient(PscpRedisClient, metaclass=Singleton):
    pass


if __name__ == '__main__':
    key = "test"
    CacheClient().client().set(key, 111)
    get = CacheClient().client().get(key)
    print(int(get))

    CacheClient().client().set(key, 111, ex=3)
    time.sleep(2)
    get = CacheClient().client().get(key)
    print(get)

    key = "test"
    CacheClient().client().set(key, 111)
    get = CacheClient().client().get(key)
    print(int(get))

    CacheClient().client().set(key, 111, ex=3)
    time.sleep(4)
    get = CacheClient().client().get(key)
    print(get)

    key = "test-value"
    CacheClient().set_value(key, -12.5)
    get = CacheClient().get_value(key, float)
    print(get)

    CacheClient().set_value(key, -12.5, ex=3)
    time.sleep(4)
    get = CacheClient().get_value(key, float)
    print(get)
