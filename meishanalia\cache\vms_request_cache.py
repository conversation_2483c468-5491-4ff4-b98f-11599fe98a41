import time
import json
from common.logger import logger
from common.singleton import Singleton
from proto import cache_pb2, antenna_pb2
from cache.client import Cache<PERSON>lient
from common.common_util import MyJosnEncoder

VMS_REQUEST_PREFIX = 'vms-request-queue'



class VmsRequestCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def push_request(self, vehicle_name, request_type, request_content):
        request = {}
        request['type'] = request_type
        request['timestamp'] = time.time()
        request['content'] = request_content
        request_value = json.dumps(request, cls=MyJosnEncoder)
        queue_name =f"{VMS_REQUEST_PREFIX}:{vehicle_name}"
        self._redis.lpush(queue_name, request_value)

    def pop_request(self, vehicle_name, timeout=None):
        queue_name =f"{VMS_REQUEST_PREFIX}:{vehicle_name}"
        if timeout is None:
            request = self._redis.rpop(queue_name)
        else:
            request = self._redis.brpop(queue_name, timeout = timeout)
        return request

    def clear_all_request(self, vehicle_name):
        queue_name =f"{VMS_REQUEST_PREFIX}:{vehicle_name}"
        while self._redis.llen(queue_name) > 0:
            CacheClient().ltrim(queue_name,1, 0)
  
    def pop_all_request(self, vehicle_name):
        queue_name =f"{VMS_REQUEST_PREFIX}:{vehicle_name}"
        request_list = []
        while self._redis.llen(queue_name) > 0:
            request_list = request_list + [self._redis.rpop(queue_name)]
        return request_list


if __name__ == '__main__':
    queue_name = VMS_REQUEST_QUEUE
    CacheClient().ltrim(queue_name,1, 0)
    logger.info(f'''count={CacheClient().llen(queue_name)}''')
    CacheClient().lpush(queue_name, '1')
    CacheClient().lpush(queue_name, '2')
    CacheClient().lpush(queue_name, '3')
    CacheClient().lpush(queue_name, '4')
    request_list = []
    while CacheClient().llen(queue_name) > 0:
        request_list = request_list + [CacheClient().brpop(queue_name,timeout = 2)[1]]
    logger.info(f'''request_list:{request_list}''')
    logger.info(f'''index:0-2,value:{CacheClient().lrange(queue_name, -2,-1)}''')
    CacheClient().ltrim(queue_name,0, 2)
    index = 0
    logger.info(f'''index:{index},value:{CacheClient().lindex(queue_name, index)}''')
    index = 1
    logger.info(f'''index:{index},value:{CacheClient().lindex(queue_name, index)}''')
    index = 2
    logger.info(f'''index:{index},value:{CacheClient().lindex(queue_name, index)}''')
    logger.info(f'''index:0-2,value:{CacheClient().lrange(queue_name, 0,3)}''')

    vehicle_name = 'AT800'
    VmsRequestCache().clear_all_request(vehicle_name)
    VmsRequestCache().push_request(vehicle_name, 'PATH', 'AT800')
    VmsRequestCache().push_request(vehicle_name, 'PATH', 'AT800')
    VmsRequestCache().push_request(vehicle_name, 'PATH', 'AT800')

    pop_all_request()