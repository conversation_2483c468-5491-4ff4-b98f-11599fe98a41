# 梅山车辆生产/测试模式切换功能说明

## 功能概述

梅山场景车辆生产/测试模式切换功能，实现了一台车只显示一个车号，通过修改"车辆模式"配置来区分生产和测试，解决了云控上一台车需要生产测试两个车号录入两次的遗留问题。

## 核心功能

### 1. 车辆操作模式管理
- **生产模式 (PROD)**: 车辆执行正常的港口作业指令
- **测试模式 (TEST)**: 车辆执行测试或模拟指令，不影响生产作业

### 2. 模式切换机制（参考甬舟逻辑优化）
- **查询当前指令**: 切换前先查询车辆当前执行的指令
- **TOS指令校验**: 生产模式切测试模式时，校验当前指令是否为生产TOS指令
- **智能取消指令**: 只取消非生产TOS指令，保护正在执行的生产作业
- **指令队列隔离**: 不同模式使用不同的指令队列，通过数据库过滤实现
- **状态同步**: 模式切换状态实时同步到所有相关组件

## 主要改变

### 1. 车辆名称管理改造

**修改文件**: `common/name_converter.py`

- **删除AT8xx测试车号**: 不再为测试车分配独立的AT8xx车号
- **统一车号**: 所有车辆统一使用AT5xx/AT6xx车号
- **保留fabutest**: 特殊测试车fabutest使用AT500车号

**修改前**:
```
howo1 -> AT501 (生产车)
howo1_test -> AT801 (测试车)
```

**修改后**:
```
howo1 -> AT501 (统一车号，通过模式区分)
fabutest -> AT500 (测试车)
```

### 2. TOS Bridge业务逻辑

**修改文件**: `tos_bridge.py`

- **添加模式切换处理**: 重写`on_vehicle_operation_mode_changed`方法，参考甬舟逻辑优化
- **指令查询改造**: 修改`_query_new_wi`方法支持操作模式参数
- **智能取消指令**: 查询当前指令信息，记录后取消执行
- **简化复制逻辑**: 不进行复杂的指令复制，通过数据库过滤获取测试指令

### 3. 数据库查询改造

**修改文件**: `tos_db.py`

- **参数兼容**: `query_new_wi`方法支持车辆状态字典参数
- **指令过滤**: 添加测试模式和生产模式的指令过滤方法
- **向后兼容**: 保持对旧接口的兼容性

## 车辆模式说明

### 生产模式 (PROD)
- **指令来源**: 码头TOS指令、云控指令
- **指令类型**: 生产作业指令、去往指令、收工指令
- **路径规划**: 单车路径规划

### 测试模式 (TEST)
- **指令来源**: 云控指令
- **指令类型**: 去往指令、模拟作业指令、收工指令
- **路径规划**: 单车路径规划
- **限制**: 指令接收开关不能操作

## 使用方法

### 1. 通过Web接口切换模式

```python
# 设置车辆为生产模式
request = web_service_pb2.UpdateVehicleModeRequest()
request.vehicle_name = "AT501"
request.operation_mode = antenna_pb2.VehicleOperationMode.PROD

# 设置车辆为测试模式
request.operation_mode = antenna_pb2.VehicleOperationMode.TEST
```

### 2. 通过命令行工具切换

```bash
python script/update_operation_mode.py --vehicle AT501 --mode PROD
python script/update_operation_mode.py --vehicle AT501 --mode TEST
```

### 3. 查询车辆模式

```python
# 获取车辆当前模式
operation_mode = vehicle_cache.get_vehicle_operation_mode("AT501")
mode_name = VehicleOperationMode.Name(operation_mode)
```

## 数据库影响

### 最小化数据库更改

本次实现采用了最小化数据库更改的策略：

1. **不修改表结构**: 不添加新的数据库字段
2. **利用现有字段**: 通过现有的 `REMARK1` 字段来区分指令类型
3. **缓存层实现**: 操作模式信息存储在Redis缓存中
4. **兼容性保证**: 保持与现有系统的兼容性

### 指令类型识别策略

梅山场景通过 `REMARK1` 字段来区分指令类型：

| 指令来源 | REMARK1字段值 | 指令类型 | 说明 |
|---------|--------------|---------|------|
| **云控下发** | `"FB:T*"` | 测试指令 | 云控手动下发的测试指令，如FB:T、FB:T0、FB:T1等 |
| **云控下发** | `"FB:S*"` | 模拟指令 | 云控手动下发的模拟指令，如FB:S、FB:S0、FB:S1等 |
| **TOS系统** | 其他值或空 | 生产指令 | Oracle TOS系统的正常生产指令 |

### REMARK1字段解析规则

参考项目中 `_parse_remark1` 的解析逻辑：

**格式**: `FB:XY`
- **FB**: 固定前缀
- **X**: 第一位字符 (`FB_CS`) - 指令来源标识
  - `T` = 测试指令 (TEST) - 测试模式下可执行
  - `S` = 模拟指令 (SIMULATION) - 测试模式下可执行
  - 其他 = 生产指令 (TOS) - 仅生产模式下可执行
- **Y**: 第二位字符 (`FB_DD`) - 其他控制标识

这种策略的优势：
- **灵活匹配**: 支持各种测试/模拟指令格式（FB:T、FB:T0、FB:S、FB:S0等）
- **向后兼容**: 不影响现有的TOS指令处理逻辑
- **与项目一致**: 使用与项目相同的解析逻辑
- **模式统一**: 测试模式下同时支持TEST和SIMULATION指令

### 指令过滤逻辑

```python
def filter_test_mode_wis(self, vehicle_name, wis):
    """测试模式：保留测试指令和模拟指令"""
    # 根据REMARK1字段过滤：参考项目中_parse_remark1的解析逻辑
    filtered_wis = []
    for wi in wis:
        remark1 = wi.get('REMARK1', '')
        if self._is_test_instruction(remark1):  # 检查FB_CS是否为'T'或'S'
            filtered_wis.append(wi)
    return filtered_wis

def filter_prod_mode_wis(self, vehicle_name, wis):
    """生产模式：只过滤测试/模拟的作业指令，非作业指令不限制"""
    # 生产模式过滤规则：
    # 1. 如果是测试/模拟指令且是作业指令，则过滤掉
    # 2. 如果是云控下发的收工/去往指令，则保留（即使带有FB:T*标识）
    # 3. 其他所有指令都保留（包括：生产作业指令、所有非作业指令）
    filtered_wis = []
    for wi in wis:
        remark1 = wi.get('REMARK1', '')
        is_test_instruction = self._is_test_instruction(remark1)  # 现在包含T和S
        is_work_instruction = self._is_work_instruction(wi)
        is_cloud_control_instruction = self._is_cloud_control_instruction(wi)

        if is_test_instruction and is_work_instruction:
            # 过滤掉测试/模拟的作业指令
            continue
        elif is_cloud_control_instruction:
            # 保留云控下发的收工/去往指令
            filtered_wis.append(wi)
        else:
            # 保留所有其他指令
            filtered_wis.append(wi)
    return filtered_wis

def _is_test_instruction(self, remark1):
    """判断是否为测试/模拟指令，参考项目中_parse_remark1的解析逻辑"""
    import re
    if not remark1:
        return False

    # 使用与项目一致的正则表达式解析REMARK1
    match = re.match(r'FB:(\S*)', remark1)
    if match is not None and len(match.group(1)) > 0:
        # 获取第一位字符作为指令来源标识
        command_source = match.group(1)[0]
        # T表示测试指令，S表示模拟指令，两者都可以在测试模式下执行
        return command_source in ['T', 'S']

    return False

def _is_cloud_control_instruction(self, wi):
    """判断是否为云控下发的收工/去往指令"""
    wi_type = wi.get('WI_TYPE', '')
    wi_act = wi.get('WI_ACT', '')
    remark1 = wi.get('REMARK1', '')

    # 检查是否为收工指令：WI_TYPE为LOAD/DSCH且WI_ACT为STOP_WORK
    is_stop_work = (wi_type in ['LOAD', 'DSCH'] and wi_act == 'STOP_WORK')

    # 检查是否为去往指令：WI_TYPE为GOTO且WI_ACT为MOVE
    is_goto = (wi_type == 'GOTO' and wi_act == 'MOVE')

    # 检查是否带有云控标识（FB:T*）
    is_cloud_marked = self._is_test_instruction(remark1)

    # 只有同时满足：是收工/去往指令 且 带有云控标识，才认为是云控指令
    return (is_stop_work or is_goto) and is_cloud_marked

def _is_work_instruction(self, wi):
    """判断是否为作业指令（装船、卸船、移箱等）"""
    wi_type = wi.get('WI_TYPE', '')
    wi_act = wi.get('WI_ACT', '')

    # 作业指令的判断逻辑：
    # 1. 装船作业：WI_TYPE='LOAD' and WI_ACT='UNLOAD'
    # 2. 卸船作业：WI_TYPE='DSCH' and WI_ACT='LOAD'
    # 3. 移箱作业：WI_TYPE='YARD' 或其他LOAD/DSCH组合

    if wi_type in ['LOAD', 'DSCH', 'YARD']:
        # 排除收工指令
        if wi_act in ['STOP_WORK', 'LEAVE_SPACE', 'STOP']:
            return False
        # 排除充电和换班指令
        if wi_act in ['CHARGE', 'CHANGE']:
            return False
        # 其他LOAD/DSCH/YARD类型的指令都认为是作业指令
        return True

    # 其他类型（如GOTO等）不是作业指令
    return False
```

## 测试验证

运行测试脚本验证功能：

```bash
python script/test_meishan_vehicle_mode.py
```

## 问题修复记录

### 生产模式指令接收问题

**问题描述**：
在测试模式切换到生产模式后，车辆收不到一些指令，如去往指令、收工指令等。

**原因分析**：
原始的生产模式过滤逻辑过于严格，会过滤掉所有带有测试标识的指令，包括非作业指令。

**解决方案**：
修改生产模式的过滤逻辑，采用更精细的过滤策略：

```python
# 修改前：过滤所有测试指令
if not self._is_test_instruction(remark1):
    filtered_wis.append(wi)

# 修改后：只过滤测试的作业指令
if is_test_instruction and is_work_instruction:
    continue  # 过滤掉测试的作业指令
else:
    filtered_wis.append(wi)  # 保留所有其他指令
```

**指令分类**：
- **作业指令**：装船(`LOAD`+`UNLOAD`)、卸船(`DSCH`+`LOAD`)、移箱(`YARD`等)
- **非作业指令**：去往(`GOTO`+`MOVE`)、收工(`STOP_WORK`/`LEAVE_SPACE`)、充电(`CHARGE`)等

**修改效果**：
- ✅ 生产模式：接收生产作业指令 + 所有非作业指令
- ✅ 测试模式：接收测试作业指令 + 测试非作业指令
- ❌ 生产模式：过滤测试作业指令

### 生产TOS指令保护机制

**问题描述**：
生产模式切换到测试模式时，如果车辆正在执行生产TOS指令（真实港口作业），不应该被取消。

**解决方案**：
添加TOS指令校验机制，在模式切换时检查当前指令类型：

```python
def _is_production_tos_command(self, command_context):
    """判断是否为生产TOS指令"""
    # 1. 检查指令来源
    if command_context.remote_command_source == RemoteCommand.CommandContext.RemoteCommandSource.TOS:
        return True  # TOS来源的指令是生产指令
    elif command_context.remote_command_source in (TEST, SIMULATION):
        return False  # 测试/仿真指令不是生产指令

    # 2. 检查是否为作业指令（保守策略）
    if (command_context.wi_type in [WI_LOAD, WI_DSCH, WI_YARD] and
        command_context.action_type in [LOAD, UNLOAD]):
        return True  # 默认认为作业指令是生产指令
```

**保护策略**：
- 🛡️ **生产TOS指令**：不允许取消，阻止模式切换
- ✅ **测试指令**：允许取消，正常切换
- ✅ **非TOS指令**：允许取消，正常切换
- ⚠️ **未知指令**：默认保护（保守策略）

## 注意事项

1. **云控配合**: 需要云控项目配合修改车辆录入界面和模式切换界面
2. **指令区分**: 需要根据实际业务需求完善指令类型的区分逻辑
3. **监控日志**: 建议增加模式切换的详细日志监控
4. **渐进部署**: 建议先在测试环境验证，再逐步部署到生产环境

## 与甬舟场景的差异

| 功能模块 | 甬舟场景 | 梅山场景 | 差异说明 |
|---------|---------|---------|---------|
| **车辆名称** | 双车号系统 | 单车号系统 | 梅山统一车号，甬舟保留双车号 |
| **指令复制** | ✅ 完整实现 | ⚠️ 简化实现 | 甬舟有复杂的指令复制逻辑 |
| **数据库过滤** | ✅ TOS_SOURCE字段 | ⚠️ 多策略过滤 | 甬舟在数据库层真正过滤 |
| **路径规划** | 强控/单车切换 | 统一单车模式 | 甬舟更复杂的路径规划 |
| **锁站交互** | ✅ 支持 | ❌ 不涉及 | 梅山场景不需要锁站交互 |
| **车辆状态** | 复杂状态管理 | 基础状态管理 | 甬舟有更详细的状态流转 |

### 梅山场景的功能设计

1. **双指令队列**: 梅山功能设计上，生产模式和测试模式会有两个指令队列，类似甬舟的实现
2. **指令筛选机制**: 通过车辆操作模式去筛选指令，确保不同模式接收对应的指令
3. **Oracle TOS系统**: 梅山使用Oracle数据库，需要根据实际字段实现指令类型判断
4. **指令解析统一**: 指令解析在梅山没有差异，生产和测试指令的解析逻辑相同
5. **模式切换策略**:
   - **生产模式**: 筛选并接收生产指令
   - **测试模式**: 筛选并接收测试指令

## 后续扩展

1. **指令来源标识**: 可以在数据库中添加指令来源标识字段
2. **更细粒度控制**: 可以根据具体业务需求添加更细粒度的模式控制
3. **监控面板**: 可以在Web界面添加车辆模式监控面板
4. **自动切换**: 可以根据特定条件自动切换车辆模式
