# Server
import selectors
import socket
import types
import struct
import traceback
import time
from common.logger import logger
from common.periodical_task import PeriodicalTask


class PersistentTcpServer():
    def __init__(self,bind_ip = '',bind_port = 0,unpack_format=''):
        self._sel = None
        self._bind_ip = bind_ip
        self._bind_port = bind_port
        self._loop_thread = None
        self._unpack_format = unpack_format
        self._buffer_size = 1024
        self._conn = []
 
    def start(self):
        self.register()
        self._loop_thread = PeriodicalTask(target=self.loop, interval=0)
        self._loop_thread.start()

 
    def register(self):
        conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        conn.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        conn.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 10)  # 10S内没有数据则开始探测
        conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)  # 探测间隔
        conn.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)  # 最大重试次数
        conn.bind((self._bind_ip, self._bind_port))
        conn.listen()
        conn.setblocking(False)
        self._sel = selectors.DefaultSelector()
        self._sel.register(conn, selectors.EVENT_READ, data=None)
 
    def listen(self):
        while True:
            events = self._sel.select(timeout=None)
            for key, mask in events:
                if key.data is None:
                    self.accept(key.fileobj)
                else:
                    self.process(key, mask)

    def loop(self):
        try:
            events = self._sel.select(timeout=None)
            for key, mask in events:
                if key.data is None:
                    self.accept(key.fileobj)
                else:
                    self.process(key, mask)
        except Exception as e:
            logger.warning(f"cranes loop error: {e}, trace:{traceback.format_exc()}")

    def accept(self, sock):
        conn, addr = sock.accept()
        self._conn.append(conn)
        conn.setblocking(False)
        data = types.SimpleNamespace(addr=addr, buffer=b'')
        events = selectors.EVENT_READ
        self._sel.register(conn, events, data=data)


    def process(self, key, mask):
        sock = key.fileobj
        data = key.data
        if mask & selectors.EVENT_READ:
            try:
                bytesbuffer = sock.recv(self._buffer_size)
                if bytesbuffer:
                    if self._unpack_format:
                        receive_content = struct.unpack(self._unpack_format, bytesbuffer)
                        logger.debug(f"process:receive addr {data.addr},len:{len(bytesbuffer)}\n"
                                    f"process:receive_content:{receive_content}")
                else:
                    logger.warning('closing connection to ' + str(data.addr))
                    self._sel.unregister(sock)
                    sock.close()
            except ConnectionResetError as e:
                logger.warning('closing connection to' + str(data.addr))
                self._sel.unregister(sock)
                sock.close()
 
 
if __name__ == '__main__':
    server = PersistentTcpServer(bind_ip = '',bind_port = 3000,unpack_format='!HHHHHHHHHH')
    server.start()
