db:
  oracle:
    mode: remote
    username: fabutech
    password: fabu1#%
    ip: ***********
    port: 1521
    sid: wrjk

  mysql:
    data_mysql:
      dsn: mysql://root:fabu124@***********:3306/nb_port_fat?charset=utf8
    chassis_mysql:
      dsn: mysql://root:fabu124@***********:3306/nb_port_fat_chassis?charset=utf8

  redis:
    mode: cluster
    sentinel:
      master: antenna_server
      nodes:
        - ************:15000
        - ***********1:15000
        - ************:15000
        - ************:15000
        - ************:15000
    # 0(pro)、8(pre),15(fat),iecs(6)
    database: 15
    # share_database生产预发用0,其他模式下与database保持一致
    share_database: 15
    # plc_share_database 在portman及DEV模式下与database保持一致,在生产(pro)、预发(pre)、测试(FAT)模式下用8
    plc_share_database: 8
    password: fabu123!@#

  kafka:
    producer_enable: False
    consumer_enable: False
    group_id: FABU_FMS_FAT
    nodes:
      - ************:9093

service:
  scheduler_server:
    ip: ************
    port: 46170

server_attribute:
  #如果是非分布式为默认为Node1-1;分布式主节点为'Node1-1';其他节点格式为'Node[major_num]-[minor_num]',如Node1-2
  #1.cluster_[]:集群,可以与其他多节点同时运行tos_bridge
  #  (1):cluster_master:集群主节点,除了启动tos_bridge,同时启动其他服务
  #  (2):cluster_slave:集群子节点,只启动tos_bridge服务
  #  (3):cluster_back:备节点,当cluster_master所在服务主机挂掉时，所有服务会被cluster_back接管
  #2.single:只单机运行tos_bridge(注意redis的database其他服务没有在用),同时运行其他服务
  nodes:
    -
      node_name: Node1-1
      mode: single

modules:
  timing_record:
    start: false
  vehicle_info:
    start: true
    publisher: true

keepalive_services:
  nb-ecs:
    enable: True
    acceptable_delay_ms: 1000
  ship-load-sch:
    enable: True
    acceptable_delay_ms: 30000