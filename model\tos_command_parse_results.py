import datetime
import time
import traceback

from sqlalchemy import Column, String, TIMESTAMP, Integer
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession
from proto.antenna_pb2 import RemoteCommand
from config.config_manage import ConfigManage

Base = declarative_base()


class TosCommandParseResult(Base):
    __tablename__ = 'tos_command_parse_results'
    id = Column('id', Integer, primary_key=True, autoincrement=True)
    vehicle_name = Column('vehicle_name', String)
    tos_wi_id = Column('tos_wi_id', Integer)
    tos_wi_no = Column('tos_wi_no', Integer)
    tos_wi_version = Column('tos_wi_version', Integer)
    sub_command_uuid = Column('sub_command_uuid', String)
    sub_command_type = Column('sub_command_type', String)
    sub_command_detail = Column('sub_command_detail', String)
    timestamp_ms = Column('timestamp_ms', Integer)
    create_time = Column(TIMESTAMP)

def is_support():
    if ConfigManage().is_iecs_scene():
        return False
    else:
        return True

def insert(vehicle_name, tos_wi_id, tos_wi_no, tos_wi_version, sub_command_uuid, sub_command_type, sub_command_detail, insert_time = 0):
    if not is_support():
        return
    start = time.time()
    session = MysqlSession().acquire()
    try:
        result = TosCommandParseResult(vehicle_name=vehicle_name,
                                       tos_wi_id=int(tos_wi_id),
                                       tos_wi_no=int(tos_wi_no),
                                       tos_wi_version=int(tos_wi_version),
                                       sub_command_uuid=sub_command_uuid,
                                       sub_command_type=RemoteCommand.CommandType.Name(sub_command_type),
                                       sub_command_detail=sub_command_detail,
                                       timestamp_ms=int(time.time() * 1000),
                                       create_time=datetime.datetime.now()
                                       )
        #logger.info(f"Insert TosCommandParseResult :{result.__dict__}")
        session.add(result)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert TosCommandParseResult err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()
    end = time.time()
    logger.debug(f"Insert TosCommandParseResult :vehicle_name:{vehicle_name},tos_wi_id:{tos_wi_id},cost time:{(end - start)},execute duration:{end-insert_time}")
    if (end - start) > 1.0 or (end - insert_time) > 1.0:
        logger.warning(f"TosCommandParseResult insert over cost execeed round time:{round(end - start)}, execeed time:{end - start},"
                     f"execute duration round time:{round(end - insert_time)},execute duration:{end-insert_time}")
    return

def async_insert(async_handler, vehicle_name, tos_wi_id, tos_wi_no, tos_wi_version, sub_command_uuid, sub_command_type, sub_command_detail):
    if not is_support():
        return
    logger.debug(f"Insert TosCommandParseResult :vehicle_name:{vehicle_name},tos_wi_id:{tos_wi_id}")
    async_handler.submit(insert, vehicle_name, tos_wi_id, tos_wi_no, tos_wi_version, sub_command_uuid, sub_command_type, sub_command_detail, time.time())