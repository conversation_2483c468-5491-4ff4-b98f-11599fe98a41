from typing import List, Dict
import traceback
import datetime
import sqlalchemy
from sqlalchemy import Column, Integer, String, DateTime, inspect
from common.logger import logger
from meishanalia.common.constant import TABLE_CRANE_INFO, TABLE_CRANE_INFO_DEBUG
from meishanalia.communication.db import TosSession
from meishanalia.model.table_info import CraneInfo


def get_by_crane_no(crane_no, debug=False):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        info = session.query(CraneInfo).filter(CraneInfo.CRANE_ID == crane_no).order_by(sqlalchemy.desc(CraneInfo.UPDATE_TIME)).first()
        return info.to_dict() if info is not None else None
    except Exception as e:
        logger.warning(f"get_by_crane_no err:{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()


def batch_get_by_crane_no(crane_nos: List[int], debug: bool):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        crane_ids = []
        results = []
        for id in crane_nos:
            crane_ids.append(f"CR{id}")
        cranes = session.query(CraneInfo).filter(CraneInfo.CRANE_ID.in_(crane_ids)).all()
        for crane in cranes:
            results.append(crane.to_dict())
        return results
    except Exception as e:
        logger.warning(f"batch_get_by_crane_no err:{e}, trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()


def get_all_crane(debug=False):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    results = []
    try:
        infos = session.query(CraneInfo).all()
        for info in infos:
            results.append(info.to_dict())
        return results
    except Exception as e:
        logger.warning(f"get_all_crane err:{e}, trace:{traceback.format_exc()}")
        return results
    finally:
        session.close()


def insert(record: CraneInfo, update=True, debug=False):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        cur = session.query(CraneInfo).filter(CraneInfo.CRANE_ID == record.CRANE_ID).first()
        if cur is None or not update:
            logger.info(f"Insert CraneInfo table:{record.__dict__}")
            record.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            record.UPDATE_TIME = record.INSERT_TIME
            session.add(record)
        else:
            record.UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            logger.info(f"Update CraneInfo table:{record.__dict__}")
            #session.query(CraneInfo).filter(CraneInfo.CRANE_ID == record.CRANE_ID).update(record.__dict__)
            for key in record.__dict__:
                if key == '_sa_instance_state':
                    continue
                setattr(cur, key, getattr(record, key))
        session.commit()
    except Exception as e:
        logger.warning(f"Insert CraneInfo table err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()


if __name__ == '__main__':
    infos = get_all_crane(True)
    for k in infos:
        print(k.__dict__)
    print(len(infos))
    print(CraneInfo.__table__.name)

    infos = get_all_crane(False)
    for k in infos:
        print(k.__dict__)
    print(len(infos))
    print(CraneInfo.__table__.name)


    infos = get_by_crane_no('CR44')
    print(infos.__dict__)
    print(CraneInfo.__table__.name)

    infos = get_by_crane_no('CR44',True)
    print(infos.to_dict())
    print(CraneInfo.__table__.name)
