config_area {
  critical_area_from {
    utm_x: 404123.5906182256
    utm_y: 3294858.972550558
  }
  critical_area_to {
    utm_x: 404147.60042389354
    utm_y: 3294881.6116590165
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404136.84
        utm_y: 3294837.8
      }
      to_point {
        utm_x: 404147.60042389354
        utm_y: 3294881.6116590165
      }
    }
  }
  description: "62"
}
config_area {
  critical_area_from {
    utm_x: 404115.8726940107
    utm_y: 3294867.039686199
  }
  critical_area_to {
    utm_x: 404139.19108205696
    utm_y: 3294890.390334561
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404136.84
        utm_y: 3294837.8
      }
      to_point {
        utm_x: 404139.19108205696
        utm_y: 3294890.390334561
      }
    }
  }
  description: "63"
}
config_area {
  critical_area_from {
    utm_x: 404079.36744771036
    utm_y: 3294895.761225365
  }
  critical_area_to {
    utm_x: 404102.9563040167
    utm_y: 3294918.838613826
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404090.49222779344
        utm_y: 3294875.9035957814
      }
      to_point {
        utm_x: 404102.9563040167
        utm_y: 3294918.838613826
      }
    }
  }
  description: "64"
}
config_area {
  critical_area_from {
    utm_x: 404066.83089709136
    utm_y: 3294899.4145529815
  }
  critical_area_to {
    utm_x: 404089.70057901985
    utm_y: 3294923.2048377316
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404090.49222779344
        utm_y: 3294875.9035957814
      }
      to_point {
        utm_x: 404089.70057901985
        utm_y: 3294923.2048377316
      }
    }
  }
  description: "65"
}
config_area {
  critical_area_from {
    utm_x: 404029.82492957875
    utm_y: 3294928.4758998626
  }
  critical_area_to {
    utm_x: 404053.3210928117
    utm_y: 3294951.6476566656
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404044.14445558685
        utm_y: 3294914.007191563
      }
      to_point {
        utm_x: 404053.3210928117
        utm_y: 3294951.6476566656
      }
    }
  }
  description: "66"
}
config_area {
  critical_area_from {
    utm_x: 404021.11347250285
    utm_y: 3294937.704890272
  }
  critical_area_to {
    utm_x: 404044.97528719367
    utm_y: 3294960.4999291985
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404044.14445558685
        utm_y: 3294914.007191563
      }
      to_point {
        utm_x: 404044.97528719367
        utm_y: 3294960.4999291985
      }
    }
  }
  description: "67"
}
config_area {
  critical_area_from {
    utm_x: 403982.24849375436
    utm_y: 3294968.698922232
  }
  critical_area_to {
    utm_x: 404006.09849281295
    utm_y: 3294991.506323331
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 403997.7966833802
        utm_y: 3294952.1107873446
      }
      to_point {
        utm_x: 404006.09849281295
        utm_y: 3294991.506323331
      }
    }
  }
  description: "68"
}
config_area {
  critical_area_from {
    utm_x: 403973.7986588027
    utm_y: 3294977.370888638
  }
  critical_area_to {
    utm_x: 403997.57630413264
    utm_y: 3295000.253711509
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 403997.7966833802
        utm_y: 3294952.1107873446
      }
      to_point {
        utm_x: 403997.57630413264
        utm_y: 3295000.253711509
      }
    }
  }
  description: "69"
}
config_area {
  critical_area_from {
    utm_x: 403937.383283412
    utm_y: 3295006.097983218
  }
  critical_area_to {
    utm_x: 403961.28029391525
    utm_y: 3295028.85612218
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 403951.4489111736
        utm_y: 3294990.2143831262
      }
      to_point {
        utm_x: 403961.28029391525
        utm_y: 3295028.85612218
      }
    }
  }
  description: "6A"
}
config_area {
  critical_area_from {
    utm_x: 403929.215598231
    utm_y: 3295014.533567433
  }
  critical_area_to {
    utm_x: 403952.8823066783
    utm_y: 3295037.5311088506
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 403951.4489111736
        utm_y: 3294990.2143831262
      }
      to_point {
        utm_x: 403952.8823066783
        utm_y: 3295037.5311088506
      }
    }
  }
  description: "6B"
}
config_area {
  critical_area_from {
    utm_x: 403897.16970429645
    utm_y: 3295047.915215383
  }
  critical_area_to {
    utm_x: 403921.07569201157
    utm_y: 3295070.6639241674
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 403905.10113896703
        utm_y: 3295028.317978908
      }
      to_point {
        utm_x: 403921.07569201157
        utm_y: 3295070.6639241674
      }
    }
  }
  description: "6C"
}
config_area {
  critical_area_from {
    utm_x: 404362.586393344
    utm_y: 3295087.578878862
  }
  critical_area_to {
    utm_x: 404386.4876218479
    utm_y: 3295110.332587922
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404382.84
        utm_y: 3295073.39
      }
      to_point {
        utm_x: 404386.4876218479
        utm_y: 3295110.332587922
      }
    }
  }
  description: "72"
}
config_area {
  critical_area_from {
    utm_x: 404354.49331675755
    utm_y: 3295096.0116387657
  }
  critical_area_to {
    utm_x: 404378.10581434437
    utm_y: 3295119.0648371927
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404382.84
        utm_y: 3295073.39
      }
      to_point {
        utm_x: 404378.10581434437
        utm_y: 3295119.0648371927
      }
    }
  }
  description: "73"
}
config_area {
  critical_area_from {
    utm_x: 404322.6687989952
    utm_y: 3295129.3501871875
  }
  critical_area_to {
    utm_x: 404346.47904135403
    utm_y: 3295152.1990899798
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404341.3432998235
        utm_y: 3295116.7261728174
      }
      to_point {
        utm_x: 404346.47904135403
        utm_y: 3295152.1990899798
      }
    }
  }
  description: "74"
}
config_area {
  critical_area_from {
    utm_x: 404314.22666634456
    utm_y: 3295138.1178017124
  }
  critical_area_to {
    utm_x: 404338.03093213
    utm_y: 3295160.972930905
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404341.3432998235
        utm_y: 3295116.7261728174
      }
      to_point {
        utm_x: 404338.03093213
        utm_y: 3295160.972930905
      }
    }
  }
  description: "75"
}
config_area {
  critical_area_from {
    utm_x: 404282.7276153338
    utm_y: 3295170.875744184
  }
  critical_area_to {
    utm_x: 404306.3961239115
    utm_y: 3295193.8714329475
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404299.84659964696
        utm_y: 3295160.062345635
      }
      to_point {
        utm_x: 404306.3961239115
        utm_y: 3295193.8714329475
      }
    }
  }
  description: "76"
}
config_area {
  critical_area_from {
    utm_x: 404274.1063702734
    utm_y: 3295179.9211450224
  }
  critical_area_to {
    utm_x: 404297.9253381189
    utm_y: 3295202.7609517374
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404299.84659964696
        utm_y: 3295160.062345635
      }
      to_point {
        utm_x: 404297.9253381189
        utm_y: 3295202.7609517374
      }
    }
  }
  description: "77"
}
config_area {
  critical_area_from {
    utm_x: 404239.84897178755
    utm_y: 3295215.6290713875
  }
  critical_area_to {
    utm_x: 404263.78080977633
    utm_y: 3295238.350583892
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404258.34989947046
        utm_y: 3295203.3985184524
      }
      to_point {
        utm_x: 404263.78080977633
        utm_y: 3295238.350583892
      }
    }
  }
  description: "78"
}
config_area {
  critical_area_from {
    utm_x: 404259.9291269508
    utm_y: 3295251.631101646
  }
  critical_area_to {
    utm_x: 404283.8121720434
    utm_y: 3295274.4038958773
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404258.34989947046
        utm_y: 3295203.3985184524
      }
      to_point {
        utm_x: 404283.8121720434
        utm_y: 3295274.4038958773
      }
    }
  }
  description: "79"
}
config_area {
  critical_area_from {
    utm_x: 404199.92017350026
    utm_y: 3295257.462185286
  }
  critical_area_to {
    utm_x: 404223.77946634084
    utm_y: 3295280.2598637897
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404216.8531992939
        utm_y: 3295246.7346912697
      }
      to_point {
        utm_x: 404223.77946634084
        utm_y: 3295280.2598637897
      }
    }
  }
  description: "7A"
}
config_area {
  critical_area_from {
    utm_x: 404191.8853031896
    utm_y: 3295266.277185005
  }
  critical_area_to {
    utm_x: 404215.6778252745
    utm_y: 3295289.1445393087
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404216.8531992939
        utm_y: 3295246.7346912697
      }
      to_point {
        utm_x: 404215.6778252745
        utm_y: 3295289.1445393087
      }
    }
  }
  description: "7B"
}
config_area {
  critical_area_from {
    utm_x: 404160.32808669144
    utm_y: 3295299.4935532506
  }
  critical_area_to {
    utm_x: 404184.1062198971
    utm_y: 3295322.3758691573
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404175.3564991174
        utm_y: 3295290.070864087
      }
      to_point {
        utm_x: 404184.1062198971
        utm_y: 3295322.3758691573
      }
    }
  }
  description: "7C"
}
config_area {
  critical_area_from {
    utm_x: 404620.8815895833
    utm_y: 3295334.5232909913
  }
  critical_area_to {
    utm_x: 404644.6708345338
    utm_y: 3295357.3940545265
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404640.15
        utm_y: 3295320.55
      }
      to_point {
        utm_x: 404644.6708345338
        utm_y: 3295357.3940545265
      }
    }
  }
  description: "82"
}
config_area {
  critical_area_from {
    utm_x: 404612.0983040681
    utm_y: 3295343.668944907
  }
  critical_area_to {
    utm_x: 404635.98355154606
    utm_y: 3295366.4394291556
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404640.15
        utm_y: 3295320.55
      }
      to_point {
        utm_x: 404635.98355154606
        utm_y: 3295366.4394291556
      }
    }
  }
  description: "83"
}
config_area {
  critical_area_from {
    utm_x: 404580.7518590213
    utm_y: 3295376.4532336504
  }
  critical_area_to {
    utm_x: 404604.5669113875
    utm_y: 3295399.2971230038
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404598.6579499221
        utm_y: 3295363.890625057
      }
      to_point {
        utm_x: 404604.5669113875
        utm_y: 3295399.2971230038
      }
    }
  }
  description: "84"
}
config_area {
  critical_area_from {
    utm_x: 404572.44640702184
    utm_y: 3295385.173072485
  }
  critical_area_to {
    utm_x: 404596.19674881117
    utm_y: 3295408.084232754
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404598.6579499221
        utm_y: 3295363.890625057
      }
      to_point {
        utm_x: 404596.19674881117
        utm_y: 3295408.084232754
      }
    }
  }
  description: "85"
}
config_area {
  critical_area_from {
    utm_x: 404541.09847293067
    utm_y: 3295418.0861313553
  }
  critical_area_to {
    utm_x: 404564.25532846944
    utm_y: 3295441.596980799
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404557.1658998441
        utm_y: 3295407.231250114
      }
      to_point {
        utm_x: 404564.25532846944
        utm_y: 3295441.596980799
      }
    }
  }
  description: "86"
}
config_area {
  critical_area_from {
    utm_x: 404532.3936953016
    utm_y: 3295426.867366653
  }
  critical_area_to {
    utm_x: 404556.0219950527
    utm_y: 3295449.90436843
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404557.1658998441
        utm_y: 3295407.231250114
      }
      to_point {
        utm_x: 404556.0219950527
        utm_y: 3295449.90436843
      }
    }
  }
  description: "87"
}
config_area {
  critical_area_from {
    utm_x: 404498.06377977843
    utm_y: 3295463.055062155
  }
  critical_area_to {
    utm_x: 404522.16912196914
    utm_y: 3295485.592419539
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404515.67384976614
        utm_y: 3295450.571875171
      }
      to_point {
        utm_x: 404522.16912196914
        utm_y: 3295485.592419539
      }
    }
  }
  description: "88"
}
config_area {
  critical_area_from {
    utm_x: 404495.8455162184
    utm_y: 3295476.7485153945
  }
  critical_area_to {
    utm_x: 404519.6747405832
    utm_y: 3295499.577621075
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404515.67384976614
        utm_y: 3295450.571875171
      }
      to_point {
        utm_x: 404519.6747405832
        utm_y: 3295499.577621075
      }
    }
  }
  description: "89"
}
config_area {
  critical_area_from {
    utm_x: 404457.60578248126
    utm_y: 3295503.894444148
  }
  critical_area_to {
    utm_x: 404481.44296666095
    utm_y: 3295526.715238402
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404474.18179968814
        utm_y: 3295493.912500228
      }
      to_point {
        utm_x: 404481.44296666095
        utm_y: 3295526.715238402
      }
    }
  }
  description: "8A"
}
config_area {
  critical_area_from {
    utm_x: 404878.00536293257
    utm_y: 3295580.614485585
  }
  critical_area_to {
    utm_x: 404901.6622167852
    utm_y: 3295603.6221639947
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404898.84
        utm_y: 3295568.79
      }
      to_point {
        utm_x: 404901.6622167852
        utm_y: 3295603.6221639947
      }
    }
  }
  description: "J2"
}
config_area {
  critical_area_from {
    utm_x: 404869.81188561715
    utm_y: 3295589.1150390464
  }
  critical_area_to {
    utm_x: 404893.38393999706
    utm_y: 3295612.2095893193
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404898.84
        utm_y: 3295568.79
      }
      to_point {
        utm_x: 404893.38393999706
        utm_y: 3295612.2095893193
      }
    }
  }
  description: "J3"
}
config_area {
  critical_area_from {
    utm_x: 404837.9110983145
    utm_y: 3295622.70539763
  }
  critical_area_to {
    utm_x: 404861.7924904365
    utm_y: 3295645.479925294
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404857.3408132629
        utm_y: 3295612.1237916662
      }
      to_point {
        utm_x: 404861.7924904365
        utm_y: 3295645.479925294
      }
    }
  }
  description: "94"
}
config_area {
  critical_area_from {
    utm_x: 404829.54065407085
    utm_y: 3295631.4562539165
  }
  critical_area_to {
    utm_x: 404853.37138651893
    utm_y: 3295654.2837853422
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404857.3408132629
        utm_y: 3295612.1237916662
      }
      to_point {
        utm_x: 404853.37138651893
        utm_y: 3295654.2837853422
      }
    }
  }
  description: "95"
}
config_area {
  critical_area_from {
    utm_x: 404797.6300646639
    utm_y: 3295664.695393036
  }
  critical_area_to {
    utm_x: 404821.7453309426
    utm_y: 3295687.2221312
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404815.8416265258
        utm_y: 3295655.457583332
      }
      to_point {
        utm_x: 404821.7453309426
        utm_y: 3295687.2221312
      }
    }
  }
  description: "96"
}
config_area {
  critical_area_from {
    utm_x: 404789.304696904
    utm_y: 3295673.2439679992
  }
  critical_area_to {
    utm_x: 404813.0168138517
    utm_y: 3295696.194687157
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404815.8416265258
        utm_y: 3295655.457583332
      }
      to_point {
        utm_x: 404813.0168138517
        utm_y: 3295696.194687157
      }
    }
  }
  description: "97"
}
config_area {
  critical_area_from {
    utm_x: 404754.72875787574
    utm_y: 3295709.549040615
  }
  critical_area_to {
    utm_x: 404779.0001272098
    utm_y: 3295731.9074983136
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404774.3424397886
        utm_y: 3295698.791374998
      }
      to_point {
        utm_x: 404779.0001272098
        utm_y: 3295731.9074983136
      }
    }
  }
  description: "98"
}
config_area {
  critical_area_from {
    utm_x: 404787.1912776888
    utm_y: 3295756.3175697606
  }
  critical_area_to {
    utm_x: 404810.9452726384
    utm_y: 3295779.224942459
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404774.3424397886
        utm_y: 3295698.791374998
      }
      to_point {
        utm_x: 404810.9452726384
        utm_y: 3295779.224942459
      }
    }
  }
  description: "99"
}
config_area {
  critical_area_from {
    utm_x: 404715.4620038321
    utm_y: 3295750.790926212
  }
  critical_area_to {
    utm_x: 404739.3375861654
    utm_y: 3295773.5715444707
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404732.8432530515
        utm_y: 3295742.1251666644
      }
      to_point {
        utm_x: 404739.3375861654
        utm_y: 3295773.5715444707
      }
    }
  }
  description: "9A"
}
config_area {
  critical_area_from {
    utm_x: 404667.04110812757
    utm_y: 3295801.008889172
  }
  critical_area_to {
    utm_x: 404690.500681814
    utm_y: 3295824.2176892804
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: GANTRY_BLOCK
    block_turn {
      from_point {
        utm_x: 404691.3440663144
        utm_y: 3295785.45895833
      }
      to_point {
        utm_x: 404690.500681814
        utm_y: 3295824.2176892804
      }
    }
  }
  description: "JD"
}
approximate_point_offset_from: 0.0
approximate_point_offset_to: 0.0
approximate_point_theta: 0.0
