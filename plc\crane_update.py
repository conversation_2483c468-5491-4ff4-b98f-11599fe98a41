import time
import traceback
import copy

from google.protobuf.text_format import MessageToString
from common.logger import logger
from proto.antenna_pb2 import RemoteCommand, LightColor
from proto.fabupilot.canbus.chassis_pb2 import Chassis
from common.periodical_task import PeriodicalTask
from cache import vehicle_cache,plc_cache
from cache.command_cache import CommandCache
from common.name_converter import NameConverter
from plc.model import STORE_CRANE_LIST, make_crane_store_plc
from config.config_manage import ConfigManage



class UpDateCraneStorePlc(object):
    def __init__(self):
        self._vehicle_cache = None
        self._command_cache = None
        self._plc_cache = None
        self._loop_thread = None
        self._crane_list = STORE_CRANE_LIST
        self._via_finish_crane_nos = dict()
        self._msg_no = 0
        self._update_env = ''

    def start(self):
        self._update_env = ConfigManage().get_env()
        self._vehicle_cache = vehicle_cache
        self._plc_cache = plc_cache
        self._command_cache = CommandCache()
        self._loop_thread = PeriodicalTask(target=self.loop, interval=1, master_only=True)
        self._loop_thread.start()

    def _prase_crane_vi_status(self, vehicle_name, status, command_context):
        mode = 0
        if status is None:
            logger.warning(f'[_prase_crane_vi_status]:vehicle_name:{vehicle_name} invalid status')
            return mode
        if  (not status.vehicle_mode.safe_mode) \
          and ((not status.HasField('light_color')) or (status.HasField('light_color') and status.light_color == LightColor.LIGHT_GREEN))\
          and status.chassis.driving_mode == Chassis.DrivingMode.COMPLETE_AUTO_DRIVE \
          and command_context.type == RemoteCommand.WAIT_CRANE_OFF:
        #if  (not status.vehicle_mode.safe_mode) and command_context.type == RemoteCommand.WAIT_CRANE_OFF:
            mode = 1
        logger.debug(f'[_prase_crane_vi_status]:vehicle status info:vehicle_name:{vehicle_name},safe_mode:{status.vehicle_mode.safe_mode},'
            f'light_color:{LightColor.Name(status.light_color)},driving_mode:{Chassis.DrivingMode.Name(status.chassis.driving_mode)},mode:{mode}')
        return mode
  
    def loop(self):
        #先车后桥吊，测试，场景，车辆名称，而且是属于上面的桥吊列表中的
        #车辆突然下线了呢？
        start = time.time()
        via_finish_crane_nos = dict() #'crane_no':(vehicle_name,tos_id,mode)
        #via_unfinish_crane_nos = {}
        online_vehicles = self._vehicle_cache.get_online_trucks()
        online_vehicles = list(filter(lambda v:(not NameConverter().is_debug_truck_no(v)), online_vehicles))
        for v in online_vehicles:
            commands = self._command_cache.query_commands(v)
            if len(commands) > 0:
                command_context = commands[0].command.command_context
                transfer_point = command_context.transfer_point
                logger.debug(f'[UpDateCraneStorePlc]:command info:vehicle_name:{v},transfer_point:{transfer_point},'
                            f'command_type:{RemoteCommand.CommandType.Name(command_context.type)}'
                            f',tos_command_id:{command_context.tos_command_id}')
                if transfer_point.startswith("CR") and command_context.type == RemoteCommand.WAIT_CRANE_OFF \
                    and (int(crane_no := transfer_point[2:]) in self._crane_list) and (crane_no not in via_finish_crane_nos):
                    status = self._vehicle_cache.get_vehicle_status(v)
                    mode = self._prase_crane_vi_status(v, status, command_context)
                    if mode:
                        via_finish_crane_nos[crane_no] = (v,command_context.tos_command_id,mode)
        diff_via_crane_nos = dict(self._via_finish_crane_nos.items() - via_finish_crane_nos.items())
        self._via_finish_crane_nos = copy.deepcopy(via_finish_crane_nos)
        #前后差的说明是需要重更新状态的
        for (crane_key, crane_status) in diff_via_crane_nos.items():
            crane_status = list(crane_status)
            crane_status[2] = 0
            diff_via_crane_nos[crane_key] = tuple(crane_status)
        #若相同key=crane_no,via_finish_crane_nos覆盖diff_via_crane_nos,或{diff_via_crane_nos,via_finish_crane_nos}
        update_via_crane_nos = dict(diff_via_crane_nos, **via_finish_crane_nos)
        update_via_crane_nos = dict(sorted(update_via_crane_nos.items(), key=lambda via_crane: via_crane[0]))
        logger.debug(f'[UpDateCraneStorePlc]:via_finish_crane_nos:{via_finish_crane_nos},_via_finish_crane_nos:{self._via_finish_crane_nos},'
                     f'diff_via_crane_nos:{diff_via_crane_nos},update_via_crane_nos:{update_via_crane_nos}')
        self._msg_no = self._msg_no + 1
        for (crane_key, crane_status) in update_via_crane_nos.items():
            crane_no = int(crane_key)
            truck_no = crane_status[0]
            wi_id = crane_status[1]
            mode = crane_status[2]
            info = make_crane_store_plc(crane_no, self._msg_no, int(truck_no[2:]), wi_id, mode, self._update_env)
            logger.debug(f'[UpDateCraneStorePlc] update crane_no:{crane_no},info:{MessageToString(info, as_one_line=True)}')
            self._plc_cache.set_store_crane(crane_no, info, str(mode))
        end = time.time()
        logger.debug(f"UpDateCraneStorePlc loop cost:{end - start}")
        if (end - start) > 1.0:
            logger.warning(f"UpDateCraneStorePlc loop over cost execeed round time:{round(end - start)}, execeed time:{end - start}")