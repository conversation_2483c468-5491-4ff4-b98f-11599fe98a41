from distutils.log import debug
import time, datetime, traceback
from google.protobuf.text_format import Message<PERSON><PERSON><PERSON><PERSON>

from common.periodical_task import PeriodicalTask
from common.logger import logger
from common.singleton import Singleton
from proto.antenna_pb2 import ForbidStopArea
from proto.cache_pb2 import ForbidStopAreaSet

from cache.map_cache import Map<PERSON>ache
from cache.command_cache import Command<PERSON>ache
from transfer_point_map.transfer_point import TransferPoint
from common.constant import STACKER_TAIL_CLOSE_ROAD_DISTANCE,STACKER_HEAD_FORBID_AREAR_DISTANCE,TWO_YARD_BAY_LENGTH


class RestrictAreaMapBase(object):
    def __init__(self, forward_yard_bay_gap = STACKER_TAIL_CLOSE_ROAD_DISTANCE, backward_yard_bay_gap = STACKER_HEAD_FORBID_AREAR_DISTANCE):
        self._two_yard_bay_length = TWO_YARD_BAY_LENGTH #两个贝长度
        self._forward_yard_bay_gap = forward_yard_bay_gap #堆高机车尾离作业贝位关路距离
        self._backward_yard_bay_gap = backward_yard_bay_gap #堆高机车头离作业贝位关路距离
        self._map_cache = None
        self._command_cache = None
        self._forbid_stop_area_loop_thread = None
        self._init_restrict()

    def _init_restrict(self):
        self._even_forward_length = self._two_yard_bay_length + self._forward_yard_bay_gap    #6.55 + 3
        self._even_backward_length = self._two_yard_bay_length + self._backward_yard_bay_gap  #6.55 + 3
        self._odd_forward_length = self._two_yard_bay_length/2  + self._forward_yard_bay_gap  #6.55/2 + 3
        self._odd_backward_length = self._two_yard_bay_length/2 + 3*self._backward_yard_bay_gap #6.55/2 + 3*3

    def _get_forbid_center_point(self,to_pos):
        if not to_pos:
            return None
        (ctn_size, ctn_pos) =  ('SIZE_40_FEET', '') if int(to_pos[-1]) % 2 == 0 else ('SIZE_20_FEET', 'MIDDLE')
        return TransferPoint().get_transfer_point_loc_v2(to_pos,'AT500', ctn_size, ctn_pos, 'center')

    def _get_forbid_area_length(self,to_pos):
        if not to_pos:
            return None
        if int(to_pos[-1]) % 2 == 0:
            return (self._even_forward_length,self._even_backward_length)
        else:
            return (self._odd_forward_length,self._odd_backward_length)

    def _make_forbid_stop_area(self,work_pos,timestamp_ms):
        area = ForbidStopArea()
        area.timestamp_ms = timestamp_ms
        area.work_pos = work_pos
        area.owner = "dynamic_stop"
        if (center_point:=self._get_forbid_center_point(area.work_pos)):
            area.center_point.x,area.center_point.y = center_point[0], center_point[1]
        else:
            return None
        if (area_length:=self._get_forbid_area_length(area.work_pos)):
            area.forward_length,area.backward_length = area_length[0],area_length[1]
        else:
            return None
        return area

    def _forbid_stop_area_loop(self):
        start = time.time()
        debug_string = []
        areas = ForbidStopAreaSet()
        stack_forbids = self._map_cache.hget_stack_work_areas()
        stack_forbids = sorted(stack_forbids, key=lambda stack: stack.yard_id)
        stack_forbids_yard_count = len(stack_forbids)
        stack_forbids_count = 0
        areas.timestamp_ms = int(time.time() * 1000)
        for stack in stack_forbids:
            for bay in sorted(stack.bayset):
                try:
                    work_pos = stack.yard_id + ("%02d" % (bay))
                    debug_string.append(work_pos)
                    if (area:=self._make_forbid_stop_area(work_pos,areas.timestamp_ms)):
                        areas.forbid_stop_areas.append(area)
                    stack_forbids_count = stack_forbids_count + 1
                except Exception as e:
                    logger.warning(f"fail to make forbid for stack:{work_pos},"
                                  f"error:{e}, trace:{traceback.format_exc()}")
        self._map_cache.set_forbid_stop_area(areas)
        logger.debug(f"####################\n"
                     f"[_forbid_stop_area_loop] stack_forbids_yard_count:{stack_forbids_yard_count},"
                     f"stack_forbids_count:{stack_forbids_count},debug_string:{debug_string}")
        logger.debug(f"[_forbid_stop_area_loop] make forbid top areas:{MessageToString(areas, as_one_line=True)}")
        end = time.time()
        logger.debug(f"forbid_stop_area_loop loop cost:{end - start}")
        if (end - start) > 1.1:
            logger.warning(f"forbid_stop_area_loop over cost execeed round time:{round(end - start)}, execeed time:{end - start}")

    def start(self):
        self._map_cache = MapCache()
        self._forbid_stop_area_loop_thread = PeriodicalTask(target=self._forbid_stop_area_loop, interval=1, master_only=True)
        self._forbid_stop_area_loop_thread.start()


class RestrictAreaMap(RestrictAreaMapBase, metaclass=Singleton):
    pass