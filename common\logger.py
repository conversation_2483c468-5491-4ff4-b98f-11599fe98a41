import datetime
import logging.handlers
import multiprocessing
import os
import sys
import time
from abc import ABC, abstractmethod
from typing import Any, Optional

from common.constant import SIM_MODE


class DependencyChecker:
    """依赖检测与兼容性管理"""

    def __init__(self):
        self._check_dependencies()

    def _check_dependencies(self):
        """严格按优先级检测日志库"""
        self._has_loguru = self._check_module_exists("loguru")
        self._has_mp_logging = (
            self._check_module_exists("multiprocessing_logging")
            if not self._has_loguru
            else False
        )

    def _check_module_exists(self, module_name: str) -> bool:
        try:
            __import__(module_name)
            return True
        except ImportError:
            return False

    @property
    def logger_type(self) -> str:
        if self._has_loguru:
            return "loguru"
        if self._has_mp_logging:
            return "multiprocessing"
        return "standard"


class LogConfig:
    ROTATION_SIZE = 1e9
    ROTATION_DATE = datetime.time(0, 0, 0)
    COMPRESSION = None  # 因为压缩会占用当前进程cpu，导致其他任务变慢，需要改写后再打开
    BACKUP_COUNT = 15


class BaseLogger(ABC):
    @abstractmethod
    def init_logger(self, name: str): ...


class LoggerFactory(ABC):
    @abstractmethod
    def create_logger(self) -> BaseLogger: ...


class LoguruFactory(LoggerFactory):
    def create_logger(self) -> BaseLogger:
        return LoguruLogger()


class MultiprocessLoggerFactory(LoggerFactory):
    def create_logger(self) -> BaseLogger:
        return MultiprocessLogger()


class StandardLoggerFactory(LoggerFactory):
    def create_logger(self) -> BaseLogger:
        return StandardLogger()


class LoggerWrapper(BaseLogger):
    """自动初始化的统一日志接口"""

    def __init__(self):
        self._factory = self._select_factory()
        self._logger = self._factory.create_logger()
        self._initialized = False

    def _select_factory(self) -> LoggerFactory:
        checker = DependencyChecker()
        return {
            "loguru": LoguruFactory(),
            "multiprocessing": MultiprocessLoggerFactory(),
            "standard": StandardLoggerFactory(),
        }[checker.logger_type]

    def init_logger(self, name: str):
        if not self._initialized:
            os.makedirs("log", exist_ok=True)
            self._initialized = True
        self._logger.init_logger(name)

    def __getattr__(self, name: str):
        return getattr(self._logger, name)


class MultiprocessLogger(BaseLogger):
    def __init__(self):
        self._logger = logging.getLogger(__name__)

    def init_logger(self, name: str):
        formatter = logging.Formatter(
            "%(levelname)s-%(asctime)s.%(msecs)03d [%(filename)s:%(lineno)s] %(message)s",
            "%m-%d %H:%M:%S",
        )
        for handler in self._logger.handlers[:]:
            self._logger.removeHandler(handler)

        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        self._logger.addHandler(console_handler)

        if not name:
            name = "main"
        for level, level_name in [
            (logging.DEBUG, "DEBUG"),
            (logging.INFO, "INFO"),
            (logging.WARNING, "WARNING"),
            (logging.ERROR, "ERROR"),
        ]:
            handler = logging.handlers.TimedRotatingFileHandler(
                f"log/{name}.{level_name}",
                when="MIDNIGHT",
                interval=1,
                backupCount=LogConfig.BACKUP_COUNT,
            )
            handler.setFormatter(formatter)
            handler.setLevel(level)
            self._logger.addHandler(handler)

        self._logger.setLevel(logging.DEBUG)
        import multiprocessing_logging

        multiprocessing_logging.install_mp_handler(self._logger)

    def __getattr__(self, name: str):
        return getattr(self._logger, name)


class LoguruLogger(BaseLogger):

    class Rotator:
        def __init__(self, *, size, at):
            now = datetime.datetime.now()

            self._size_limit = size
            self._time_limit = now.replace(
                hour=at.hour, minute=at.minute, second=at.second
            )

            if now >= self._time_limit:
                # The current time is already past the target time so it would rotate already.
                # Add one day to prevent an immediate rotation.
                self._time_limit += datetime.timedelta(days=1)

        def should_rotate(self, message, file):
            file.seek(0, 2)
            if file.tell() + len(message) > self._size_limit:
                return True
            excess = message.record["time"].timestamp() - self._time_limit.timestamp()
            if excess >= 0:
                elapsed_days = datetime.timedelta(seconds=excess).days
                self._time_limit += datetime.timedelta(days=elapsed_days + 1)
                return True
            return False

    def __init__(self):
        import loguru

        self._logger = loguru.logger
        self._logger_formatter = "{level}-{time:MM-DD HH:mm:ss.SSS} [{file}:{line}-{process}-{thread}] {message}"
        # 移除默认处理器,并添加新的控制台处理器，设置日志级别为WARNING
        loguru.logger.remove(0)
        loguru.logger.add(sys.stderr, level="WARNING")
        loguru.logger.add(
            "log/main.log",
            format=self._logger_formatter,
            rotation=LoguruLogger.Rotator(
                size=LogConfig.ROTATION_SIZE, at=LogConfig.ROTATION_DATE
            ).should_rotate,
            level="DEBUG",
            retention=LogConfig.BACKUP_COUNT,
            compression=LogConfig.COMPRESSION,
            filter=lambda r: not r["extra"].get("name"),
            enqueue=True,
        )

    def init_logger(self, name: str):
        import loguru

        if not name:
            self._logger = loguru.logger
        else:
            self._logger = loguru.logger.bind(name=name)
            for level in ("DEBUG", "INFO", "WARNING", "ERROR"):
                loguru.logger.add(
                    f"log/{name}.{level}",
                    format=self._logger_formatter,
                    rotation=LoguruLogger.Rotator(
                        size=LogConfig.ROTATION_SIZE, at=LogConfig.ROTATION_DATE
                    ).should_rotate,
                    level=level,
                    retention=LogConfig.BACKUP_COUNT,
                    compression=LogConfig.COMPRESSION,
                    filter=lambda r: r["extra"].get("name") == name,
                    enqueue=True,
                )

    def __getattr__(self, name: str):
        return getattr(self._logger, name)


class StandardLogger(BaseLogger):
    def __init__(self):
        self._logger = logging.getLogger(__name__)

    def init_logger(self, name: str):
        formatter = logging.Formatter(
            "%(levelname)s-%(asctime)s.%(msecs)03d [%(filename)s:%(lineno)s] %(message)s",
            "%m-%d %H:%M:%S",
        )
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        self._logger.addHandler(console_handler)
        self._logger.setLevel(logging.DEBUG)

    def __getattr__(self, name: str):
        return getattr(self._logger, name)


logger = LoggerWrapper()


def init_logger(name: str):
    """统一初始化入口"""
    if SIM_MODE:
        name = f"{name}_{os.environ.get('USER', 'NONE')}"
    logger.init_logger(name)


# 保留原有初始化函数
def init_chassis_log():
    init_logger("chassis")


def init_server_log():
    init_logger("server")


def init_plc_log():
    init_logger("plc")


def init_tos_bridge_log(suffix=""):
    init_logger(f"tos_bridge{suffix}")


def init_block_map_log():
    init_logger("block_map")


def init_server_monitor_log():
    init_logger("server_monitor")


def init_vehicle_info_log():
    init_logger("vehicle_info")


def init_event_logger_log():
    init_logger("event_logger")


def init_transfer_position_log():
    init_logger("transfer_position")


def init_vms_consumer_log():
    init_logger("vms_consumer")


def init_plc_vms_consumer_log():
    init_logger("plc_vms_consumer")


def init_inner_vms_consumer_log():
    init_logger("inner_vms_consumer")


def init_vms_producer_log():
    init_logger("vms_producer")


def init_inner_vms_producer_log():
    init_logger("inner_vms_producer")


def init_auto_calibration_log():
    init_logger("auto_calibration")


def init_fast_server_log():
    init_logger("server_fast")


def init_debug_fast_server_log():
    init_logger("server_debug_fast")


def init_timing_recorder_log():
    init_logger("timing_recorder")


def init_tos_redeposit_log():
    init_logger("tos_redeposit")


def init_data_status_log():
    init_logger("data_status")


def init_device_status_log():
    init_logger("device_status")


def init_heartbeat_log():
    init_logger("heartbeat")


def init_work_alarm_log():
    init_logger("work_alarm")


def init_update_crane_store_log():
    init_logger("update_crane_store")


def init_crane_sender_log():
    init_logger("crane_sender")


def init_gantry_sender_log():
    init_logger("gantry_sender")

def init_mqtt_log():
    init_logger("mqtt_client")


def _test_logger():
    i = 0
    while True:
        logger.info(f"{os.getpid()}_{i}")
        i += 1
        time.sleep(0.1)


def _test_mp_logger():
    workers = []
    init_logger("test")
    init_logger("test2")
    for i in range(2):
        worker = multiprocessing.Process(target=_test_logger)
        worker.start()
        workers.append(worker)
    for worker in workers:
        worker.join()


if __name__ == "__main__":
    _test_mp_logger()
