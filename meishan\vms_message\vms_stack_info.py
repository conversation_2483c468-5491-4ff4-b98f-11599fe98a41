import json
import time
from google.protobuf import json_format
from google.protobuf.text_format import MessageToString
from common.logger import logger
from cache.map_cache import Map<PERSON>ache
from proto.cache_pb2 import StackWorkAreaInfo


def vms_stack_info_handler(msg):
    stack_info = json.loads(msg.decode())
    stack_info_dict = {k.lower(): v for k, v in stack_info.items()}
    area = StackWorkAreaInfo()
    json_format.ParseDict(stack_info_dict,area,ignore_unknown_fields=True)
    area.recv_timestamp = int(time.time() * 1000)
    MapCache().hset_stack_work_area(area)
    return



if __name__ == '__main__':
    wi_j = b'{"YARD_ID": "XF", "BAYSET":[40]}'
    #wi_j = b'{"YARD_ID": "Y7"}'
    vms_stack_info_handler(wi_j)
