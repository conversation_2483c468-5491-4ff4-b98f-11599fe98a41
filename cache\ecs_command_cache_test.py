import uuid
import time

from cache.ecs_command_cache import EcsCommandCache
from proto import ecs_command_pb2

if __name__ == '__main__':
    cache = EcsCommandCache()
    # command
    command = ecs_command_pb2.EcsCommand()
    command.vehicle_name = 'AT520'
    command.user_id = 'test'

    # test slow brake
    print('------dispatch slow brake command-------')
    command.uuid = str(uuid.uuid4())
    command.timestamp = int(time.time())
    command.command_type = ecs_command_pb2.EcsCommandType.SLOW_BRAKE
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.DISPATCH
    command.slow_brake_command.brake_reason = "test slow brake"
    print(cache.dispatch_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, 'AT520') is True)
    print(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, 'AT520'))

    print('------cancel slow brake command------')
    command.timestamp = int(time.time())
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.CANCEL
    print(cache.cancel_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, 'AT520') is False)
    print(len(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.SLOW_BRAKE, 'AT520')) == 0)

    # test emergency brake
    print('------dispatch emergency brake command-------')
    command.uuid = str(uuid.uuid4())
    command.timestamp = int(time.time())
    command.command_type = ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.DISPATCH
    command.emergency_brake_command.brake_reason = "emergency_brake_command"
    print(cache.dispatch_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, 'AT520') is True)
    print(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, 'AT520'))

    print('------cancel emergency brake command------')
    command.timestamp = int(time.time())
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.CANCEL
    print(cache.cancel_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, 'AT520') is False)
    print(len(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.EMERGENCY_BRAKE, 'AT520')) == 0)

    # test all slow brake
    print('------dispatch all slow brake command-------')
    command.uuid = str(uuid.uuid4())
    command.timestamp = int(time.time())
    command.all_slow_brake_command.brake_reason = "all_slow_brake_command"
    command.command_type = ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.DISPATCH
    print(cache.dispatch_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE) is True)
    print(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE))
    print(cache.query_brake_reason_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE))

    print('------cancel all slow brake command------')
    command.timestamp = int(time.time())
    command.execute_type = ecs_command_pb2.EcsCommandExecuteType.CANCEL
    print(cache.cancel_brake_command(command))
    print(cache.query_brake_status_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE) is False)
    print(len(cache.query_brake_command_set_by_type(ecs_command_pb2.EcsCommandType.ALL_SLOW_BRAKE)) == 0)