#!/usr/bin/env python3
"""
测试XF40特殊offset处理
"""

import sys
import numpy as np
sys.path.insert(0, '.')

def test_special_offset():
    """测试XF和8M箱区的offset计算"""
    try:
        from transfer_point_map.transfer_point import TransferPoint

        print("=== 特殊箱区 Offset测试 ===")
        print("XF箱区: 向右偏移6.0米")
        print("8M箱区: 向左偏移3.0米")

        # 测试TransferPoint
        tp = TransferPoint()

        # 模拟原始坐标
        original_x, original_y = 404900.0, 3296200.0
        print(f"\n原始坐标: ({original_x}, {original_y})")

        # 测试不同的XF位置
        xf_positions = ['XF40', 'XF30', 'XF50', 'XF10']
        print(f"\n=== XF箱区测试 ===")
        for pos in xf_positions:
            adjusted_x, adjusted_y = tp._apply_special_offset(original_x, original_y, pos)
            print(f"{pos}调整后坐标: ({adjusted_x}, {adjusted_y})")
            print(f"  实际offset: ({adjusted_x - original_x:.3f}, {adjusted_y - original_y:.3f})")

        # 测试不同的8M位置
        m8_positions = ['8M40', '8M30', '8M50', '8M10']
        print(f"\n=== 8M箱区测试 ===")
        for pos in m8_positions:
            adjusted_x, adjusted_y = tp._apply_special_offset(original_x, original_y, pos)
            print(f"{pos}调整后坐标: ({adjusted_x}, {adjusted_y})")
            print(f"  实际offset: ({adjusted_x - original_x:.3f}, {adjusted_y - original_y:.3f})")

        # 测试其他箱区位置
        other_positions = ['XG40', '8L40', '7F40']
        print(f"\n=== 其他箱区测试 ===")
        for pos in other_positions:
            adjusted_x, adjusted_y = tp._apply_special_offset(original_x, original_y, pos)
            print(f"{pos}调整后坐标: ({adjusted_x}, {adjusted_y}) (应该无变化)")
            print(f"  实际offset: ({adjusted_x - original_x:.3f}, {adjusted_y - original_y:.3f})")

        print("\n✓ 特殊箱区 offset测试完成")
        
    except Exception as e:
        import traceback
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()

if __name__ == '__main__':
    test_special_offset()
