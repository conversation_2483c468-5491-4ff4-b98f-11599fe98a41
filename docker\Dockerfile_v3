FROM python:3.8.1


WORKDIR /tmp



ENV DEBIAN_FRONTEND=noninteractive

# change apt source
RUN sed -i 's/http:\/\/deb.debian.org/http:\/\/mirrors.163.com/g' /etc/apt/sources.list

RUN apt clean && \
    apt update -y && \
    apt install -y \
    build-essential \
    sudo \
    rsync \
    vim \
    git \
    wget \
    curl \
    zsh \
    sqlite3 \
    net-tools \
    redis \
    alien libaio1 unixodbc \
    tmux \
    netcat \
    sysstat \
    iftop \
    htop \
    fping

COPY installers /tmp/installers
RUN bash /tmp/installers/install_skel.sh
RUN bash /tmp/installers/install_instant_client.sh

RUN python -m pip install --upgrade pip

COPY aliphone /tmp/aliphone
RUN python /tmp/aliphone/setup.py install
RUN rm -rf /tmp/aliphone

RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple \
    grpcio==1.34.0 \
    grpcio-tools==1.34.0 \
    mysqlclient==2.0.3 \
    numpy==1.19.4 \
    protobuf==3.13.0 \
    cx-Oracle==8.0.1 \
    wheel \
    redis==3.5.3 \
    Flask==1.1.2 \
    SQLAlchemy==1.3.20 \
    utm==0.6.0 \
    multiprocessing-logging==0.3.1 \
    kafka-python==2.0.2 \
    requests==2.25.1 \
    psutil \
    pynmea2 \
    ksql \
    libtmux