config_area {
  critical_area_from {
    utm_x: 404128.42164132587
    utm_y: 3294863.416825905
  }
  critical_area_to {
    utm_x: 404152.3547572268
    utm_y: 3294886.1369923498
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404136.84
        utm_y: 3294837.8
      }
      to_point {
        utm_x: 404152.3547572268
        utm_y: 3294886.1369923498
      }
    }
  }
  description: "62"
}
config_area {
  critical_area_from {
    utm_x: 404120.2722426434
    utm_y: 3294871.932529163
  }
  critical_area_to {
    utm_x: 404143.92508205696
    utm_y: 3294894.944334561
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404136.84
        utm_y: 3294837.8
      }
      to_point {
        utm_x: 404143.92508205696
        utm_y: 3294894.944334561
      }
    }
  }
  description: "63"
}
config_area {
  critical_area_from {
    utm_x: 404083.9693771927
    utm_y: 3294900.4207189474
  }
  critical_area_to {
    utm_x: 404107.6665540167
    utm_y: 3294923.386863826
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404090.49222779344
        utm_y: 3294875.9035957814
      }
      to_point {
        utm_x: 404107.6665540167
        utm_y: 3294923.386863826
      }
    }
  }
  description: "64"
}
config_area {
  critical_area_from {
    utm_x: 404071.068673655
    utm_y: 3294904.4477544893
  }
  critical_area_to {
    utm_x: 404094.36607901985
    utm_y: 3294927.8193377317
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404090.49222779344
        utm_y: 3294875.9035957814
      }
      to_point {
        utm_x: 404094.36607901985
        utm_y: 3294927.8193377317
      }
    }
  }
  description: "65"
}
config_area {
  critical_area_from {
    utm_x: 404034.3869122515
    utm_y: 3294933.1913150167
  }
  critical_area_to {
    utm_x: 404058.0320928117
    utm_y: 3294956.2109899987
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404044.14445558685
        utm_y: 3294914.007191563
      }
      to_point {
        utm_x: 404058.0320928117
        utm_y: 3294956.2109899987
      }
    }
  }
  description: "66"
}
config_area {
  critical_area_from {
    utm_x: 404025.8507430983
    utm_y: 3294942.212861484
  }
  critical_area_to {
    utm_x: 404049.70048719365
    utm_y: 3294965.0205291985
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404044.14445558685
        utm_y: 3294914.007191563
      }
      to_point {
        utm_x: 404049.70048719365
        utm_y: 3294965.0205291985
      }
    }
  }
  description: "67"
}
config_area {
  critical_area_from {
    utm_x: 403986.99472988537
    utm_y: 3294973.2277757437
  }
  critical_area_to {
    utm_x: 404010.8378928129
    utm_y: 3294996.042323331
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 403997.7966833802
        utm_y: 3294952.1107873446
      }
      to_point {
        utm_x: 404010.8378928129
        utm_y: 3294996.042323331
      }
    }
  }
  description: "68"
}
config_area {
  critical_area_from {
    utm_x: 403978.5039188438
    utm_y: 3294981.93523216
  }
  critical_area_to {
    utm_x: 404002.30650413263
    utm_y: 3295004.792111509
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 403997.7966833802
        utm_y: 3294952.1107873446
      }
      to_point {
        utm_x: 404002.30650413263
        utm_y: 3295004.792111509
      }
    }
  }
  description: "69"
}
config_area {
  critical_area_from {
    utm_x: 403942.143998652
    utm_y: 3295010.5919259787
  }
  critical_area_to {
    utm_x: 403966.0134939152
    utm_y: 3295033.37892218
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 403951.4489111736
        utm_y: 3294990.2143831262
      }
      to_point {
        utm_x: 403966.0134939152
        utm_y: 3295033.37892218
      }
    }
  }
  description: "6A"
}
config_area {
  critical_area_from {
    utm_x: 403933.8660713252
    utm_y: 3295019.1594837364
  }
  critical_area_to {
    utm_x: 403957.6065066783
    utm_y: 3295042.0809088503
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 403951.4489111736
        utm_y: 3294990.2143831262
      }
      to_point {
        utm_x: 403957.6065066783
        utm_y: 3295042.0809088503
      }
    }
  }
  description: "6B"
}
config_area {
  critical_area_from {
    utm_x: 403901.9387262189
    utm_y: 3295052.4078405555
  }
  critical_area_to {
    utm_x: 403925.81329201156
    utm_y: 3295075.1895241677
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 403905.10113896703
        utm_y: 3295028.317978908
      }
      to_point {
        utm_x: 403925.81329201156
        utm_y: 3295075.1895241677
      }
    }
  }
  description: "6C"
}
config_area {
  critical_area_from {
    utm_x: 404367.3484596179
    utm_y: 3295092.0697261756
  }
  critical_area_to {
    utm_x: 404391.2203131117
    utm_y: 3295114.8542518625
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404382.84
        utm_y: 3295073.39
      }
      to_point {
        utm_x: 404391.2203131117
        utm_y: 3295114.8542518625
      }
    }
  }
  description: "72"
}
config_area {
  critical_area_from {
    utm_x: 404359.1048962459
    utm_y: 3295100.656269718
  }
  critical_area_to {
    utm_x: 404382.8153335982
    utm_y: 3295123.608724065
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404382.84
        utm_y: 3295073.39
      }
      to_point {
        utm_x: 404382.8153335982
        utm_y: 3295123.608724065
      }
    }
  }
  description: "73"
}
config_area {
  critical_area_from {
    utm_x: 404327.38883037254
    utm_y: 3295133.8950439
  }
  critical_area_to {
    utm_x: 404351.20969288715
    utm_y: 3295156.7328745555
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404341.3432998235
        utm_y: 3295116.7261728174
      }
      to_point {
        utm_x: 404351.20969288715
        utm_y: 3295156.7328745555
      }
    }
  }
  description: "74"
}
config_area {
  critical_area_from {
    utm_x: 404318.944540925
    utm_y: 3295142.666761938
  }
  critical_area_to {
    utm_x: 404342.76205060835
    utm_y: 3295165.5080892304
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404341.3432998235
        utm_y: 3295116.7261728174
      }
      to_point {
        utm_x: 404342.76205060835
        utm_y: 3295165.5080892304
      }
    }
  }
  description: "75"
}
config_area {
  critical_area_from {
    utm_x: 404287.3795960899
    utm_y: 3295175.501240832
  }
  critical_area_to {
    utm_x: 404311.1210260043
    utm_y: 3295198.421635803
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404299.84659964696
        utm_y: 3295160.062345635
      }
      to_point {
        utm_x: 404311.1210260043
        utm_y: 3295198.421635803
      }
    }
  }
  description: "76"
}
config_area {
  critical_area_from {
    utm_x: 404278.8332284649
    utm_y: 3295184.4635157716
  }
  critical_area_to {
    utm_x: 404302.65897641674
    utm_y: 3295207.29624961
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404299.84659964696
        utm_y: 3295160.062345635
      }
      to_point {
        utm_x: 404302.65897641674
        utm_y: 3295207.29624961
      }
    }
  }
  description: "77"
}
config_area {
  critical_area_from {
    utm_x: 404244.63697448844
    utm_y: 3295220.113131004
  }
  critical_area_to {
    utm_x: 404268.52613118076
    utm_y: 3295242.879513952
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404258.34989947046
        utm_y: 3295203.3985184524
      }
      to_point {
        utm_x: 404268.52613118076
        utm_y: 3295242.879513952
      }
    }
  }
  description: "78"
}
config_area {
  critical_area_from {
    utm_x: 404264.6869803672
    utm_y: 3295256.1368352375
  }
  critical_area_to {
    utm_x: 404288.5486729682
    utm_y: 3295278.932001967
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404258.34989947046
        utm_y: 3295203.3985184524
      }
      to_point {
        utm_x: 404288.5486729682
        utm_y: 3295278.932001967
      }
    }
  }
  description: "79"
}
config_area {
  critical_area_from {
    utm_x: 404204.68835743336
    utm_y: 3295262.002521484
  }
  critical_area_to {
    utm_x: 404228.5367969554
    utm_y: 3295284.8115533
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404216.8531992939
        utm_y: 3295246.7346912697
      }
      to_point {
        utm_x: 404228.5367969554
        utm_y: 3295284.8115533
      }
    }
  }
  description: "7A"
}
config_area {
  critical_area_from {
    utm_x: 404196.60106632125
    utm_y: 3295270.8362178183
  }
  critical_area_to {
    utm_x: 404220.4119728684
    utm_y: 3295293.684428457
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404216.8531992939
        utm_y: 3295246.7346912697
      }
      to_point {
        utm_x: 404220.4119728684
        utm_y: 3295293.684428457
      }
    }
  }
  description: "7B"
}
config_area {
  critical_area_from {
    utm_x: 404165.02467752626
    utm_y: 3295304.049183783
  }
  critical_area_to {
    utm_x: 404188.82760302466
    utm_y: 3295326.905708841
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404175.3564991174
        utm_y: 3295290.070864087
      }
      to_point {
        utm_x: 404188.82760302466
        utm_y: 3295326.905708841
      }
    }
  }
  description: "7C"
}
config_area {
  critical_area_from {
    utm_x: 404625.5963717643
    utm_y: 3295339.084758542
  }
  critical_area_to {
    utm_x: 404649.40543610905
    utm_y: 3295361.9348888486
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404640.15
        utm_y: 3295320.55
      }
      to_point {
        utm_x: 404649.40543610905
        utm_y: 3295361.9348888486
      }
    }
  }
  description: "82"
}
config_area {
  critical_area_from {
    utm_x: 404616.86338387453
    utm_y: 3295348.1793723507
  }
  critical_area_to {
    utm_x: 404640.72635407036
    utm_y: 3295370.973201635
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404640.15
        utm_y: 3295320.55
      }
      to_point {
        utm_x: 404640.72635407036
        utm_y: 3295370.973201635
      }
    }
  }
  description: "83"
}
config_area {
  critical_area_from {
    utm_x: 404585.4795597498
    utm_y: 3295381.000446048
  }
  critical_area_to {
    utm_x: 404609.3031045716
    utm_y: 3295403.8354786104
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404598.6579499221
        utm_y: 3295363.890625057
      }
      to_point {
        utm_x: 404609.3031045716
        utm_y: 3295403.8354786104
      }
    }
  }
  description: "84"
}
config_area {
  critical_area_from {
    utm_x: 404577.1404326763
    utm_y: 3295389.754753487
  }
  critical_area_to {
    utm_x: 404600.9276913867
    utm_y: 3295412.6275828495
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404598.6579499221
        utm_y: 3295363.890625057
      }
      to_point {
        utm_x: 404600.9276913867
        utm_y: 3295412.6275828495
      }
    }
  }
  description: "85"
}
config_area {
  critical_area_from {
    utm_x: 404545.4809655981
    utm_y: 3295422.9726752886
  }
  critical_area_to {
    utm_x: 404568.9377439613
    utm_y: 3295446.1843005833
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404557.1658998441
        utm_y: 3295407.231250114
      }
      to_point {
        utm_x: 404568.9377439613
        utm_y: 3295446.1843005833
      }
    }
  }
  description: "86"
}
config_area {
  critical_area_from {
    utm_x: 404537.0250022813
    utm_y: 3295431.514259877
  }
  critical_area_to {
    utm_x: 404560.7439382234
    utm_y: 3295454.457931723
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404557.1658998441
        utm_y: 3295407.231250114
      }
      to_point {
        utm_x: 404560.7439382234
        utm_y: 3295454.457931723
      }
    }
  }
  description: "87"
}
config_area {
  critical_area_from {
    utm_x: 404502.9336758395
    utm_y: 3295467.436350203
  }
  critical_area_to {
    utm_x: 404526.9203301602
    utm_y: 3295490.099986595
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404515.67384976614
        utm_y: 3295450.571875171
      }
      to_point {
        utm_x: 404526.9203301602
        utm_y: 3295490.099986595
      }
    }
  }
  description: "88"
}
config_area {
  critical_area_from {
    utm_x: 404500.1969012312
    utm_y: 3295480.921166188
  }
  critical_area_to {
    utm_x: 404524.02869938925
    utm_y: 3295503.747585017
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404515.67384976614
        utm_y: 3295450.571875171
      }
      to_point {
        utm_x: 404524.02869938925
        utm_y: 3295503.747585017
      }
    }
  }
  description: "89"
}
config_area {
  critical_area_from {
    utm_x: 404462.4982884557
    utm_y: 3295508.5766841895
  }
  critical_area_to {
    utm_x: 404486.33430940594
    utm_y: 3295531.3986934186
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404474.18179968814
        utm_y: 3295493.912500228
      }
      to_point {
        utm_x: 404486.33430940594
        utm_y: 3295531.3986934186
      }
    }
  }
  description: "8A"
}
config_area {
  critical_area_from {
    utm_x: 404882.6143098627
    utm_y: 3295585.283745844
  }
  critical_area_to {
    utm_x: 404906.3998531047
    utm_y: 3295608.1583591234
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404898.84
        utm_y: 3295568.79
      }
      to_point {
        utm_x: 404906.3998531047
        utm_y: 3295608.1583591234
      }
    }
  }
  description: "J2"
}
config_area {
  critical_area_from {
    utm_x: 404874.3571294926
    utm_y: 3295593.8538887952
  }
  critical_area_to {
    utm_x: 404898.12517504167
    utm_y: 3295616.7466826565
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404898.84
        utm_y: 3295568.79
      }
      to_point {
        utm_x: 404898.12517504167
        utm_y: 3295616.7466826565
      }
    }
  }
  description: "J3"
}
config_area {
  critical_area_from {
    utm_x: 404842.66208864463
    utm_y: 3295627.2062203223
  }
  critical_area_to {
    utm_x: 404866.52281557233
    utm_y: 3295650.002397863
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404857.3408132629
        utm_y: 3295612.1237916662
      }
      to_point {
        utm_x: 404866.52281557233
        utm_y: 3295650.002397863
      }
    }
  }
  description: "94"
}
config_area {
  critical_area_from {
    utm_x: 404834.252025288
    utm_y: 3295636.0093258233
  }
  critical_area_to {
    utm_x: 404858.11033164925
    utm_y: 3295658.8080366985
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404857.3408132629
        utm_y: 3295612.1237916662
      }
      to_point {
        utm_x: 404858.11033164925
        utm_y: 3295658.8080366985
      }
    }
  }
  description: "95"
}
config_area {
  critical_area_from {
    utm_x: 404802.57213863387
    utm_y: 3295668.995092856
  }
  critical_area_to {
    utm_x: 404826.4682356861
    utm_y: 3295691.754190942
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404815.8416265258
        utm_y: 3295655.457583332
      }
      to_point {
        utm_x: 404826.4682356861
        utm_y: 3295691.754190942
      }
    }
  }
  description: "96"
}
config_area {
  critical_area_from {
    utm_x: 404793.9411258999
    utm_y: 3295677.8810128216
  }
  critical_area_to {
    utm_x: 404817.75629470573
    utm_y: 3295700.7247807845
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404815.8416265258
        utm_y: 3295655.457583332
      }
      to_point {
        utm_x: 404817.75629470573
        utm_y: 3295700.7247807845
      }
    }
  }
  description: "97"
}
config_area {
  critical_area_from {
    utm_x: 404759.7472597015
    utm_y: 3295713.780815219
  }
  critical_area_to {
    utm_x: 404783.74680656433
    utm_y: 3295736.430798671
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404774.3424397886
        utm_y: 3295698.791374998
      }
      to_point {
        utm_x: 404783.74680656433
        utm_y: 3295736.430798671
      }
    }
  }
  description: "98"
}
config_area {
  critical_area_from {
    utm_x: 404792.66178413434
    utm_y: 3295761.630872249
  }
  critical_area_to {
    utm_x: 404816.4443988923
    utm_y: 3295784.508530243
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404774.3424397886
        utm_y: 3295698.791374998
      }
      to_point {
        utm_x: 404816.4443988923
        utm_y: 3295784.508530243
      }
    }
  }
  description: "99"
}
config_area {
  critical_area_from {
    utm_x: 404720.94407471793
    utm_y: 3295756.0019585914
  }
  critical_area_to {
    utm_x: 404744.80486484914
    utm_y: 3295778.798069977
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404732.8432530515
        utm_y: 3295742.1251666644
      }
      to_point {
        utm_x: 404744.80486484914
        utm_y: 3295778.798069977
      }
    }
  }
  description: "9A"
}
config_area {
  critical_area_from {
    utm_x: 404711.62407884287
    utm_y: 3295763.9585252474
  }
  critical_area_to {
    utm_x: 404735.4425811222
    utm_y: 3295786.7988174777
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404732.8432530515
        utm_y: 3295742.1251666644
      }
      to_point {
        utm_x: 404735.4425811222
        utm_y: 3295786.7988174777
      }
    }
  }
  description: "9B"
}
config_area {
  critical_area_from {
    utm_x: 404680.21062572184
    utm_y: 3295796.781625521
  }
  critical_area_to {
    utm_x: 404704.0251877456
    utm_y: 3295819.626026052
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404691.3440663144
        utm_y: 3295785.45895833
      }
      to_point {
        utm_x: 404704.0251877456
        utm_y: 3295819.626026052
      }
    }
  }
  description: "9C"
}
config_area {
  critical_area_from {
    utm_x: 404671.5139308047
    utm_y: 3295805.807322285
  }
  critical_area_to {
    utm_x: 404695.22958750126
    utm_y: 3295828.754383697
  }
  map_adjustments {
    adjustment_type: BLOCK_TURN
    adjustment_reason: YARD_BOX_BLOCK
    block_turn {
      from_point {
        utm_x: 404691.3440663144
        utm_y: 3295785.45895833
      }
      to_point {
        utm_x: 404695.22958750126
        utm_y: 3295828.754383697
      }
    }
  }
  description: "JD"
}
approximate_point_offset_from: 0.0
approximate_point_offset_to: 0.0
approximate_point_theta: 0.0
