#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

source $DOCKER_PATH/common.sh
echo "DB_DOCKER_NAME：$DB_DOCKER_NAME"

if [[  -z $(docker images -q ${DB_IMG}) ]]; then
  echo "----------pull image: ${DB_IMG}"
  docker pull $DB_IMG
fi

docker ps -a --format "{{.Names}}" | grep "${DB_DOCKER_NAME}" 1>/dev/null
if [ $? == 0 ]; then
docker stop ${DB_DOCKER_NAME} 1>/dev/null
docker rm -f ${DB_DOCKER_NAME} 1>/dev/null
fi

docker create -it \
    --name $DB_DOCKER_NAME \
    -p ${ANTENNA_DB_PORT}:1521 \
    --add-host 8100886c2eda:127.0.0.1 \
    --hostname 8100886c2eda \
    -v /etc/localtime:/etc/localtime:ro \
    $DB_IMG /bin/bash
docker start $DB_DOCKER_NAME
docker exec $DB_DOCKER_NAME bash -c 'env'
docker exec $DB_DOCKER_NAME /etc/init.d/oracle-xe-18c start
docker cp $LOCAL_DIR/common/create_table.sql $DB_DOCKER_NAME:/root
docker exec $DB_DOCKER_NAME bash -c 'source ~/.bashrc; sqlplus fabutech/fabu1#% < /root/create_table.sql'
# docker cp $LOCAL_DIR/common/create_table_nt.sql $DB_DOCKER_NAME:/root
# docker exec $DB_DOCKER_NAME bash -c 'source ~/.bashrc; sqlplus fabutech/fabu1#% < /root/create_table_nt.sql'
