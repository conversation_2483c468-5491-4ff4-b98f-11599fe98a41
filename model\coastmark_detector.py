import os
import sys
import cv2
import onnxruntime as ort
import numpy as np
from common.singleton import Singleton
from common.logger import logger

ort.set_default_logger_severity(4)


class CoastMarkDetector(metaclass=Singleton):
    def __init__(self):
        self.mark_detector_file = 'model/coastmark_detector_v2.3.onnx'
        self.coastline_recognizer_file = 'model/coastline_recognizer_v2.0.onnx'
        self._load_model()

        self._down_scale = 4
        self._mark_thresh = 0.25
        self._coastline_thresh = 0.45
        self._recognizer_width = 192
        self._recognizer_height = 32
        self.width = 768
        self.height = 128
        self.perspective_areas = {
            '31_left': [[950, 495], [943, 518], [1234, 900], [1209, 933]],
            '31_right': [[52, 925], [77, 955], [314, 499], [322, 519]],
            '32_left': [[937, 458], [931, 480], [1134, 750], [1118, 777]],
            '32_right': [[72, 934], [99, 955], [327, 488], [337, 511]],
            '33_left': [[1464, 396], [1460, 428], [1768, 850], [1744, 892]],
            '33_right': [[99, 1041], [132, 1077], [441, 476], [448, 510]],
            '34_left': [[1590, 489], [1581, 526], [1882, 998], [1852, 1040]],
            '34_right': [[96, 1032], [128, 1070], [450, 512], [460, 544]],
            '35_left': [[1280, 438], [1272, 466], [1569, 903], [1544, 942]],
            '35_right': [[357, 1032], [392, 1068], [663, 506], [676, 534]],
            '36_left': [[1480, 459], [1472, 490], [1768, 904], [1744, 940]],
            '36_right': [[33, 1035], [75, 1072], [368, 527], [378, 563]],
            '37_left': [[927, 443], [920, 463], [1165, 764], [1148, 789]],
            '37_right': [[67, 905], [89, 937], [339, 507], [345, 534]],
            '38_left': [[968, 505], [962, 529], [1219, 891], [1198, 917]],
            '38_right': [[36, 923], [59, 948], [317, 481], [326, 502]],
            '39_left': [[912, 394], [907, 414], [1126, 686], [1111, 714]],
            '39_right': [[37, 929], [60, 958], [314, 547], [320, 571]],
            '40_left': [[953, 456], [948, 476], [1162, 782], [1143, 808]],
            '40_right': [[18, 808], [38, 832], [279, 418], [284, 437]],
            '41_left': [[1503, 492], [1491, 526], [1784, 1028], [1752, 1064]],
            '41_right': [[117, 1038], [152, 1076], [448, 486], [458, 516]],
            '42_left': [[1614, 438], [1605, 474], [1884, 908], [1857, 948]],
            '42_right': [[12, 610], [33, 658], [296, 195], [303, 228]],
            '43_left': [[1588, 207], [1578, 249], [1908, 720], [1881, 777]],
            '43_right': [[106, 1036], [134, 1072], [410, 518], [417, 549]],
            '44_left': [[1401, 472], [1395, 507], [1782, 980], [1755, 1024]],
            '44_right': [[34, 1035], [70, 1068], [400, 476], [410, 510]],
            '45_left': [[1004, 474], [995, 502], [1265, 895], [1241, 929]],
            '45_right': [[56, 922], [76, 946], [324, 516], [332, 540]],
            '46_left': [[972, 431], [967, 455], [1214, 783], [1196, 816]],
            '46_right': [[3, 876], [22, 903], [233, 526], [239, 548]],
            '47_left': [[1451, 352], [1445, 380], [1731, 768], [1705, 801]],
            '47_right': [[342, 1032], [371, 1064], [706, 519], [715, 551]],
            '48_left': [[1448, 469], [1438, 496], [1729, 975], [1700, 1012]],
            '48_right': [[137, 1041], [170, 1069], [504, 493], [515, 524]],
            '49_left': [[1518, 414], [1510, 440], [1763, 818], [1743, 851]],
            '49_right': [[152, 1044], [182, 1075], [503, 506], [512, 539]],
            '50_left': [[1517, 453], [1509, 480], [1753, 897], [1731, 926]],
            '50_right': [[117, 1047], [146, 1074], [403, 537], [413, 566]],
            '51_left': [[1518, 534], [1508, 564], [1813, 1026], [1784, 1050]],
            '51_right': [[184, 1050], [218, 1077], [508, 536], [515, 566]],
            '52_left': [[1486, 427], [1479, 451], [1748, 843], [1725, 878]],
            '52_right': [[207, 1044], [238, 1070], [509, 506], [522, 537]],
            '53_left': [[1412, 385], [1406, 412], [1691, 799], [1666, 833]],
            '53_right': [[179, 1053], [212, 1075], [477, 521], [487, 549]],
            '54_left': [[1462, 444], [1455, 470], [1710, 859], [1683, 891]],
            '54_right': [[157, 1047], [190, 1073], [419, 515], [429, 544]],
            '55_left': [[1505, 445], [1495, 468], [1739, 870], [1714, 900]],
            '55_right': [[186, 1061], [213, 1077], [494, 523], [503, 550]],
            '56_left': [[1478, 422], [1470, 452], [1760, 831], [1737, 864]],
            '56_right': [[128, 1053], [166, 1076], [466, 524], [472, 555]],
        }

        right_target_area = np.float32([[0, 58], [0, 108], [640, 53],
                                        [640, 103]])
        left_target_area = np.float32([[128, 53], [128, 103], [768, 58],
                                       [768, 108]])
        self.perspective_m = {}
        for k, pts1 in self.perspective_areas.items():
            pts2 = left_target_area if 'left' in k else right_target_area
            M = cv2.getPerspectiveTransform(np.float32(pts1), pts2)
            self.perspective_m[k] = M

    def _load_model(self):
        if os.path.isfile(self.mark_detector_file):
            self.mark_detector = ort.InferenceSession(self.mark_detector_file, providers=['CUDAExecutionProvider'])
        else:
            self.mark_detector = None

        if os.path.isfile(self.coastline_recognizer_file):
            self.coastline_recognizer = ort.InferenceSession(
                self.coastline_recognizer_file, providers=['CUDAExecutionProvider'])
        else:
            self.coastline_recognizer = None

    def _preprocess(self, image, use_rgb=True):
        image = image.astype(np.float32)
        mean_rgb = np.array([123.675, 116.28, 103.53])
        std_rgb = np.array([58.395, 57.12, 57.375])

        if use_rgb:
            image = image[..., [2, 1, 0]]
            image -= mean_rgb
            image /= std_rgb
        else:
            mean_bgr = mean_rgb[[2, 1, 0]]
            std_bgr = std_rgb[[2, 1, 0]]
            image -= mean_bgr
            image /= std_bgr

        image = np.transpose(image, [2, 0, 1])

        return image

    def _postprocess(self, result, is_left):
        scores, bboxes, marks = result

        # currently at most two bboxes are detected
        # use the second bbox iff:
        # 1) valid detection
        # 2) more close to crane
        xmin_b1, xmax_b1 = bboxes[0][0, [0, 2]]
        xmin_b2, xmax_b2 = bboxes[0][1, [0, 2]]
        if scores[1] >= self._mark_thresh and ((is_left and xmin_b2 > xmax_b1 and xmax_b2 < self.width) or (not is_left and xmin_b2 > 0 and xmax_b2 < xmin_b1)):
            bbox = bboxes[0][1]
            mark = marks[0][1][0]
        elif scores[0] >= self._mark_thresh:
            bbox = bboxes[0][0]
            mark = marks[0][0][0]
        else:
            return

        bbox = self._down_scale * bbox
        return bbox, mark

    def crop_roi_image(self, crane_id, image, side):
        M = self.perspective_m[f'{crane_id}_{side}']
        roi_img = cv2.warpPerspective(image, M, (self.width, self.height))

        return roi_img

    def check_is_coastline(self, roi_img):
        if self.coastline_recognizer is not None:
            roi_img = cv2.resize(
                roi_img, (self._recognizer_width, self._recognizer_height))
            roi_img = self._preprocess(roi_img)
            scores = self.coastline_recognizer.run(
                None, {'data': roi_img[None]})[0]
            coastline_score = scores[0][1]
            return coastline_score >= self._coastline_thresh
        else:
            logger.warning(f'model file {self.coastline_recognizer_file} not exist!')
            logger.warning(
                'please run "bash ./script/download_model.sh" outside docker to get perception model!')

        # in most cases, roi should be coastline
        return True

    def detect(self, roi_img, is_left):
        if self.mark_detector is not None:
            roi_img = self._preprocess(roi_img)
            result = self.mark_detector.run(None, {'data': roi_img[None]})
            return self._postprocess(result, is_left)
        else:
            logger.warning(f'model file {self.mark_detector_file} not exist!')
            logger.warning(
                'please run "bash ./script/download_model.sh" outside docker to get perception model!')


if __name__ == '__main__':
    if len(sys.argv) > 1:
        imf = sys.argv[1]
    else:
        imf = '/data/datasets/crane_images/images/tmp/49_right_20221202_184829.jpg'
    img = cv2.imread(imf)
    print(img.shape)
    cid, side = os.path.basename(imf).split('_')[:2]

    roi_img = CoastMarkDetector().crop_roi_image(cid, img, side)
    mark_info = CoastMarkDetector().detect(roi_img, side == 'left')
    bbox, mark = mark_info
    x1, y1, x2, y2 = bbox.astype(int)
    text_str = f'{mark}'
    cv2.putText(roi_img, text_str, (x1, y1 - 2), cv2.FONT_HERSHEY_SIMPLEX, 1,
                (0, 0, 255))
    out_file = 'roi.jpg'
    cv2.imwrite(out_file, roi_img)
    print(f'visualization in {out_file}')
