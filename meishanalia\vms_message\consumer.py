import os
import time
import json
import traceback
from threading import Thread

from meishanalia.common.constant import KAFKA_NB_VMS_SERVERS_LIST,VMS_CONTROL_TYPE,VMS_CRANE_TYPE,VMS_TRUCK_TYPE,VMS_WI_TYPE,VMS_AHT_TRUCK_INFO_TOPIC,VMS_AHT_CONTROL_INFO_TOPIC

from common.logger import logger,init_vms_consumer_log
from common.singleton import Singleton
from model.kafka_server import KafkaMsgConsumer
from common.periodical_task import PeriodicalTask
from meishanalia.vms_message.msgs import from_json, VmsMsg
from meishanalia.vms_message.vms_control import vms_control_handler
from meishanalia.vms_message.vms_crane import vms_crane_handler
from meishanalia.vms_message.vms_truck import vms_truck_handler
from meishanalia.vms_message.vms_wi import vms_wi_handler
from config.config_manage import ConfigManage

class ConsumerEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj,datetime.datetime):
            return obj.strftime("datetime.datetime(%Y, %m, %d, %H, %M, %S)")
        else:
            return json.JSONEncoder.default(self,obj)


class VmsConsumers(metaclass=Singleton):
    def __init__(self):
        # 梅山非开发环境都打开kafka功能
        if ConfigManage().is_iecs_scene() and not ConfigManage().is_dev_env():
            self._server = KAFKA_NB_VMS_SERVERS_LIST
            self._topic = VMS_AHT_CONTROL_INFO_TOPIC
            #self._topic = KAFKA_FABU_VEHICLE_GPS_TOPIC
            self._consumer = None
            self._consumer_thread = PeriodicalTask(target=self.__handle_message, interval=1, master_only=True)
            self._handlers = {}
    ''' 
    def dispatch_message(self,msg):
        logger.info(f'msg topic: {msg.topic},partition: {msg.partition},value: {msg.value},{type(msg.value)}')
        msg_value = from_json(VmsMsg, msg.value)
        logger.info(f'MSG_BODY:{type(msg_value.MSG_BODY)},MSG_JOB_TYPE:{type(msg_value.MSG_JOB_TYPE)}')
        if msg_value.MSG_JOB_TYPE in self._handlers:
            self._handlers[msg_value.MSG_JOB_TYPE](msg_value.MSG_BODY)
    '''
    def dispatch_message(self,msg):
        logger.debug(f'msg topic: {msg.topic},partition: {msg.partition},value: {msg.value},{type(msg.value)}')
        msg_value = json.loads(msg.value)
        job_type = msg_value.get('MSG_JOB_TYPE')
        if job_type in self._handlers:
            body_json = json.dumps(msg_value.get('MSG_BODY'),cls=ConsumerEncoder).encode('utf-8')
            #logger.info(f'''job_type:{job_type},msg_body:{body_json}''')
            self._handlers[job_type](body_json)


    def __handle_message(self):
        if self._consumer.connect():
            start = time.time()
            try:
                result = self._consumer.receive(1000)
                for _, msgs in result.items():
                    for msg in msgs:  # TODO msg recv
                        self.dispatch_message(msg)
            except Exception as e:
                logger.warning(f"[handle_message] err:{e}, trace:{traceback.format_exc()}")
            interval_time = (time.time()  - start) * 1000
            if interval_time > 500:
                logger.debug(f'[handle_message] recive overtime:{interval_time} ms')
        else:
            logger.warning('vmsconsumer fail to connect server:{self._server}')


    def __register_handler(self, typ: str, handler):
        self._handlers[typ] = handler

    def __init_connect(self):
        self._consumer = KafkaMsgConsumer(self._server, self._topic)  # TODO offset commit
  
    def start_consumer(self):
        # 梅山非开发环境都打开kafka功能
        if ConfigManage().is_ms_scene() and not ConfigManage().is_dev_env():
            self.__register_handler(VMS_CONTROL_TYPE, vms_control_handler)
            self.__register_handler(VMS_CRANE_TYPE, vms_crane_handler)
            self.__register_handler(VMS_WI_TYPE, vms_wi_handler)
            self.__init_connect()
            self._consumer_thread.start()

if __name__ == '__main__':
    init_vms_consumer_log()
    VmsConsumers().start_consumer()
