import os
import math
import matplotlib.pyplot as plt
from common.logger import logger
from common.singleton import Singleton
from common.constant import CRANE_LANE_THETA
from cache.crane_calib import increase_mark_count, get_mark_count, get_abrasion_table


class AbrasionTable(metaclass=Singleton):
    def __init__(self):
        # 基本结构为dict={'mark_true':num, 'mark_false':num ...}

        # utm反解的GT与检测得到的mark最大误差设置为5米
        self._max_error_of_mark = 5.0

        # mark基本间隔为5m
        self._mark_interval = 5.0

        # 计算识别成功率时，防止分母为0
        self._kEpsilon = 1e-10

    def update_mark(self, mark_from_utm, mark_from_camera):
        # 计算mark_gt时，四舍五入取整数值
        mark_from_utm = int(mark_from_utm/self._mark_interval + 0.5) * \
            int(self._mark_interval)

        # 判断mark位置与桥吊位置是否匹配, 根据检测结果更新磨损表
        matched = 'true' if abs(
            mark_from_utm - mark_from_camera) < self._max_error_of_mark else 'false'
        increase_mark_count(f'{mark_from_utm}_{matched}')

        # 位置匹配，返回True，否则返回False
        return mark_from_utm == mark_from_camera

    def plot_coastline(self, mark_start, mark_end, threshold=0.2):
        # input：起始mark，终止mark，磨损阈值。要求mark_start大于mark_end
        # 绘制海岸线mark磨损示意图，蓝色块表示未磨损，红色表示已磨损

        fig_coastline = plt.figure()
        coastline = fig_coastline.add_subplot(111, aspect='equal')

        # ellipsis_flag表示是否对当前mark进行省略
        ellipsis_flag = False
        # erasion_num_flag表示是否显示当前磨损数字
        erasion_num_flag = True
        # x_coordinate_revise对省略mark的坐标偏移进行修正
        x_coordinate_revise = 0
        # y_coordinate_revise对换行mark的y坐标进行修正，初始为1
        y_coordinate_revise = 1
        lines = 0

        for mark in range(mark_start, mark_end, -5):
            # mark从大到小取值，间隔5m
            mark_info = self.get_mark_info(mark)
            # 准确度低于阈值，判定为磨损状态，显示红色
            if sum(mark_info[:2]) > 5 and mark_info[2] < threshold:
                color = 'red'
                ellipsis_flag = False
            # mark未磨损，且已有省略号，修正x轴坐标
            elif ellipsis_flag == True:
                x_coordinate_revise += 5
            # mark未磨损，且前一个mark同样未磨损，修改flag，当前mark显示为省略号
            elif mark != mark_start and color == 'blue':
                ellipsis_flag = True
            # mark未磨损，前一个mark磨损，当前mark显示为蓝色
            else:
                color = 'blue'
            if mark != mark_start and (mark-mark_start) % 200 == 0:
                lines += 1
                y_coordinate_revise += 5
                x_coordinate_revise = 200*lines
                print(mark, 'turn lines!')

            # 绘制磨损块
            if ellipsis_flag:
                plt.text(
                    mark + x_coordinate_revise + 2.5,
                    y_coordinate_revise+1,
                    '...',
                    fontsize=6,
                    verticalalignment="center",
                    horizontalalignment="center"
                )
                erasion_num_flag = True
            elif erasion_num_flag or color == 'blue':
                coastline.add_patch(
                    plt.Rectangle(
                        (mark+x_coordinate_revise,
                         y_coordinate_revise),  # (x,y)矩形左下角
                        5,  # width长
                        1,  # height宽
                        color=color,
                        alpha=0.5
                    )
                )
                plt.text(
                    mark+x_coordinate_revise,
                    y_coordinate_revise+1,
                    str(mark),
                    fontsize=3,
                    verticalalignment="bottom",
                    horizontalalignment="right"
                )
                # 如果当前mark磨损且已显示数字，flag置False，未磨损时还原为True
                if color == 'red':
                    erasion_num_flag = False
                else:
                    erasion_num_flag = True
            else:
                coastline.add_patch(
                    plt.Rectangle(
                        (mark+x_coordinate_revise,
                         y_coordinate_revise),  # (x,y)矩形左下角
                        5,  # width长
                        1,  # height宽
                        color=color,
                        alpha=0.5
                    )
                )
                erasion_num_flag = True

        # 添加图例
        plt.text(
            mark_start-189,
            y_coordinate_revise+2.5,
            'abrasion',
            fontsize=3,
            verticalalignment="center",
            horizontalalignment="right"
        )
        coastline.add_patch(
            plt.Rectangle(
                (mark_start-195, y_coordinate_revise+2),  # (x,y)矩形左下角
                5,  # width长
                1,  # height宽
                color='red',
                alpha=0.5
            )
        )
        plt.text(
            mark_start-189,
            y_coordinate_revise+4.5,
            'normal',
            fontsize=3,
            verticalalignment="center",
            horizontalalignment="right"
        )
        coastline.add_patch(
            plt.Rectangle(
                (mark_start-195, y_coordinate_revise+4),  # (x,y)矩形左下角
                5,  # width长
                1,  # height宽
                color='blue',
                alpha=0.5
            )
        )

        # 坐标范围
        plt.xlim(mark_start+5, mark_start-195)
        plt.ylim(0, (lines+1)*5+5)

        # 隐藏轴脊
        for position in ['left', 'top', 'right', 'bottom']:
            coastline.spines[position].set_visible(False)
        # 隐藏刻度
        coastline.set(xticks=(),
                      yticks=()
                      )
        # 保存图像
        fig_coastline.savefig(
            f'coastline_{mark_start}_to_{mark_end}.png', dpi=300, bbox_inches='tight')

    def get_mark_info(self, mark):
        # 用于查询表格信息，输入用于查询的mark值;
        # mark不存在无法查询时，返回三个0;
        # 成功查询，返回正确次数、错误次数及成功率
        success_num = get_mark_count(f'{mark}_true')
        fail_num = get_mark_count(f'{mark}_false')
        return success_num, fail_num, float(success_num)/float(fail_num + success_num + self._kEpsilon)

    def get_table(self):
        # 返回完整表格
        return get_abrasion_table()

    def get_abrasion_mark(self, threshold=0.2):
        # 返回表格中整体磨损情况，返回检测成功率低于threshold的marks
        raw_table = self.get_table()
        abrasion_marks = {}
        visited = set()
        for mark_key in raw_table:
            mark = mark_key.split('_')[0]
            if mark in visited:
                continue

            visited.add(mark)
            mark_info = self.get_mark_info(mark)
            if sum(mark_info[:2]) > 100 and mark_info[2] < threshold:
                abrasion_marks[mark] = mark_info

        return abrasion_marks
