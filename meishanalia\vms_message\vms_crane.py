import json
from meishanalia.vms_message.msgs import from_json, CraneMsg
from meishanalia.model import table_info
from meishanalia.model import crane_info


def vms_crane_handler(msg):
    crane = from_json(CraneMsg, msg)
    crane_record = table_info.CraneInfo()
    for attr in dir(crane_record):
        if not attr.startswith('_') and hasattr(crane, attr):
            setattr(crane_record, attr, getattr(crane, attr))
    crane_info.insert(crane_record)

if __name__ == '__main__':
    #crane = '{"WI_NO":10,"DISPATCH_TIME":"2021-04-21 14:02:17.270394"}'
    crane = '{"ID": 38, "CRANE_ID": "CR38", "LANE_NO": "3", "BAY1": "70", "BAY2": "77", "BAY3": "79", "VESSEL_DIRECTION": "R", "VESSEL_REFNO": "COSCR2"}'
    vms_crane_handler(crane)
