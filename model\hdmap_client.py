import time
from collections import defaultdict

import grpc
import proto.hdmaplib_pb2 as hdmaplib_pb2
import proto.hdmaplib_pb2_grpc as hdmaplib_pb2_grpc
from config.config_manage import ConfigManage


class HdmapClient(object):
    def __init__(self):
        hdmap_server = ConfigManage().get_config()['service']['hdmap_server']
        self._hdmap_server_url = f'{hdmap_server["ip"]}:{hdmap_server["port"]}'
        self._hdmap_channel = grpc.insecure_channel(target=self._hdmap_server_url,
                                                       options=[('grpc.keepalive_timeout_ms', 1000)])
        self._hdmap_stub = hdmaplib_pb2_grpc.HdmapLibStub(self._hdmap_channel)

    def get_electronic_fence(self, lock_station_names, forward_extend_dist = 18, backward_extend_dist = 25, scene = "port_yongzhou") -> dict:
        def fetch_electronic_fence(request):
            response = self._hdmap_stub.GetElectronicFence(request)
            fence_data = defaultdict(list)
            for e_f in response.electronic_fences:
                fence = [(point.x, point.y) for point in e_f.points]
                fence_data[e_f.poi_name].append(fence)
            return fence_data

        request = hdmaplib_pb2.GetElectronicFenceRequest()
        request.time_stamp = time.time()
        request.business_scene = scene
        request.lock_names.extend(lock_station_names)

        # 获取正向锁站范围
        request.forward_extend_dist = forward_extend_dist
        request.backward_extend_dist = backward_extend_dist
        ret = fetch_electronic_fence(request)

        # 获取设置潮汐引桥时锁站范围
        request.forward_extend_dist = backward_extend_dist
        request.backward_extend_dist = forward_extend_dist
        reverse_ret = fetch_electronic_fence(request)

        # 组合数据
        for poi_name, fences in reverse_ret.items():
            ret[poi_name].extend(fences)

        return ret


if __name__ == '__main__':
    lock_station_names = ['PSTP_501', 'PSTP_502', 'PSTP_601', 'PSTP_602']
    print(HdmapClient().get_electronic_fence(lock_station_names))
