import sys
import time, datetime
from google.protobuf.text_format import MessageToString
from typing import List
from common.logger import logger
from common.common_util import time_valid
from common.singleton import Singleton
from cache.client import CacheClient
from proto.cache_pb2 import ServerNode

ONLINE_SERVER_NODE_KEY = 'config:online_server_node'

class ServerNodeCacheBase(object):
    def __init__(self):
        self._redis = CacheClient()

    def get_server_node(self, ex=None):
        name = f"{ONLINE_SERVER_NODE_KEY}"
        server_nodes = self._redis.hgetvals_proto(name, ServerNode)
        if ex:
            cur_time = time.time()
            server_nodes = [node for node in server_nodes if node is not None and time_valid(node.timestamp_ms * 1000000, ex, cur_time)]
        server_nodes = sorted(server_nodes, key=lambda server: server.node_name)
        return server_nodes

    def set_server_node(self, config):
        if len(config.node_name) == 0:
            logger.warning(f'invalid node name:{config.node_name}')
            return False
        name = f"{ONLINE_SERVER_NODE_KEY}"
        key = f"{config.node_name}"
        config.timestamp_ms = int(time.time() * 1000)
        config.timestamp = datetime.datetime.fromtimestamp(config.timestamp_ms/1000).strftime("%Y-%m-%d %H:%M:%S")
        return self._redis.hset_proto(name, key, config)

    def update_server_node(self, node_name, mode):
        return self.set_server_node(ServerNode(node_name = node_name, mode = mode))

    def delete_server_node(self, config):
        if len(config.node_name) == 0:
            logger.warning(f'invalid node name:{config.node_name}')
            return False
        name = f"{ONLINE_SERVER_NODE_KEY}"
        key = f"{config.node_name}"
        return self._redis.hdel(name, key)

    def check_server_node_in(self, config):
        if len(config.node_name) == 0:
            logger.warning(f'invalid node name:{config.node_name}')
            return False
        name = f"{ONLINE_SERVER_NODE_KEY}"
        key = f"{config.node_name}"
        if not self._redis.hexists(name, key):
            return False
        server_nodes = self.get_server_node()
        for node in server_nodes:
            if node.node_name == config.node_name:
                return True
        return False

    def check_server_node_valid(self, config):
        if len(config.node_name) == 0:
            logger.warning(f'invalid node name:{config.node_name}')
            return False
        #若是CLUSTER_MASTER,不能有其他SINGLE或CLUSTER_MASTER存在(当前节点为CLUSTER_MASTER，其他只能为CLUSTER_SLAVE或CLUSTER_BACK)
        #若是CLUSTER_SLAVE,不能有其他SINGLE存在(当前节点为CLUSTER_SLAVE，其他只能为CLUSTER_MASTER或CLUSTER_BACK或CLUSTER_SLAVE)
        #若是CLUSTER_BACK,不能有其他的SINGLE存在(当前节点为CLUSTER_BACK，其他只能为CLUSTER_MASTER或CLUSTER_BACK或CLUSTER_SLAVE)
        #若是SINGLE_MASTER,除了SINGLE_BACK不能有其他节点存在(当前节点为SINGLE_MASTER，其他只能为SINGLE_BACK)
        #若是SINGLE_BACK,除了SINGLE_MASTER\SINGLE_BACK不能有其他节点存在
        #若是SINGLE,不能有其他节点存在
        server_nodes = self.get_server_node()
        for server in server_nodes:
            if server.node_name == config.node_name:
                continue
            if (config.mode == ServerNode.CLUSTER_MASTER and (server.mode != ServerNode.CLUSTER_SLAVE and server.mode != ServerNode.CLUSTER_BACK)) or \
                (config.mode == ServerNode.CLUSTER_SLAVE and (server.mode != ServerNode.CLUSTER_MASTER and server.mode != ServerNode.CLUSTER_SLAVE and server.mode != ServerNode.CLUSTER_BACK)) or \
                (config.mode == ServerNode.CLUSTER_BACK and server.mode != ServerNode.CLUSTER_MASTER and server.mode != ServerNode.CLUSTER_SLAVE and server.mode != ServerNode.CLUSTER_BACK) or \
                (config.mode == ServerNode.SINGLE_MASTER and server.mode != ServerNode.SINGLE_BACK) or \
                (config.mode == ServerNode.SINGLE_BACK and (server.mode != ServerNode.MASTER and server.mode != ServerNode.SINGLE_BACK)) or \
                config.mode == ServerNode.SINGLE:
                logger.warning(f"check server node fail:input server node:{MessageToString(config, as_one_line=True)}\n"
                             f"conflict server node:{MessageToString(server, as_one_line=True)}")
                return False
        return True

class ServerNodeCache(ServerNodeCacheBase, metaclass=Singleton):
      pass

if __name__ == '__main__':
    pass