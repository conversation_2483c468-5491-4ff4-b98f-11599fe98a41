import traceback
from typing import Dict
import datetime
#from dataclasses_json import dataclass_json
from sqlalchemy import Column, Integer, String, DateTime, Float, event, Text, func
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from common.common_util import to_dict, from_dict
from meishanalia.common.constant import TABLE_TOS_CMD, TABLE_CMD_INFO, TABLE_CONTROL_CMD, TABLE_TRUCK_INFO, TABLE_CRANE_INFO, TABLE_CRANE_INFO_DEBUG

Base = declarative_base()
Base.to_dict = to_dict
Base.from_dict = from_dict


def default_time():
    return datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")

class TosWisInfo(Base):
    __tablename__ = TABLE_TOS_CMD
    ID = Column('ID', Integer, primary_key=True, autoincrement=True)
    TRUCK_NO = Column('TRUCK_NO', String)
    # TRUCK_SEQ = Column('TRUCK_SEQ', Integer)
    TRUCK_SEQ = Column('TRUCK_SEQ', String)
    WI_NO = Column('WI_NO', Integer)
    WI_ID = Column('WI_ID', Integer)
    CTN_NO = Column('CTN_NO', String)
    EQUIT_TYPE = Column('EQUIT_TYPE', String)
    TEU = Column('TEU', Integer)
    FROM_POS = Column('FROM_POS', String)
    TO_POS = Column('TO_POS', String)
    WI_TYPE = Column('WI_TYPE', String)
    WI_ACT = Column('WI_ACT', String)
    WI_STATUS = Column('WI_STATUS', String)
    TWIN_FLAG = Column('TWIN_FLAG', String)
    TWIN_WI_NO = Column('TWIN_WI_NO', Integer)
    TWIN_CTN_NO = Column('TWIN_CTN_NO', String)
    TRUCK_POS = Column('TRUCK_POS', String)
    DISPATCH_TIME = Column('DISPATCH_TIME', DateTime)
    CANCEL_TIME = Column('CANCEL_TIME', DateTime)
    CONFIRMED_TIME = Column('CONFIRMED_TIME', DateTime)
    REMARK1 = Column('REMARK1', String)
    REMARK2 = Column('REMARK2', String)
    REMARK3 = Column('REMARK3', String)
    CTN_WEIGHT = Column('CTN_WEIGHT', Integer)
    POW_NAME = Column('POW_NAME', String)
    LOCK_FLAG = Column('LOCK_FLAG', String)
    LOCK_PAVILION = Column('LOCK_PAVILION', String)
    #current time:default=datetime.datetime.now,default=default_time,default=func.now()
    #fixed server start time:default=datetime.datetime.now(),default=default_time()
    INSERT_TIME = Column('INSERT_TIME', DateTime, default=default_time)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime, default=default_time, onupdate=default_time)
    VERSION = Column('VERSION', Integer, default=1)
    RESET_VERSION = Column('RESET_VERSION', Integer, default=0)
    VESSEL_POS = Column('VESSEL_POS', String)
    CTRL_SPEED = Column('CTRL_SPEED', Integer, default=0)
    CTRL_STOP_NODE = Column('CTRL_STOP_NODE', String)
    CTRL_WAIT_NODE = Column('CTRL_WAIT_NODE', String)
    CTRL_LOCK_NODE = Column('CTRL_LOCK_NODE', String)
    CTRL_QUEUE_NODE = Column('CTRL_QUEUE_NODE', String)
    CTRL_TURN_NODE = Column('CTRL_TURN_NODE', String)
    CTRL_CHECK_NODE = Column('CTRL_CHECK_NODE', String)
    CTRL_ACTION = Column('CTRL_ACTION', String)
    CTRL_CUR_WIS = Column('CTRL_CUR_WIS', String)
    CTRL_CUR_WI = Column('CTRL_CUR_WI', String)
    CTRL_CUR_IDS = Column('CTRL_CUR_IDS', String)
    CTRL_CUR_ID = Column('CTRL_CUR_ID', String)
    CTRL_INSERT_TIME = Column('CTRL_INSERT_TIME', DateTime)
    CTRL_UPDATE_TIME = Column('CTRL_UPDATE_TIME', DateTime)
    CTRL_STATUS = Column('CTRL_STATUS', String)
    CTRL_VERSION = Column('CTRL_VERSION', Integer, default=0)
    CTRL_RESET_VERSION = Column('CTRL_RESET_VERSION', Integer, default=0)
    CMD_TYPE = Column('CMD_TYPE', String, default='TOS_CMD')

#update will not execute
@event.listens_for(TosWisInfo, 'before_update')
def increment_seq(mapper, connection, target):
    target.VERSION += 1


class TosWiInfo(Base):
    __tablename__ = TABLE_TOS_CMD + '_EX'
    ID = Column('ID', Integer, primary_key=True, autoincrement=True)
    TRUCK_NO = Column('TRUCK_NO', String)
    # TRUCK_SEQ = Column('TRUCK_SEQ', Integer)
    TRUCK_SEQ = Column('TRUCK_SEQ', String)
    WI_NO = Column('WI_NO', Integer)
    WI_ID = Column('WI_ID', Integer)
    CTN_NO = Column('CTN_NO', String)
    EQUIT_TYPE = Column('EQUIT_TYPE', String)
    TEU = Column('TEU', Integer)
    FROM_POS = Column('FROM_POS', String)
    TO_POS = Column('TO_POS', String)
    WI_TYPE = Column('WI_TYPE', String)
    WI_ACT = Column('WI_ACT', String)
    WI_STATUS = Column('WI_STATUS', String)
    TWIN_FLAG = Column('TWIN_FLAG', String)
    TWIN_WI_NO = Column('TWIN_WI_NO', Integer)
    TWIN_CTN_NO = Column('TWIN_CTN_NO', String)
    TRUCK_POS = Column('TRUCK_POS', String)
    DISPATCH_TIME = Column('DISPATCH_TIME', DateTime)
    CANCEL_TIME = Column('CANCEL_TIME', DateTime)
    CONFIRMED_TIME = Column('CONFIRMED_TIME', DateTime)
    REMARK1 = Column('REMARK1', String)
    REMARK2 = Column('REMARK2', String)
    REMARK3 = Column('REMARK3', String)
    CTN_WEIGHT = Column('CTN_WEIGHT', Integer)
    POW_NAME = Column('POW_NAME', String)
    LOCK_FLAG = Column('LOCK_FLAG', String)
    LOCK_PAVILION = Column('LOCK_PAVILION', String)
    INSERT_TIME = Column('INSERT_TIME', DateTime)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime)
    VERSION = Column('VERSION', Integer)
    RESET_VERSION = Column('RESET_VERSION', Integer)
    VESSEL_POS = Column('VESSEL_POS', String)

class DcWiTnfo(Base):
    __tablename__ = TABLE_CMD_INFO
    ID = Column('ID', Integer, primary_key=True, autoincrement=True)
    TRUCK_NO = Column('TRUCK_NO', String)
    # TRUCK_SEQ = Column('TRUCK_SEQ', Integer)
    TRUCK_SEQ = Column('TRUCK_SEQ', String)
    WI_NO = Column('WI_NO', Integer, default=0)
    WI_ID = Column('WI_ID', Integer, default=0)
    CTN_NO = Column('CTN_NO', String)
    EQUIT_TYPE = Column('EQUIT_TYPE', String)
    TEU = Column('TEU', Integer)
    FROM_POS = Column('FROM_POS', String)
    TO_POS = Column('TO_POS', String)
    WI_TYPE = Column('WI_TYPE', String)
    WI_ACT = Column('WI_ACT', String)
    WI_STATUS = Column('WI_STATUS', String)
    TWIN_FLAG = Column('TWIN_FLAG', String)
    TWIN_WI_NO = Column('TWIN_WI_NO', Integer)
    TWIN_CTN_NO = Column('TWIN_CTN_NO', String)
    TRUCK_POS = Column('TRUCK_POS', String)
    DISPATCH_TIME = Column('DISPATCH_TIME', DateTime)
    CANCEL_TIME = Column('CANCEL_TIME', DateTime)
    CONFIRMED_TIME = Column('CONFIRMED_TIME', DateTime)
    REMARK1 = Column('REMARK1', String)
    REMARK2 = Column('REMARK2', String)
    REMARK3 = Column('REMARK3', String)
    CTN_WEIGHT = Column('CTN_WEIGHT', Integer)
    POW_NAME = Column('POW_NAME', String)
    LOCK_FLAG = Column('LOCK_FLAG', String)
    LOCK_PAVILION = Column('LOCK_PAVILION', String)
    INSERT_TIME = Column('INSERT_TIME', DateTime)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime, onupdate=default_time)
    START_TIME = Column('START_TIME', DateTime)
    ARRIVE_TIME = Column('ARRIVE_TIME', DateTime)
    FINISH_TIME = Column('FINISH_TIME', DateTime)
    TRUCK_STATUS = Column('TRUCK_STATUS', String)
    VERSION = Column('VERSION', Integer, default=0)
    RESET_VERSION = Column('RESET_VERSION', Integer, default=0)
    VESSEL_POS = Column('VESSEL_POS', String)
    CTRL_SPEED = Column('CTRL_SPEED', Integer, default=0)
    CTRL_WAIT_NODE = Column('CTRL_WAIT_NODE', String)
    CTRL_LOCK_NODE = Column('CTRL_LOCK_NODE', String)
    CTRL_QUEUE_NODE = Column('CTRL_QUEUE_NODE', String)
    CTRL_TURN_NODE = Column('CTRL_TURN_NODE', String)
    CTRL_CHECK_NODE = Column('CTRL_CHECK_NODE', String)
    CTRL_ACTION = Column('CTRL_ACTION', String)
    CTRL_CUR_WIS = Column('CTRL_CUR_WIS', String)
    CTRL_CUR_WI = Column('CTRL_CUR_WI', String)
    CTRL_CUR_IDS = Column('CTRL_CUR_IDS', String)
    CTRL_CUR_ID = Column('CTRL_CUR_ID', String)
    CTRL_INSERT_TIME = Column('CTRL_INSERT_TIME', DateTime)
    CTRL_UPDATE_TIME = Column('CTRL_UPDATE_TIME', DateTime)
    CTRL_STATUS = Column('CTRL_STATUS', String, default='DISPATCH')
    CTRL_VERSION = Column('CTRL_VERSION', Integer, default=0)
    CTRL_RESET_VERSION = Column('CTRL_RESET_VERSION', Integer, default=0)
    CMD_TYPE = Column('CMD_TYPE', String, default='TOS_CMD')
    # ARRIVE_AREA = Column('ARRIVE_AREA', String, default='DEFAULT_SIDE')

class ControlInfo(Base):
    __tablename__ = TABLE_CONTROL_CMD
    ID = Column('ID', Integer, primary_key=True, autoincrement=True)
    TRUCK_NO = Column('TRUCK_NO', String)
    SPEED = Column('SPEED', Integer)
    STOP_NODE = Column('STOP_NODE', String)
    WAIT_NODE = Column('WAIT_NODE', String)
    LOCK_NODE = Column('LOCK_NODE', String)
    QUEUE_NODE = Column('QUEUE_NODE', String)
    TURN_NODE = Column('TURN_NODE', String)
    CHECK_NODE = Column('CHECK_NODE', String)
    ACTION = Column('ACTION', String)
    CUR_WIS = Column('CUR_WIS', String)
    CUR_WI = Column('CUR_WI', String)
    CUR_IDS = Column('CUR_IDS', String)
    CUR_ID = Column('CUR_ID', String)
    INSERT_TIME = Column('INSERT_TIME', DateTime)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime)
    STATUS = Column('STATUS', String)
    VERSION = Column('VERSION', Integer, default=1)

class TruckInfo(Base):
    __tablename__ = TABLE_TRUCK_INFO
    ID = Column('ID', Integer, primary_key=True, autoincrement=True)
    TRUCK_NO = Column('TRUCK_NO', String)
    POS_X = Column('POS_X', Float, default=0.0)
    POS_Y = Column('POS_Y', Float, default=0.0)
    ALTITUDE = Column('ALTITUDE', Float, default=0.0)
    ANGEL = Column('ANGEL', Float, default=0.0)
    CUR_POS = Column('CUR_POS', String, default='0')
    POWER_TYPE = Column('POWER_TYPE', String, default='ELECTRIC')
    REST_OIL = Column('REST_OIL', Float, default='0')
    REST_ELECTRIC = Column('REST_ELECTRIC', Float, default=0.0)
    SPEED = Column('SPEED', Float, default='0')
    SENSOR_STATUS = Column('SENSOR_STATUS', String, default='null')
    TRUCK_MODE = Column('TRUCK_MODE', Integer, default=2)
    ACTION = Column('ACTION', String, default='IDEL')
    CUR_WIS = Column('CUR_WIS', String, default='0')
    CUR_IDS = Column('CUR_IDS', String, default='0')
    TRUCK_STATUS = Column('TRUCK_STATUS', String, default='0')
    INSERT_TIME = Column('INSERT_TIME', DateTime)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime)
    NODE_PATH = Column('NODE_PATH', Text)
    PATH1 = Column('PATH1', Text)
    PATH2 = Column('PATH2', Text)


class CraneInfo(Base):
    __tablename__ = TABLE_CRANE_INFO
    ID = Column('ID', Integer, primary_key=True)
    CRANE_ID = Column('CRANE_ID', String)
    LANE_NO = Column('LANE_NO', String)
    VESSEL_DIRECTION = Column('VESSEL_DIRECTION', String)
    BERTH = Column('BERTH', String)
    DRIVE_DIRECTION = Column('DRIVE_DIRECTION', String)
    BRIDGE_UP = Column('BRIDGE_UP', String)
    BRIDGE_DOWN = Column('BRIDGE_DOWN', String)
    INSERT_TIME = Column('INSERT_TIME', DateTime)
    UPDATE_TIME = Column('UPDATE_TIME', DateTime)
    def _asdict(self):
        return {c.key: getattr(self, c.key)
                for c in inspect(self).mapper.column_attrs}