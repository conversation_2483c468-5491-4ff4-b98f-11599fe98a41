import struct
from decimal import Decimal
from enum import Enum

from proto.antenna_pb2 import PushChassisInfoRequest
from cache.chassis_cache import ChassisCache


def insert_vehicle_chassis(vehicle_name, chassis_detail):
    ChassisCache().rpush_chassis_info_by_vehicle_name(vehicle_name, chassis_detail)


def zfill(v):
    chars = hex(v).strip("0x")
    if v == 0:
        chars = "00"
    elif len(chars)<2:
        if v<16:
            chars = "0"+chars
        else:
            chars = chars + "0"
    return chars


def bigintToHex(data:int, sep_len=8):
    base=2**sep_len - 1
    res=""
    while data>base:
        v=data & base
        data>>=sep_len
        res=res+zfill(v)
    res=res+ zfill(data & base)
    if len(res)<16:
        res=res+"0"*(16-len(res))
    return res


def transform_chssis_data_to_file_format(proto_obj:PushChassisInfoRequest, first_timestamp, to_str=True):
    res_list = []
    for can_data_cluster in  proto_obj.can_data_cluster:
        chan_num=int.from_bytes(bytes=can_data_cluster.chan_num, byteorder="little", signed=True)
        for can_data in can_data_cluster.can_data:
            cur_timestamp = can_data_cluster.header.timestamp_sec + float(int.from_bytes(bytes=can_data.offset_milli_secs, byteorder="little", signed=False))/1000
            msg_id="{:#x}x".format(can_data.msg_id)[2:]
            # data=struct.pack("<q", can_data.data).hex()
            data=bigintToHex(can_data.data)
            data="%016s" % data
            data=" ".join([data[i:i + 2] for i in range(0, len(data), 2)]).upper()
            res_list.append({
                "timestamp":cur_timestamp,
                "msg_id":msg_id,
                "data":data,
                "chan_num":chan_num+1,
            })

    res_list.sort(key=lambda x: x.get("timestamp"))
    res_str_list = []
    for res in res_list:
        res["timestamp"] = res["timestamp"]-first_timestamp
        if res["timestamp"]<0:
            continue
        res_str = f"{Decimal(res['timestamp']).quantize(Decimal('0.000000'))} {res['chan_num']} {res['msg_id']} Rx d 8 {res['data']}"
        res_str_list.append(res_str)
    return first_timestamp, res_str_list


class WeekdayEnum(Enum):
    Mon = 0
    Tue = 1
    Wed = 2
    Thu = 3
    Fri = 4
    Sat = 5
    Sun = 6


class MonthEnum(Enum):
    Jan = 1
    Feb = 2
    Mar = 3
    Apr = 4
    May = 5
    Jun = 6
    Jul = 7
    Aug = 8
    Sep = 9
    Oct = 10
    Nov = 11
    Dec = 12

if __name__ == '__main__':
    # 1) simulation  PushChassisInfo
    # from model import chassis_detail
    # import time
    # while True:
    #     time.sleep(1)
    #     insert_vehicle_chassis("JT802", chassis_detail)

    print(WeekdayEnum.Mon)
    print(WeekdayEnum.Mon.name)
    print(WeekdayEnum.Mon.value)
    print(WeekdayEnum(3))
    print(MonthEnum(6).name)