import time
import uuid
import traceback
from redis import WatchError

from proto import cache_pb2
from cache.client import CacheClient, LocalClient
from common.singleton import Singleton
from common.logger import logger

CHASSIS_LIST_PREFIX = "chassis-list-key"
VEHICLE_COROUTINE_LOCK_KEY = "vehicle-coroutine-lock-key"
VEHICLE_COROUTINE_LOCK_EXPIRE_TIME = 70
CHASSIS_LIST_MAXiMUM = 1024


class ChassisCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()  #

    def query_chassis_info_by_vehicle_name(self, vehicle_name):
        key='%s:%s' % (CHASSIS_LIST_PREFIX, vehicle_name)
        chassis_list=self._redis.lrange(key,0,-1)
        if not chassis_list:
            return []
        return chassis_list

    def delete_chassis_info(self, vehicle_name, delete_length=None):
        key='%s:%s' % (CHASSIS_LIST_PREFIX, vehicle_name)
        length=self._redis.llen(key)
        self._redis.ltrim(key, (delete_length if delete_length else length), -1)

    def rpush_chassis_info_by_vehicle_name(self, vehicle_name, chassis_info):
        key='%s:%s' % (CHASSIS_LIST_PREFIX, vehicle_name)
        self._redis.rpush(key, chassis_info)

    def acquire_lock(self, vehicle_name, acquire_time=10, timeout=VEHICLE_COROUTINE_LOCK_EXPIRE_TIME, retries=5):
        identifier = str(uuid.uuid4())
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, vehicle_name)
        end = time.time() + acquire_time
        i = 0
        while time.time()<end:
            if self._redis.ttl(key) == -1:
                # permanent -> short-lived
                self._redis.expire(key, timeout)
                logger.debug(f"acquire_lock here1,vehicle_name:{vehicle_name}")
            elif self._redis.ttl(key)>0:
                # never expire if antena-server is running and lock hasn't been released
                self._redis.expire(key, timeout)
                logger.debug(f"acquire_lock here2,vehicle_name:{vehicle_name}")
            elif self._redis.setnx(key, identifier):
                # the previous lock has been released
                self._redis.expire(key, timeout)
                logger.debug(f"acquire_lock here3,vehicle_name:{vehicle_name},identifier:{identifier}")
                return identifier
            i += 1
            if i >= retries:
                logger.debug(f"acquire_lock:vehicle_name:{vehicle_name} fail to get invalid acquire_lock")
                return None
            time.sleep(0.2)
        logger.debug(f"acquire_lock here4,vehicle_name:{vehicle_name}")
        return None

    def release_lock(self, vehicle_name, identifier):
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, vehicle_name)
        pipe = self._redis.pipeline(True)
        while True:
            try:
                logger.debug(f"release_lock,before watch vehicle_name:{vehicle_name},identifier:{identifier}")
                pipe.watch(key)
                key_identifier = pipe.get(key)
                logger.debug(f"release_lock,after watch vehicle_name:{vehicle_name},identifier:{identifier}, key_identifier:{key_identifier}")
                if key_identifier is not None and key_identifier.decode() == identifier:
                    pipe.multi()
                    pipe.delete(key)
                    pipe.execute()
                    logger.debug(f"release_lock, sucessfully vehicle_name:{vehicle_name},identifier:{identifier}, key_identifier:{key_identifier}")
                    return True
                pipe.unwatch()
                break
            except Exception as e:
                logger.warning(f"release_lock error:vehicle_name:{vehicle_name}, err{e}, trace:{traceback.format_exc()}")
                time.sleep(0.2)
        logger.debug(f"release_lock, fail vehicle_name:{vehicle_name},identifier:{identifier}")
        return False

    def release_all_lock(self):
        """
        release all locks when one of our antenna-servers start.
        then the server  which preempt the lock  when  perform vehicle's task.
        :return:
        """
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, "*")
        for coroutine_key in self._redis.keys(key):
            self._redis.delete(coroutine_key)

    def limit_chassis_length(self, vehicle_name):
        key='%s:%s' % (CHASSIS_LIST_PREFIX, vehicle_name)
        length=self._redis.llen(key)
        if length>=CHASSIS_LIST_MAXiMUM:
            self.delete_chassis_info(vehicle_name, length-60)

    def get_can_length(self, vehicle_name):
        key = '%s:%s' % (CHASSIS_LIST_PREFIX, vehicle_name)
        return self._redis.llen(key)


    def show_lock(self, vehicle_name):
        identifier = None
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, vehicle_name)
        pipe = self._redis.pipeline(True)
        try:
            pipe.watch(key)
            identifier = pipe.get(key)
            pipe.unwatch()
        except Exception as e:
            logger.warning(f"release_lock error:vehicle_name:{vehicle_name}, err{e}, trace:{traceback.format_exc()}")
        logger.info(f"show_lock:vehicle_name:{vehicle_name},identifier:{identifier}")

    def release_vehicle_lock(self,vehicle_name):
        """
        release all locks when one of our antenna-servers start.
        then the server  which preempt the lock  when  perform vehicle's task.
        :return:
        """
        logger.info(f"release_vehicle_lock:vehicle_name:{vehicle_name}")
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, vehicle_name)
        self._redis.delete(key)

    def get_vehicle_lock_ttl_time(self,vehicle_name):
        key='%s:%s' % (VEHICLE_COROUTINE_LOCK_KEY, vehicle_name)
        return self._redis.ttl(key)
