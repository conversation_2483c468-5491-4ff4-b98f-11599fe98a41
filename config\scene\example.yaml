db:
  oracle:
    # local、remote(local无需指定其他字段内容，会自动捕获本地oracle地址；remote需额外设置其他字段)
    mode: local
    username: fabutech
    password: fabu1#%@
    ip: ***********
    port: 1521
    sid: wrjk
    # max_connections可不设置, 默认2
    max_connections: 2

  mysql:
    # pool_size可不设置, 默认10;max_overflow可不设置, 默认5
    # 普通数据记录
    data_mysql:
      dsn: mysql://read_write_user:OD3vd4v992GfppR9@*************:3306/nb_port_test?charset=utf8
      pool_size: 10
      max_overflow: 5
    # 监控模块
    monitor_mysql:
      dsn: mysql://root:fabu124@************:3306/nb_port_prod?charset=utf8
      pool_size: 10
      max_overflow: 5
    # 车辆底盘信息
    chassis_mysql:
      dsn: mysql://read_write_user:OD3vd4v992GfppR9@*************:3306/nb_port_test_chassis?charset=utf8
      pool_size: 10
      max_overflow: 5
    # 仅万象场景需额外设置tos_mysql，且tos_mysql需额外设置mode
    tos_mysql:
      # local、remote(local无需设置dsn，会自动捕获本地地址；remote需额外设置dsn)
      mode: local
      pool_size: 100
      max_overflow: 5

  redis:
    # single、cluster(两模式互斥)
    mode: single
    # single配置ip、prot
    ip: localhost
    port: 6379
    # cluster配置sentinel
    sentinel:
      master: redis1
      nodes:
        - *************:26380
        - *************:26381
        - *************:26382
    # 0(pro)、8(pre),15(fat),iecs(6)
    database: 0
    # share_database生产(pro)预发(pre)用0,其他模式(DEV、FAT)下与database保持一致
    share_database: 0
    # plc_share_database 在portman及DEV模式下与database保持一致,在生产(pro)、预发(pre)、测试(FAT)模式下用8
    plc_share_database: 8
    password: fabu

  kafka:
      producer_enable: True
      consumer_enable: True
      group_id: my_group_new
      #sasl认证相关配置
      sasl_config:
        sasl_enable: False
      nodes:
        #公司kafka
        - ************:9093

server_attribute:
  #如果是非分布式为默认为Node1-1;分布式主节点为'Node1-1';其他节点格式为'Node[major_num]-[minor_num]',如Node1-2
  #1.cluster_[]:集群,可以与其他多节点同时运行tos_bridge
  #  (1):cluster_master:集群主节点,除了启动tos_bridge,同时启动其他服务
  #  (2):cluster_slave:集群子节点,只启动tos_bridge服务
  #  (3):cluster_back:备节点,当cluster_master所在服务主机挂掉时，所有服务会被cluster_back接管
  #2.single:只单机运行tos_bridge(注意redis的database其他服务没有在用),同时运行其他服务
  nodes:
    -
      node_name: Node1-1
      mode: cluster_master
    -
      node_name: Node1-2
      mode: cluster_slave

keepalive_services:
  # 与下列服务保活
  nb-ecs:
    enable: True
    acceptable_delay_ms: 1000
  ship-load-sch:
    enable: True
    acceptable_delay_ms: 30000