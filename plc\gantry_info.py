import struct
from typing import Optional

from common.logger import logger
from proto.cache_pb2 import GantryInfo


def parse_from(msg, recv_timestamp_ns) -> Optional[GantryInfo]:
    template = '!HHhihHHiH'
    if len(msg) == 24:
        template = '!HHhihHHiHh'
    elif len(msg) == 30:
        template = '!HHhihHHiHhHHH'
    try:
        info = GantryInfo()
        tmp = struct.unpack(template, msg)
        info.msg_no = tmp[0]
        info.gantry_no = tmp[1]
        info.cps_guide = 20000
        info.g_pos = tmp[3]
        info.h_pos = tmp[4]
        info.spr_size = tmp[5]
        info.spr_state = (tmp[6] > 0)
        info.t_pos = tmp[7]
        info.block_nul = tmp[8]
        if len(msg) == 24 or len(msg) == 30:
            info.smm1_pos = tmp[9]
        if len(msg) == 30:
            info.rtg_o_tag = tmp[10]
        info.recv_timestamp = recv_timestamp_ns
        return info
    except struct.error as e:
        logger.warning(f"parse gantry info error {e}, msg len {len(msg)}")
        return None
