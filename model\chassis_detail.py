import os
import datetime
import json
import math
import platform
import time
import traceback
import re
from json.decoder import WHITESPACE
from operator import and_

#import pymysql
from google.protobuf import json_format
from google.protobuf.json_format import MessageTo<PERSON>son, Parse, ParseDict
from google.protobuf.message import Message
from sqlalchemy import Column, Integer, String, text, DateTime, UniqueConstraint, Text, union_all,LargeBinary
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy_utils import database_exists, create_database

from model.connection import ChassisMysqlSession
from common.logger import logger
from common.constant import DEFAULT_PAGE, DEFAULT_PAGE_NUMBER, CHASSIS_DATA_DIR, CHASSIS_SUPPOR_BY_FILE, CHASSIS_SAVE_TIME_DAYS
from proto.system_status_cache_pb2 import SystemStatusDetail
from config.config_manage import ConfigManage
#pymysql.install_as_MySQLdb()


Base = declarative_base()

def default_time():
    return datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")


class BaseChassisInfo(object):
    _mapper = {}

    @staticmethod
    def table_number():
        delta = datetime.datetime.now() - datetime.datetime.strptime("1970-01-01 00:00:00", "%Y-%m-%d %H:%M:%S")
        hours = delta.days * 24 + delta.seconds / 3600
        return math.floor(hours / 12) % 15

    @staticmethod
    def model(number):
        if number and not 14>=number>=0:
            raise ValueError(f"table_number-{number} not in [0, ... 14]")
        if number not in BaseChassisInfo._mapper:
            table_name = "chassis_info%02d" %  number
            class_name = "ChassisInfo%02d"  %  number
            ModelClass = type(class_name, (BaseChassisInfo, Base),
                         dict(
                            __tablename__=table_name,
                            # __module__=__name__,
                            id=Column('id', Integer, primary_key=True, autoincrement=True),
                            vehicle_name = Column('vehicle_name', String(32), index=True),
                            chassis_detail = Column('chassis_detail', LargeBinary(length=(2**32)-1) ),#blob
                            insert_datetime = Column('insert_datetime', DateTime, default=default_time, onupdate=default_time,
                                         index=True),
                            __table_args__=(
                                    UniqueConstraint('vehicle_name', 'insert_datetime', name='uix_api_vehicle_dt'),
                            )
                         )
                       )
            BaseChassisInfo._mapper[number] = ModelClass
        return BaseChassisInfo._mapper[number]

    @staticmethod
    def cur_model():
        return BaseChassisInfo.model(BaseChassisInfo.table_number())


    @classmethod
    def insert_cls_chassis_detail(cls, vehicle_name, chassis_detail):
        if not is_support():
            return
        session = ChassisMysqlSession().acquire()
        try:
            chassis_info = cls(vehicle_name=vehicle_name,
                                 chassis_detail=chassis_detail)
            session.add(chassis_info)
            session.commit()
        except Exception as e:
            logger.warning(f"Insert Chassis Info err{e}, trace:{traceback.format_exc()}")
        finally:
            session.close()
        return


def insert_file_chassis_detail(vehicle_name, chassis_detail):
    file_path = f"{CHASSIS_DATA_DIR}{os.sep}{vehicle_name}_{datetime.datetime.now().strftime('%Y-%m-%d:%H')}.log"
    logger.debug(f"[insert_file_chassis_detail] vehicle_name:{vehicle_name} write file_path:{file_path}")
    # bytes save to file
    with open(file_path, "ab") as ff:
        ff.write(f"@@@[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]".encode('utf-8'))
        ff.write(chassis_detail)


def is_support():
    return True


def create_all_chassis_table():
    my_class_list = [BaseChassisInfo.model(x) for x in range(15)]
    Base.metadata.create_all()
    return my_class_list


def get_by_vehicle_and_dt_in_file(vehicle_name=None, start_dt=None, end_dt=None):
    if start_dt.year != end_dt.year or start_dt.month != end_dt.month or start_dt.day != end_dt.day or \
        start_dt.hour != end_dt.hour:
        logger.warning(f"[get_by_vehicle_and_dt_in_file] start_dt and end_dt")
        return []
    time_string = start_dt.strftime("%Y-%m-%d:%H")
    file_path = f"{CHASSIS_DATA_DIR}/{vehicle_name}_{time_string}.log"
    chassis_byte_list = []
    i = 0
    if os.path.exists(file_path):
        with open(file_path, "rb") as ff:
            file_bytes = ff.read()
            file_segs = file_bytes.split(b'@@@[')
            try:
                for seg in file_segs:
                    if seg:
                        i += 1
                        chassis_insert_time = seg[:19].decode('utf-8')
                        chassis_byte = seg[20:]
                        insert_time = datetime.datetime.strptime(chassis_insert_time, "%Y-%m-%d %H:%M:%S")
                        if insert_time >= start_dt and insert_time <= end_dt:
                            logger.info(f"in time:{i}---{chassis_insert_time}")
                            chassis_byte_list.append(chassis_byte)
                        else:
                            logger.info(f"out time:{i}---{chassis_insert_time}")
            except Exception as e:
                logger.warning(f"[get_by_vehicle_and_dt_in_file] err:{e}, trace:{traceback.format_exc()}")
    else:
        logger.warning(f"[get_by_vehicle_and_dt_in_file] no file_path:{file_path}")
    return chassis_byte_list

def get_by_vehicle_and_dt_in_sql(vehicle_name=None, start_dt=None, end_dt=None, page=DEFAULT_PAGE, page_number=DEFAULT_PAGE_NUMBER):
    session = ChassisMysqlSession().acquire()
    try:
        last_query = None
        for i in range(15):
            cls = BaseChassisInfo.model(i)
            filters = []
            if vehicle_name:
                filters.append(cls.vehicle_name == vehicle_name)
            if start_dt:
                filters.append(cls.insert_datetime >= start_dt)
            if end_dt:
                filters.append(cls.insert_datetime <= end_dt)
            #offset当前页的开始偏移,limit每页返回的数量
            query=session.query(cls).filter(*filters).offset(page*page_number).limit(page_number)
            if last_query:
                last_query=last_query.union_all(query)
            else:
                last_query=query
        ret = last_query.all()
    except Exception as e:
        logger.warning(f"Insert SystemStatus err{e}, trace:{traceback.format_exc()}")
        return []
    else:
        return ret
    finally:
        session.close()


def get_by_vehicle_and_dt(vehicle_name=None, start_dt=None, end_dt=None, page=DEFAULT_PAGE, page_number=DEFAULT_PAGE_NUMBER):
     if CHASSIS_SUPPOR_BY_FILE:
        return get_by_vehicle_and_dt_in_file(vehicle_name=vehicle_name, start_dt=start_dt, end_dt=end_dt)
     else:
        return get_by_vehicle_and_dt_in_sql(vehicle_name=vehicle_name, start_dt=start_dt, end_dt=end_dt, page=page, page_number=page_number)

def insert_chassis_detail(vehicle_name, chassis_detail):
    if CHASSIS_SUPPOR_BY_FILE:
        insert_file_chassis_detail(vehicle_name,chassis_detail)
        return
    cls=BaseChassisInfo.model(BaseChassisInfo.table_number())
    cls.insert_cls_chassis_detail(vehicle_name, chassis_detail)

def delete_abandoned_file_data():
    if not is_support():
        return
    try:
        delete_time = datetime.datetime.now() - datetime.timedelta(days=CHASSIS_SAVE_TIME_DAYS)
        for file_name in os.listdir(CHASSIS_DATA_DIR):
            if (match:=re.match(r'A?T(\d+)_(\d+)-(\d+)-(\d+):(\d+).log', file_name)) is not None:
                year,month,day,hour = int(match[2]),int(match[3]),int(match[4]),int(match[5])
                file_time = datetime.datetime(year=year, month=month, day=day, hour=hour)
                if file_time <= delete_time:
                    file_abs_path = os.path.join(CHASSIS_DATA_DIR, file_name)
                    logger.debug(f"[delete_abandoned_file_data delete] file:{file_abs_path}")
                    os.remove(file_abs_path)
    except Exception as e:
        logger.warning(f"Delete file err{e}, trace:{traceback.format_exc()}")
    return

def delete_abandoned_data():
    if CHASSIS_SUPPOR_BY_FILE:
        delete_abandoned_file_data()
        return
    if not is_support():
        return
    session = ChassisMysqlSession().acquire()
    try:
        for i in range(1, 3):
            abandoned_number = (BaseChassisInfo.table_number() + i) % 15
            logger.info(f"Current table_number 【{BaseChassisInfo.table_number()}】。 Abandoed table_number 【{abandoned_number}】")
            cls=BaseChassisInfo.model(abandoned_number)
            if not session.query(cls).first():
                logger.info(f"NO NEED TO TRUNCAT {cls.__tablename__}")
                continue
            # session.execute(f"truncate  table {cls.__tablename__}")  # mysql 8.0 failed  mysql 5.7 success
            session.execute(f"drop  table {cls.__tablename__}")
            session.commit()
            engine = session.bind
            cls = BaseChassisInfo.model(i)
            logger.info(f"Create table【{cls.__tablename__}】if not exists。")
            Base.metadata.create_all(engine)
    except Exception as e:
        logger.warning(f"Insert SystemStatus err{e}, trace:{traceback.format_exc()}")
        session.rollback()
    finally:
        session.close()
    return


if __name__ == '__main__':
    import pymysql
    pymysql.install_as_MySQLdb()
    session = ChassisMysqlSession().acquire()
    engine=session.bind
    # 1), 删除数据库
    '''
    if  database_exists(engine.url):
        drop_database(engine.url)
    '''
    # 2) init db
    if  not database_exists(engine.url):
        create_database(engine.url)
        print(f"Create database【{engine.url}】。")
    # 3) init chassis table
    for x in range(15):
        cls=BaseChassisInfo.model(x)
        print(f"Create table【{cls.__tablename__}】if not exists。")
    Base.metadata.create_all(engine)
    print(f"Finish create tables.")

    # # 3) test insert data
    # from common.common_util import insert_vehicle_chassis, to_dict, transform_chssis_data_to_file_format
    # import sys
    # mod = sys.modules[__name__]
    # 
    # import platform
    # if "win" in platform.platform().lower():
    #     from antenna_pb2 import PushChassisInfoRequest
    #     from fabupilot.canbus.can_data_pb2 import CanData, CanDataCluster
    #     request = PushChassisInfoRequest()
    #     request.vehicle_name = "fabutest"
    #     bytes_data = "1009".encode()
    #     can_data = CanData()
    #     can_data.offset_milli_secs = bytes_data
    #     can_data.msg_id = 1
    #     bytes_data2 = "1009".encode()
    #     can_data.data = 10081
    #     can_data_cluster = CanDataCluster()
    #     can_data_cluster.can_data.extend([can_data])
    #     can_data_cluster.chan_num = b'1'
    #     request.can_data_cluster.extend([can_data_cluster])
    #
    #     # chassis_info = MessageToJson(request)
    #     chassis_cahce_list = []
    #     chassis_info = request.SerializeToString()
    #     print(chassis_info)
    #     for i in range(63):
    #         time.sleep(1)
    #         if i==3:
    #             break
    #         chassis_cahce_list.append(chassis_info)
    #     insert_chassis_detail("JT800", b"@@@".join(chassis_cahce_list))
    # #     # 5) test delete table data
    #     delete_abandoned_data()
    #     print("Delete abondoned data finished.")
    #
    #
    #     # 4) test query data
    #     print("Query data:")
    #     first_timestamp=None
    #     all_data = get_by_vehicle_and_dt("JT800")
    #     for res in all_data:
    #         data = to_dict(res)["chassis_detail"]
    #         for single_str in data.split(b"@@@"):
    #             request=PushChassisInfoRequest()
    #             request.ParseFromString(single_str)
    #             first_timestamp, data_list = transform_chssis_data_to_file_format(request, first_timestamp)
    #             for data in data_list:
    #                 print(data)
                # break
            # break
