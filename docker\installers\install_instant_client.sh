#!/usr/bin/env bash

# Fail on first error.
set -e

cd "$(dirname "${BASH_SOURCE[0]}")"

SKEL_URI="http://release.fabu.ai/deps/instantclient-basiclite-linux.x64-********.0dbru.zip"

mkdir -p /opt/oracle
cd /opt/oracle
wget ${SKEL_URI}
# cp /tmp/installers/instantclient-basiclite-linux.x64-********.0dbru.zip .
unzip instantclient-basiclite-linux.x64-********.0dbru.zip
sh -c "echo /opt/oracle/instantclient_18_5 > \
      /etc/ld.so.conf.d/oracle-instantclient.conf"
ldconfig
