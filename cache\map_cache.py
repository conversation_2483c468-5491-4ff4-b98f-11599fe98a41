from pickle import LIST
import time
from typing import Optional, Iterable, List
from cache.client import Cache<PERSON>lient, PscpRedisClient
from cache.config_cache import ConfigCache
from cache.scheduling_cache import UturnPointSchCache
from common.singleton import Singleton
from proto import cache_pb2, antenna_pb2
from proto.antenna_pb2 import MapAdjustment, CloudBlockInfo
from proto.cache_pb2 import ParkSlotArriveMsg, CpsAlignMsg, GPSInfo
from config.config_manage import ConfigManage
from common.common_util import time_valid
from common.logger import logger
from google.protobuf import text_format
from yongzhou.common.constant import station_id_map

MANUAL_MAP_ADJUSTMENT_KEY = 'manual-map-adjustment'
AUTO_MAP_ADJUSTMENT_KEY = 'auto-map-adjustment'
AUTO_MAP_ADJUSTMENT_GANTRY_AREA_KEY = 'auto-map-adjustment-gantry-area-key'
AUTO_MAP_ADJUSTMENT_YARD_LINE_AREAR_KEY = 'auto-map-adjustment-yard-line-area-key'
AUTO_MAP_ADJUSTMENT_GANTRY_BLOCK_KEY = 'auto-map-adjustment-gantry-block-key'
AUTO_MAP_ADJUSTMENT_ROAD_SIDE_BLOCK_KEY = 'auto-map-adjustment-road-side-block-key'
MAP_SHIP_KEY = 'manual-map-ship'
BRIDGE_DRIVING_MODE_KEY = 'bridge-driving-mode-set'
BRIDGE_JUNCTION_LINKS_KEY = 'bridge-junction-links-set'
TIDE_ROAD_DIRECTION_KEY = 'tide-road-direction-set'
AUTO_MAP_FORBID_DRIVING_AREA_KEY = 'auto-map-forbid-driving-area-key'
AUTO_MAP_STACK_FORBID_AREA_PREFIX = 'auto-map-stack-forbid-area-prefix'
AUTO_MAP_STACK_WORK_AREA_PREFIX = 'auto-map-stack-work-area-prefix'
AUTO_MAP_FORBID_STOP_AREA_KEY = 'auto-map-forbid-stop-area-key'
AUTO_MAP_PARK_INFO_PREFIX = 'auto-map-park-info-prefix'
AUTO_MAP_SLOT_INFO_PREFIX = 'auto-map-slot-info-prefix'
AUTO_CPS_ALIGN_MSG_PREFIX = 'auto-cps-align-msg-prefix'
AUTO_MAP_PARK_CONTROL_INFO_PREFIX = 'auto-map-park-control-info-prefix'
ARRIVE_TO_PARK_CONTROL_MSG_QUEUE_PREFIX = 'arrive-to-park-control-msg-queue'
CLOUD_BLOCK_INFO_KEY = 'cloud_block_info'

CRANE_SHIP_KEY = 'crane-ship'
REAL_MAP_SHIP_KEY = 'real-map-ship'
#箱区属性
MAP_YARD_INFO_KEY = 'yard-info-set'
GANTRY_CROSS_INFO_KEY = 'gantry-cross-info-set'
# 地图区域到达信息
AREA_ARRIVE_INFO_SET = 'area-arrive-info-set'
# 梅山GPS数据
AUTO_MAP_MS_GPS_INFO_PREFIX = 'auto-map-ms-gps-info-prefix'
# 梅山龙门吊GPS数据(转为utm)
AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX = 'auto-map-ms-gantry-gps-info-prefix'

class MapCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._pscp_redis = PscpRedisClient()
        self._expire_time = 180  # seconds

    def set_bridge_driving_mode(self, proto):
        return self._redis.set_proto(BRIDGE_DRIVING_MODE_KEY, proto)

    def get_bridge_driving_mode(self):
        return self._redis.get_proto(BRIDGE_DRIVING_MODE_KEY, antenna_pb2.BridgesDrivingMode)

    def get_all_map_adjustment(self, truck_no=None):
        # return self.get_manual_map_adjustment() + self.get_auto_map_adjustment() + self.get_road_side_map_adjustment()
        return self.get_manual_map_adjustment(truck_no)

    def get_manual_map_adjustment(self, truck_no=None, filter_fleet=True, filter_id=False):
        proto = self._redis.get_proto(MANUAL_MAP_ADJUSTMENT_KEY, cache_pb2.MapAdjustmentSet)
        if proto is None:
            return [], 0

        block_id = proto.block_id
        map_adjustments = [m for m in proto.map_adjustments]

        if ConfigManage().is_yz_cs_scene():
            filter_fleet = False
            filter_id = True

        if truck_no is not None and filter_fleet:
            vehicle_fleet_id = ConfigCache().get_vehicle_fleet(truck_no)
            if vehicle_fleet_id is not None:
                vehicle_fleet_id = int(vehicle_fleet_id)
                # adjustment.fleet_id为-1，表示对全场生效
                map_adjustments = list(
                    filter(
                        lambda map_adjustment: map_adjustment.fleet_id == vehicle_fleet_id or map_adjustment.fleet_id == -1 \
                        or vehicle_fleet_id in map_adjustment.fleet_ids or -1 in map_adjustment.fleet_ids
                        , map_adjustments
                    )
                )

        if filter_id:
            map_adjustments = list(filter(lambda map_adjustment:
                                          ((map_adjustment.adjustment_reason != MapAdjustment.AdjustmentReason.OVERTAKE_DISPATCH) and (map_adjustment.adjustment_reason != MapAdjustment.AdjustmentReason.PB_DISPATCH)) or (map_adjustment.uuid.endswith(truck_no)), map_adjustments))
        return map_adjustments, block_id

    def get_auto_map_adjustment(self):
        proto = self._redis.get_proto(AUTO_MAP_ADJUSTMENT_KEY, cache_pb2.MapAdjustmentSet)
        if proto is None:
            return []
        return [m for m in proto.map_adjustments]

    def set_manual_map_adjustment(self, map_adjustments, timestamp=0):
        proto = cache_pb2.MapAdjustmentSet()
        proto.map_adjustments.extend(map_adjustments)
        proto.block_id = timestamp
        return self._redis.set_proto(MANUAL_MAP_ADJUSTMENT_KEY, proto)

    def clear_manual_map_adjustment(self):
        return self._redis.delete(MANUAL_MAP_ADJUSTMENT_KEY)

    def set_auto_map_adjustment(self, map_adjustments):
        proto = cache_pb2.MapAdjustmentSet()
        proto.map_adjustments.extend(map_adjustments)
        return self._redis.set_proto(AUTO_MAP_ADJUSTMENT_KEY, proto)

    def get_road_side_map_adjustment(self):
        proto = self._redis.get_proto(AUTO_MAP_ADJUSTMENT_ROAD_SIDE_BLOCK_KEY, cache_pb2.MapAdjustmentSet)
        if proto is None:
            return []
        return [m for m in proto.map_adjustments]

    def set_road_side_map_adjustment(self, map_adjustments):
        proto = cache_pb2.MapAdjustmentSet()
        proto.map_adjustments.extend(map_adjustments)
        return self._redis.set_proto(AUTO_MAP_ADJUSTMENT_ROAD_SIDE_BLOCK_KEY, proto)

    def get_all_map_ship(self, truck_no=None):
        proto = self._redis.get_proto(MAP_SHIP_KEY, cache_pb2.MapShipSet)
        if proto is None:
            return []

        if truck_no is None:
            return [m for m in proto.map_ships]

        uturn_point = UturnPointSchCache().get_vehicle_uturn_point_info(truck_no)
        if uturn_point is None:
            return [m for m in proto.map_ships]

        for map_ship in proto.map_ships:
            if map_ship.uuid == uturn_point.vessel_uuid:
                if uturn_point.HasField('virtual_lane_index'):
                    map_ship.virtual_lane_index = uturn_point.virtual_lane_index
                if uturn_point.HasField('virtual_lane_rank'):
                    map_ship.virtual_lane_rank = uturn_point.virtual_lane_rank
                break
        return [m for m in proto.map_ships]

    def get_map_ship_by_uuid(self, uuid):
        proto = self._redis.get_proto(MAP_SHIP_KEY, cache_pb2.MapShipSet)
        if proto is None:
            return None
        for ship in proto.map_ships:
            if uuid == ship.uuid:
                return ship
        return None

    def set_all_map_ship(self, map_ships):
        proto = cache_pb2.MapShipSet()
        proto.map_ships.extend(map_ships)
        return self._redis.set_proto(MAP_SHIP_KEY, proto)

    def get_map_yard_info(self):
        return self._redis.get_proto(MAP_YARD_INFO_KEY, antenna_pb2.YardInfoSet)
    def set_map_yard_info(self, yard_infos: antenna_pb2.YardInfoSet):
        return self._redis.set_proto(MAP_YARD_INFO_KEY, yard_infos)
    def get_yard_line_block_area(self):
        return self._redis.get_proto(AUTO_MAP_ADJUSTMENT_YARD_LINE_AREAR_KEY, cache_pb2.CraneMapAdjustmentConfig)

    def set_yard_line_block_area(self, area: cache_pb2.CraneMapAdjustmentConfig):
        return self._redis.set_proto(AUTO_MAP_ADJUSTMENT_YARD_LINE_AREAR_KEY, area)

    def get_gantry_block_area(self):
        return self._redis.get_proto(AUTO_MAP_ADJUSTMENT_GANTRY_AREA_KEY, cache_pb2.CraneMapAdjustmentConfig)

    def set_gantry_block_area(self, area: cache_pb2.CraneMapAdjustmentConfig):
        return self._redis.set_proto(AUTO_MAP_ADJUSTMENT_GANTRY_AREA_KEY, area)

    def get_gantry_cross_info(self) -> Optional[cache_pb2.GantryCrossInfoSet]:
        return self._redis.get_proto(GANTRY_CROSS_INFO_KEY, cache_pb2.GantryCrossInfoSet)
    
    def set_gantry_cross_info(self, cross_info_set: cache_pb2.GantryCrossInfoSet):
        return self._redis.set_proto(GANTRY_CROSS_INFO_KEY, cross_info_set)

#############################stack forbid##########################################
    def hset_stack_forbid_area(self, area:cache_pb2.StackForbidInfo):
        name = f"{AUTO_MAP_STACK_FORBID_AREA_PREFIX}"
        #key =  f"{area.truck_no}"
        key =  f"{area.block_code}" + ("%02d" % (area.bay_no))
        if True and area.status == 0:
            return self._redis.hdel(name, key)
        return self._redis.hset_proto(name, key, area)

    def hset_stack_forbid_area_dict(self, area_dict):
        area = cache_pb2.StackForbidInfo()
        for field in area.DESCRIPTOR.fields:
            if field.name in area_dict:
                setattr(area, field.name, area_dict.get(field.name))
        area.recv_timestamp = int(time.time() * 1000)
        self.hset_stack_forbid_area(area)

    def hset_stack_forbid_areas(self, areas:List[cache_pb2.StackForbidInfo]):
        name = f"{AUTO_MAP_STACK_FORBID_AREA_PREFIX}"
        if len(areas) == 0:
            return self._redis.delete(name)
        recv_timestamp = int(time.time() * 1000)
        for area in areas:
            area.recv_timestamp = recv_timestamp
            self.hset_stack_forbid_area(area)
        return True

    def hget_stack_forbid_area(self, owner: str) -> Optional[cache_pb2.StackForbidInfo]:
        name = f"{AUTO_MAP_STACK_FORBID_AREA_PREFIX}"
        key = f"{owner}"
        return self._redis.hget_proto(name, key, cache_pb2.StackForbidInfo)

    def hget_stack_forbid_areas(self) -> List[Optional[cache_pb2.StackForbidInfo]]:
        name = f"{AUTO_MAP_STACK_FORBID_AREA_PREFIX}"
        results = self._redis.hgetvals_proto(name, cache_pb2.StackForbidInfo)
        results = [result for result in results if result is not None]
        return results

    def get_forbid_driving_area(self):
        return self._redis.get_proto(AUTO_MAP_FORBID_DRIVING_AREA_KEY, cache_pb2.ForbidDrivingAreaSet)

    def get_forbid_driving_valid_area(self,filter_owner=None,filter_area='',ex=5):
        areas = []
        area_set = self.get_forbid_driving_area()
        if area_set and time_valid(area_set.timestamp_ms * 1000000,ex=ex, cur_time=time.time()):
            #areas = [area for area in area_set.forbid_driving_areas if area.owner != filter_owner]
            if str(filter_area).endswith('B') or str(filter_area).endswith('W'):
                areas = [area for area in area_set.forbid_driving_areas if area.work_pos != filter_area[:-1]]
            else:
                areas = [area for area in area_set.forbid_driving_areas]
        return areas

    def set_forbid_driving_area(self,areas:cache_pb2.ForbidDrivingAreaSet):
        return self._redis.set_proto(AUTO_MAP_FORBID_DRIVING_AREA_KEY, areas)


######stacker forbid stop area
    def hset_stack_work_area(self,area):
        name = f"{AUTO_MAP_STACK_WORK_AREA_PREFIX}"
        key = f"{area.yard_id}"
        if len(area.bayset) == 0:
            return self._redis.hdel(name, key)
        return self._redis.hset_proto(name, key, area)

    def hget_stack_work_areas(self):
        name = f"{AUTO_MAP_STACK_WORK_AREA_PREFIX}"
        results = self._redis.hgetvals_proto(name, cache_pb2.StackWorkAreaInfo)
        results = [result for result in results if result is not None]
        return results

    def set_forbid_stop_area(self,areas:cache_pb2.ForbidStopAreaSet):
        return self._redis.set_proto(AUTO_MAP_FORBID_STOP_AREA_KEY, areas)

    def get_stack_forbid_stop_area(self):
        return self._redis.get_proto(AUTO_MAP_FORBID_STOP_AREA_KEY, cache_pb2.ForbidStopAreaSet)

    def get_forbid_stop_area(self):
        areas = []
        area_set = self.get_stack_forbid_stop_area()
        if area_set:
            areas = [area for area in area_set.forbid_stop_areas]
        return areas

############################################################################################

    def hget_cloud_block_info(self, truck_no):
        key = f"{truck_no}"
        return self._pscp_redis.hget_proto(CLOUD_BLOCK_INFO_KEY, key, CloudBlockInfo)

    def get_all_real_map_ship(self):
        proto = self._redis.get_proto(REAL_MAP_SHIP_KEY, cache_pb2.RealMapShipSet)
        if proto:
            return [m for m in proto.real_map_ships]
        return []


    def set_all_real_map_ship(self, map_ships):
        proto = cache_pb2.RealMapShipSet()
        proto.real_map_ships.extend(map_ships)
        return self._redis.set_proto(REAL_MAP_SHIP_KEY, proto)

    def get_real_map_ship_by_id(self,ship_id):
        ship_set = self.get_real_map_ship()
        if ship_set:
            for ship in ship_set.ships:
                if ship_id == ship.ship_id:
                    return ship
        return None

    def hset_crane_ship(self,crane_id, info:cache_pb2.CraneShip):
        name = f"{CRANE_SHIP_KEY}"
        key = f"{crane_id}"
        return self._redis.hset_proto(name, key, info)

    def hget_crane_ship(self,crane_id):
        name = f"{CRANE_SHIP_KEY}"
        key = f"{crane_id}"
        return self._redis.hget_proto(name, key, cache_pb2.CraneShip)

    def hget_crane_ships(self):
        name = f"{CRANE_SHIP_KEY}"
        results = self._redis.hgetall_proto(name, cache_pb2.CraneShip)
        results = [result for result in results if result is not None]
        return results

    def get_bridge_junction_links(self):
        return self._redis.get_proto(BRIDGE_JUNCTION_LINKS_KEY, antenna_pb2.BridgeJunctionLinks)

    def set_tide_road_direction(self, tide_road_direction: antenna_pb2.TideRoadDirection):
        return self._redis.set_proto(TIDE_ROAD_DIRECTION_KEY, tide_road_direction)

    def get_tide_road_direction(self):
        return self._redis.get_proto(TIDE_ROAD_DIRECTION_KEY, antenna_pb2.TideRoadDirection)

#############################park info##########################################
    def hset_park_info_dict(self, park_info_dict):
        park_info = cache_pb2.ParkInfo()
        for field in park_info.DESCRIPTOR.fields:
            if field.name in park_info_dict:
                setattr(park_info, field.name, park_info_dict.get(field.name))
        self.hset_park_info(park_info)

    def hset_park_info(self, park_info: cache_pb2.ParkInfo):
        name = f"{AUTO_MAP_PARK_INFO_PREFIX}"
        key =  f"{park_info.truck_no}"
        return self._redis.hset_proto(name, key, park_info)

    def hget_park_info(self, truck_no: str) -> Optional[cache_pb2.ParkInfo]:
        name = f"{AUTO_MAP_PARK_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.ParkInfo)

    def hget_all_park_info(self):
        name = f"{AUTO_MAP_PARK_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.ParkInfo)
        results = [result for result in results if result is not None]
        return results

    def hset_slot_info_dict(self, slot_info_dict):
        slot_info = cache_pb2.SlotInfo()
        for field in slot_info.DESCRIPTOR.fields:
            if field.name in slot_info_dict:
                setattr(slot_info, field.name, slot_info_dict.get(field.name))
        self.hset_slot_info(slot_info)

    def hset_slot_info(self, slot_info: cache_pb2.SlotInfo):
        name = f"{AUTO_MAP_SLOT_INFO_PREFIX}"
        key =  f"{slot_info.truck_no}"
        return self._redis.hset_proto(name, key, slot_info)

    def hget_slot_info(self, truck_no: str) -> Optional[cache_pb2.SlotInfo]:
        name = f"{AUTO_MAP_SLOT_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.SlotInfo)

    def hget_all_slot_info(self):
        name = f"{AUTO_MAP_SLOT_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.SlotInfo)
        results = [result for result in results if result is not None]
        return results

    def hset_park_slot_arrive_info_dict(self, park_slot_arrive_info_dict):
        park_slot_arrive_info = cache_pb2.ParkSlotArriveMsg()
        for field in park_slot_arrive_info.DESCRIPTOR.fields:
            if field.name in park_slot_arrive_info_dict:
                setattr(park_slot_arrive_info, field.name, park_slot_arrive_info_dict.get(field.name))
        self.hset_park_slot_arrive_info(park_slot_arrive_info)

    def hset_park_slot_arrive_info(self, park_slot_arrive_info:cache_pb2.ParkSlotArriveMsg):
        name = f"{AUTO_MAP_PARK_CONTROL_INFO_PREFIX}"
        key =  f"{park_slot_arrive_info.truck_no}"
        return self._redis.hset_proto(name, key, park_slot_arrive_info)

    def hget_park_slot_arrive_info(self, truck_no: str) -> Optional[cache_pb2.ParkSlotArriveMsg]:
        name = f"{AUTO_MAP_PARK_CONTROL_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.ParkSlotArriveMsg)

    def hget_all_park_slot_arrive_info(self):
        name = f"{AUTO_MAP_PARK_CONTROL_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.ParkSlotArriveMsg)
        results = [result for result in results if result is not None]
        return results

    def hdel_park_slot_arrive_info(self, truck_no: str):
        name = f"{AUTO_MAP_PARK_CONTROL_INFO_PREFIX}"
        key = f"{truck_no}"
        return self._redis.hdel(name, key)

    def push_park_slot_arrive_msg(self, msg):
        key = f"{ARRIVE_TO_PARK_CONTROL_MSG_QUEUE_PREFIX}"
        return self._redis.rpush_proto(key, msg)

    def clear_park_slot_arrive_msg(self,message_len=None):
        key = f"{ARRIVE_TO_PARK_CONTROL_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_park_slot_arrive_msg(self,auto_delete=True):
        key = f"{ARRIVE_TO_PARK_CONTROL_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange_proto(key, 0, -1, cache_pb2.ParkSlotArriveMsg)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def hset_area_arrive_info(self, area_arrive_info: cache_pb2.TpArriveMsg):
        name = f"{AREA_ARRIVE_INFO_SET}"
        key =  f"{area_arrive_info.truck_no}"
        return self._redis.hset_proto(name, key, area_arrive_info)

    def hget_area_arrive_info(self, truck_no: str) -> Optional[cache_pb2.TpArriveMsg]:
        name = f"{AREA_ARRIVE_INFO_SET}"
        key = f"{truck_no}"
        return self._redis.hget_proto(name, key, cache_pb2.TpArriveMsg)

    def hdel_area_arrive_info(self, truck_no: str):
        name = f"{AREA_ARRIVE_INFO_SET}"
        key = f"{truck_no}"
        return self._redis.hdel(name, key)

#############################change info##########################################
    def hset_cps_align_msg_dict(self, cps_align_msg_dict):
        cps_align_msg = cache_pb2.CpsAlignMsg()
        for field in cps_align_msg.DESCRIPTOR.fields:
            if field.name in cps_align_msg_dict:
                print(f"name:{field.name}")
                setattr(cps_align_msg, field.name, cps_align_msg_dict.get(field.name))
        cps_align_msg.recv_timestamp = int(time.time())
        for key, value in station_id_map.items():
            if value == cps_align_msg.station_id:
                cps_align_msg.station_id = key
                break
        self.hset_cps_align_msg(cps_align_msg)

    def hset_cps_align_msg(self, cps_align_msg: cache_pb2.CpsAlignMsg):
        name = f"{AUTO_CPS_ALIGN_MSG_PREFIX}"
        key =  f"{cps_align_msg.truck_id}"
        return self._redis.hset_proto(name, key, cps_align_msg)

    def hget_cps_align_msg(self, truck_id: str) -> Optional[cache_pb2.CpsAlignMsg]:
        name = f"{AUTO_CPS_ALIGN_MSG_PREFIX}"
        key = f"{truck_id}"
        return self._redis.hget_proto(name, key, cache_pb2.CpsAlignMsg)

    def hget_all_cps_align_msg(self):
        name = f"{AUTO_CPS_ALIGN_MSG_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.CpsAlignMsg)
        results = [result for result in results if result is not None]
        return results

    def hdel_cps_align_msg(self, truck_id: str):
        name = f"{AUTO_CPS_ALIGN_MSG_PREFIX}"
        key = f"{truck_id}"
        return self._redis.hdel(name, key)
#############################gps info##########################################
    def hset_ms_gps_info(self, gps_info):
        name = f"{AUTO_MAP_MS_GPS_INFO_PREFIX}"
        key = f"{gps_info.deviceNo}"
        return self._redis.hset_proto(name, key, gps_info)

    def hget_ms_gps_info(self):
        name = f"{AUTO_MAP_MS_GPS_INFO_PREFIX}"
        results = self._redis.hgetvals_proto(name, cache_pb2.GPSInfo)
        results = [result for result in results if result is not None]
        return results

    def hset_ms_gantry_gps_info(self, gantry_gps_info: cache_pb2.GantryGPSInfo):
        name = f"{AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX}"
        key =  f"{gantry_gps_info.deviceNo}"
        self._redis.hset_proto(name, key, gantry_gps_info)
        self._redis.expire(AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX, self._expire_time)
        return True

    def hget_ms_gantry_gps_info(self, deviceNo: str) -> Optional[cache_pb2.GantryGPSInfo]:
        name = f"{AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX}"
        key = f"{deviceNo}"
        return self._redis.hget_proto(name, key, cache_pb2.GantryGPSInfo)

    def hget_all_ms_gantry_gps_info(self):
        name = f"{AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX}"
        results = self._redis.hgetall_proto(name, cache_pb2.GantryGPSInfo)
        results = [result for result in results if result is not None]
        return results

    def hdel_ms_gantry_gps_info(self, deviceNo: str):
        name = f"{AUTO_MAP_MS_GANTRY_GPS_INFO_PREFIX}"
        key = f"{deviceNo}"
        return self._redis.hdel(name, key)
############################################################################################


if __name__ == '__main__':
    import sys
    from google.protobuf.text_format import MessageToString
    usage = f"1:get_all_map_adjustment\n" + \
            f"2:get_vehicle_uturn_point_info\n" + \
            f"3:get_bridge_driving_mode\n" + \
            f"4:set_bridge_driving_mode\n" + \
            f"5:get_forbid_driving_area\n" + \
            f"6:get_forbid_driving_valid_area [filter_owner] [ex]\n" + \
            f"7:set_forbid_driving_area\n" + \
            f"8:clear manual map adjustment\n" + \
            f"9:hget_stack_forbid_areas 获取港口提供的堆高机信息\n" + \
            f"10:hset_stack_forbid_areas设置港口堆高机信息\n" + \
            f"11:clear_stack_forbid_areas清除港口堆高机信息\n" + \
            f"12:get_all_real_map_ship获取real map数据\n" + \
            f"13:hget_crane_ships获取桥吊-船信息\n"+ \
            f"14:get_bridge_junction_links获取引桥路口选道\n" + \
            f"15:hget_stack_work_areas获取梅山堆高机作业贝位信息(封禁信息)\n" + \
            f"  获取梅山堆高机作业贝位信息(封禁信息):python cache/map_cache.py 15\n" + \
            f"16:hset_stack_work_area设置梅山堆高机作业贝位信息(封禁信息)\n" + \
            f"  python cache/map_cache.py 16 堆场号 [贝位] [贝位]\n" + \
            f"  设置XG堆场10\20贝封禁:python cache/map_cache.py 16 XG 10 20\n" + \
            f"  清除XG堆场封禁:python cache/map_cache.py 16 XG\n" + \
            f"17:get_forbid_stop_area获取梅山堆高机作业贝位禁停区\n" + \
            f"18:set_forbid_stop_area设置梅山堆高机作业贝位禁停区\n" + \
            f"19:hset_cps_align_msg_dict设置换电站引导信息\n" + \
            f"  python cache/map_cache.py 19 车辆名 inposition offset device_status drive\n" + \
            f"      inposition:0:UNKNOWN_INPOSITION;1:COMPLETE_ALIGNMENT;2:NEED_ALIGNED;3:INVALID_INPOSITION;4:UNWORK_INPOSITION\n" + \
            f"      device_status:0:UNKNOWN_STATUS;1:START_ALIGN_STATUS;2:START_WORK_STATUS\n" + \
            f"      drive:0:NOT_DRIVE_AWAY;1:CAN_DRIVE_AWAY;\n" + \
            f"      如设置0.21m的对位偏差:python cache/map_cache.py 16 AT800 2 210 1 0\n" + \
            f"20:hget_cps_align_msg获取换电站引导信息\n" + \
            f"  python cache/map_cache.py 20 车辆名\n"

    if len(sys.argv) < 2:
        print(f"{usage}")
        exit()
    mask = int(sys.argv[1])
    if mask == 1:
        print(MapCache().get_all_map_adjustment())
    if mask == 2:
        print(UturnPointSchCache().get_vehicle_uturn_point_info('AT801'))
    if mask == 3:
        print(MapCache().get_bridge_driving_mode())
    if mask == 4:
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        mode_set = antenna_pb2.BridgesDrivingMode()
        mode = antenna_pb2.BridgeDrivingMode()
        mode.bridge_no = 11
        mode.driving_mode = 1
        mode_set.bridge_driving_mode.append(mode)
        mode_set.timestamp_ms = int(time.time() * 1000)
        MapCache().set_bridge_driving_mode(mode_set)
    if mask == 5:
        print(MapCache().get_forbid_driving_area())
    if mask == 6:
        filter_owner=None
        filter_area = ''
        ex=5
        if len(sys.argv) > 2:
            filter_owner = sys.argv[2]
        if len(sys.argv) > 3:
            ex = int(sys.argv[3])
        if len(sys.argv) > 4:
            filter_area = sys.argv[4]
        print(f"get_forbid_driving_valid_area:filter_owner:{filter_owner},filter_area:{filter_area},ex:{ex}")
        print(MapCache().get_forbid_driving_valid_area(filter_owner=filter_owner,filter_area=filter_area,ex=ex))
    if mask == 7:
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        area_set = cache_pb2.ForbidDrivingAreaSet()
        print(MapCache().set_forbid_driving_area(area_set))

    if mask == 8:
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        print(f"clear_manual_map_adjustment")
        MapCache().clear_manual_map_adjustment()

    if mask == 9:
        print(MapCache().hget_stack_forbid_areas())

    if mask == 10:
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        from common.common_util import MyJosnEncoder4
        from google.protobuf import json_format
        import json
        print(f"hset_stack_forbid_areas")
        infos = cache_pb2.StackForbidInfos()
        info = infos.forbids.add()
        info.truck_no = 'IGV800'
        info.block_code = '4J'
        info.bay_no = 80
        info.status = 0
        info.wi_no = -2
        info = infos.forbids.add()
        info.truck_no = 'IGV801'
        info.block_code = '3J'
        info.bay_no = 81
        info.status = 1
        info.wi_no = -1
        #infos = {"forbids": [{"truck_no": "IGV800", "block_code": "4J", "bay_no": "80", "status": 0, "wi_no": 0}, {"truck_no": "IGV801", "block_code": "3J", "bay_no": "80", "status": 0, "wi_no": 0}]}
        json_infos = json.dumps(infos, cls=MyJosnEncoder4)
        #json_infos = '{"forbids": [{"truck_no": "IGV800", "block_code": "4J", "bay_no": "80", "status": 0, "wi_no": 0}, {"truck_no": "IGV801", "block_code": "3J", "bay_no": "80", "status": 0, "wi_no": 0}]}'
        print(f"json_infos:{json_infos},type:{type(json_infos)}")
        infos_test = cache_pb2.StackForbidInfos()
        json_format.Parse(json_infos, infos_test)
        print(f"infos_test:{infos_test}")
        MapCache().hset_stack_forbid_areas(infos_test.forbids)

    if mask == 11:
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        print(f"clear_stack_forbid_areas")
        MapCache().hset_stack_forbid_areas([])

    if mask == 12:
        print(f"get_all_real_map_ship")
        print(MapCache().get_all_real_map_ship())

    if mask == 13:
        print(f"hget_crane_ships")
        print(MapCache().hget_crane_ships())

    if mask == 14:
        print(MapCache().get_bridge_junction_links())

    if mask == 15:
        print(f"hget_stack_work_areas获取梅山堆高机作业贝位信息(封禁信息):")
        areas = MapCache().hget_stack_work_areas()
        count = len(areas)
        area_idx = 0
        for area in areas:
            area_idx = area_idx + 1
            print(f"[{count}-{area_idx}] area:{MessageToString(area, as_one_line=True)}")

    if mask == 16:
        if len(sys.argv) < 3:
            print(f"{usage}")
            exit()
        yard_id = sys.argv[2]
        bayset = []
        if len(sys.argv) > 3:
            for i in range(3,len(sys.argv)):
                bayset.append(int(sys.argv[i]))
        print(f"hset_stack_work_area设置梅山堆高机作业贝位信息(封禁信息):堆场:{yard_id} 贝位:{bayset}")
        if not input("Input 'Y' to continue:") == 'Y':
            exit()
        area = cache_pb2.StackWorkAreaInfo()
        area.yard_id = yard_id
        area.recv_timestamp = int(time.time() * 1000)
        area.bayset.extend(bayset)
        print(f"set area:{MessageToString(area, as_one_line=True)}")
        MapCache().hset_stack_work_area(area)

    if mask == 17:
        print(f"get_forbid_stop_area获取梅山堆高机作业贝位禁停区:")
        areas = MapCache().get_forbid_stop_area()
        count = len(areas)
        area_idx = 0
        for area in areas:
            area_idx = area_idx + 1
            print(f"[{count}-{area_idx}] area:{MessageToString(area, as_one_line=True)}")

    if mask == 18:
        print(f"set_forbid_stop_area设置梅山堆高机作业贝位禁停区:")
        if not input("Input 'Y' to continue:") == 'Y':
            exit()
        area = cache_pb2.ForbidStopAreaSet()
        stop_area = area.forbid_stop_areas.add()
        area_time = int(time.time() * 1000)
        stop_area.timestamp_ms = area_time
        stop_area.center_point.x = 404943.7521426222
        stop_area.center_point.y = 3296240.54084911
        stop_area.work_pos = 'XF40'
        stop_area.forward_length = 9.55
        stop_area.backward_length = 9.55
        area.timestamp_ms = area_time
        print(f"set rea:{MessageToString(area, as_one_line=True)}")
        MapCache().set_forbid_stop_area(area)

    if mask == 19:
        print(f"hset_cps_align_msg_dict设置换电站引导信息:")
        if not input("Input 'Y' to continue:") == 'Y':
            exit()

        if len(sys.argv) < 7:
            print(f"{usage}")
            exit()
        plateNo = sys.argv[2]
        result = int(sys.argv[3])
        offset_a = int(sys.argv[4])
        device_status = int(sys.argv[5])
        drive = int(sys.argv[6])
        data = {
            "station_id": "MSBSS02",
            "truck_id": plateNo,
            "inposition": result,
            "offset": int(offset_a),
            "device_status": device_status,
            "drive": drive,
            "device_status_timestamp": int(time.time() * 1000),
            "drive_timestamp": time.time(),
        }
        print(f"set data:{data}")
        MapCache().hset_cps_align_msg_dict(data)

    if mask == 20:
        print(f"hget_cps_align_msg获取梅山换电站引导信息")
        vehicle_name = sys.argv[2]
        print(f"{MapCache().hget_cps_align_msg(vehicle_name)}")