import uuid
import time
from typing import List

from common.logger import logger
from common.name_converter import NameConverter

from cache.lock_cache import Lock<PERSON>ache
from model import system_status
from common.common_util import from_lonlat_to_utm, is_gantry_yard_wi, is_fabu_wi, is_container_wi
from meishanalia.common import common_util
from meishanalia.transfer_point_map.transfer_point import TransferPoint
from plc.crane_receiver import CraneInfoContainer
from proto.antenna_pb2 import RemoteCommand, StopCommand, CommandStatus, VesselDirection, ContainerSize, VehicleStatus, Position, PointPair
from proto.cache_pb2 import CommandWithStatus
from proto.system_status_cache_pb2 import SystemStatusDetail


NEED_LOCK_FLAG_LIST = ['A', 'B', 'O', 'I', 'X']  # 舱面锁， 舱内锁， 外锁， 内锁， 未知锁

class TosWi(object):
    def __init__(self, tos_wi, db  = None, tos_wis:list =[]):
        self._id = tos_wi.get('ID')
        self._wi_no = tos_wi.get('WI_NO')
        self._wi_id = tos_wi['WI_ID']
        self._truck_no = tos_wi.get('TRUCK_NO')
        self._wi_status = 'FREE'
        self._commands = list()
        self._is_cancel = False
        self._lock_cache = LockCache()
        self.parse_commands(tos_wi, db, tos_wis)

    def get_path_points(self, tos_wi, command_type=None):
        if tos_wi is None:
            return None
        stop_node = tos_wi.get('CTRL_STOP_NODE')
        if stop_node is None or len(stop_node) == 0:
            logger.warning(f"may error.vehicle name:{self._truck_no} has no stop node.tos_wi cid:{tos_wi.get('CID')}")
            return None
        nodes = stop_node.split(',')
        lock_node = tos_wi.get('CTRL_LOCK_NODE')
        queue_node = tos_wi.get('CTRL_QUEUE_NODE')
        if command_type == RemoteCommand.MOVE_TO_LOCK:
            if lock_node:
                if lock_node in nodes:
                    nodes = filter_lock(lock_node, nodes)  # 如果信通发过来的 stop_node 不包含多余的 lock_node，则此行去掉
                    nodes = nodes[:nodes.index(lock_node)+1]
                else:
                    nodes = [lock_node]
        elif command_type == RemoteCommand.MOVE_TO_UNLOCK:
            # if tos_wi.get('WI_ACT') == 'LOAD' and not lock_node:  # 2022.07 引桥休息区暂时设置在箱区，默认无后续指令去休息区前需要先去锁亭,lock_node可能为空
            #     lock_pavilion = lock_p if "-" not in (lock_p := tos_wi.get('LOCK_PAVILION')) else lock_p.split("-")[0]
            #     if lock_pavilion and lock_pavilion != "None":  # str(tos_wi.get('LOCK_FLAG')).startswith('U') and
            #         nodes = [f"YQ{lock_pavilion[:-2].zfill(2)}D00{[1,4][int(lock_pavilion[-1])%2]}"]
            # else:
            nodes = [lock_node]
        elif command_type == RemoteCommand.MOVE_TO_QUEUE:
            if not str(tos_wi.get('LOCK_FLAG')) in NEED_LOCK_FLAG_LIST and queue_node in nodes:
                nodes = filter_locks_or_queue(nodes)
                nodes = nodes[:nodes.index(queue_node) + 1]
            else:
                nodes = [queue_node]
        elif command_type == RemoteCommand.MOVE_TO_GANTRY:
            nodes = filter_locks_or_queue(nodes, filter_queue=True)
        elif command_type == RemoteCommand.MOVE_TO_CRANE:
            if str(tos_wi.get('LOCK_FLAG')) in NEED_LOCK_FLAG_LIST and tos_wi.get('LOCK_PAVILION') and lock_node:
                nodes = [nodes[-1]]
            else:
                nodes = filter_locks_or_queue(nodes, filter_queue=True)
        elif command_type == RemoteCommand.MOVE_TO_REST:
            nodes = [tos_wi.get('CTRL_WAIT_NODE')]
        logger.info(f"vehicle name:{self._truck_no},id:{self._id},stop_node:{stop_node},nodes:{nodes}")
        if len(nodes) < 2:
            logger.warning(f"vehicle_name:{self._truck_no},id:{self._id} may error.get nodes less than 2")
        path_points = dict()
        nodes = [nodes] if not isinstance(nodes, list) else nodes
        for node in nodes:
            utm_x, utm_y = TransferPoint().get_transfer_point_loc(node)
            if utm_x is None or utm_y is None:
                logger.warning(f"vehicle name:{self._truck_no},id:{self._id},node:{node} fail to utm")
                return None
            point = Position(utm_x = utm_x,utm_y = utm_y)
            path_points[node] = point
        logger.info(f"vehicle name:{self._truck_no},id:{self._id},get stop node:{path_points}")
        return path_points

    def get_logic_destination(self, tos_wi):
        if tos_wi is None:
            return None
        stop_node = tos_wi.get('CTRL_STOP_NODE')
        if stop_node is None or len(stop_node) == 0:
            logger.warning(f"may error.vehicle name:{self._truck_no} has no stop node.tos_wi:{tos_wi.get('ID')}")
            return None
        nodes = stop_node.split(',')
        logger.info(f"vehicle name:{self._truck_no},stop_node:{stop_node},logic dest:{nodes[-1]}")
        return nodes[-1]

    def is_crane_for_binded(self, tos_wi):
        if not tos_wi['TO_POS'].startswith('CR'):
            return False
        return tos_wi.get('TWIN_FLAG') == 'T'
        '''
        crane = CraneInfoContainer().get_crane_info(int(tos_wi['TO_POS'][2:4]))
        size = crane.spr_size if crane is not None else 0
        if size == 1:
            logger.info('crane bind status: False')
            return False
        elif size == 2 or size == 4:
            logger.info('crane bind status: True')
            return True
        else:
            logger.warning('Invalid crane spr size: ' + str(size) + 'assuming ' + str(tos_wi['TWIN_FLAG'] == 'T'))
            return tos_wi['TWIN_FLAG'] == 'T'
        '''

    def parse_commands(self, tos_wi, db, tos_wis:list):
        logger.info('vehicle name:' + str(self._truck_no) + ' parse command:' + str(tos_wi))
        self._commands.clear()
        action = tos_wi.get('CTRL_ACTION')
        # reset指令 直接过滤中间逻辑点reset指令
        if tos_wi.get('RESET_VERSION') and tos_wi.get('CTRL_STOP_NODE'):
            # 2022.07 调箱门作业暂时不去作业，reset后 过滤DX开头的逻辑点
            tos_wi["CTRL_STOP_NODE"] = ",".join(list(
                filter(lambda node: not (node.startswith("IR") or node.startswith("DX")), tos_wi.get('CTRL_STOP_NODE').split(","))))
        #to do lzc
        if action == 'LOAD' or action == 'UNLOAD':
            if tos_wi['WI_STATUS'] == 'DISPATCH':
                if tos_wi.get('WI_TYPE') == 'LOAD':
                    if tos_wi['WI_ACT'] == 'LOAD':
                        self._commands.append(self.make_command(
                            RemoteCommand.STOP, tos_wi, 'START', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                    elif tos_wi['WI_ACT'] == 'UNLOAD':
                        if tos_wi['TWIN_TYPE'] == 'SECOND':
                            if not self.is_crane_for_binded(tos_wi):  # 如果是绑定双小箱则第二条指令不解析，直接跳过，设置TRUCK_STATUS为FINISH
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP, tos_wi, 'START', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_QUEUE, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_IN_LINE, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                                '''
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP_CRANE_DETECTION, tos_wi, '', db, tos_wis=tos_wis))
                                '''
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                        else:  # IECS tos_wi['TWIN_TYPE'] == 'ALONE' tos_wi['TWIN_TYPE'] == 'FIRST'
                            if tos_wi['TWIN_TYPE'] == 'FIRST' and self.is_crane_for_binded(tos_wi):
                                for wi in tos_wis:
                                    wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                                    wi['TWIN_TYPE'] = 'ALONE'
                                tos_wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                                tos_wi['TWIN_TYPE'] = 'ALONE'
                            self._commands.append(self.make_command(
                                RemoteCommand.STOP, tos_wi, 'START', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_LOCK, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.WAIT_LOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_QUEUE, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.WAIT_IN_LINE, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                            '''
                            self._commands.append(self.make_command(
                                RemoteCommand.STOP_CRANE_DETECTION, tos_wi, '', db, tos_wis=tos_wis))
                            '''
                            self._commands.append(self.make_command(
                                RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                elif tos_wi.get('WI_TYPE') == 'DSCH':
                    if tos_wi['WI_ACT'] == 'LOAD':
                        if tos_wi['TWIN_TYPE'] == 'SECOND':  # 双小箱 第二个
                            #pass
                            if self.is_crane_for_binded(tos_wi):  # 绑定
                                pass
                            else:  # 非绑定
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP, tos_wi, 'START', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                                '''
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP_CRANE_DETECTION, tos_wi, '', db, tos_wis=tos_wis))
                                '''
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                                '''
                                # 2022.07 引桥休息区暂时设置在箱区，默认无后续指令去休息区前需要先去锁亭
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', tos_wis=tos_wis))
                                '''
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                        else:
                            if self.is_crane_for_binded(tos_wi):
                                for wi in tos_wis:
                                    wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                                    wi['TWIN_TYPE'] = 'ALONE'
                                tos_wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                                tos_wi['TWIN_TYPE'] = 'ALONE'
                                '''
                                tos_wi['EQUIT_TYPE'] = '22GP'
                                tos_wi['TRUCK_POS'] = 'F'
                                tos_wi['TWIN_TYPE'] = 'FIRST'
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP, tos_wi, 'START', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_CRANE_OFF, tos_wi, '', db, tos_wis=tos_wis))
                                tos_wi['TRUCK_POS'] = 'A'
                                tos_wi['TWIN_TYPE'] = 'SECOND'
                                self._commands.append(self.make_command(
                                    RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.STOP_CRANE_DETECTION, tos_wi, '', db, tos_wis=tos_wis))
                                self._commands.append(self.make_command(
                                    RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                                '''
                            # 非绑定双小箱第一个
                            self._commands.append(self.make_command(
                                RemoteCommand.STOP, tos_wi, 'START', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                            '''
                            self._commands.append(self.make_command(
                                RemoteCommand.STOP_CRANE_DETECTION, tos_wi, '', db, tos_wis=tos_wis))
                            '''
                            self._commands.append(self.make_command(
                                RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                            '''
                            # 2022.07 引桥休息区暂时设置在箱区，默认无后续指令去休息区前需要先去锁亭
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', tos_wis=tos_wis))
                            self._commands.append(self.make_command(
                                RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', tos_wis=tos_wis))
                            '''
                            self._commands.append(self.make_command(
                                RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                    elif tos_wi['WI_ACT'] == 'UNLOAD':
                        self._commands.append(self.make_command(
                            RemoteCommand.STOP, tos_wi, 'START'))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                elif tos_wi.get('WI_TYPE') == 'YARD':
                    self._commands.append(self.make_command(
                        RemoteCommand.STOP, tos_wi, 'START', tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
            elif tos_wi['WI_STATUS'] == 'CANCEL':
                logger.info("wi status is cancel:" + str(tos_wi))
                self._is_cancel = True
                self._commands.append(self.make_command(
                    RemoteCommand.STOP, tos_wi, 'CANCEL_YARD', tos_wis=tos_wis))
        elif action == 'LEAVE_SPACE' or action == 'STOP_WORK':
            if tos_wi['WI_STATUS'] == 'DISPATCH':
                if str(tos_wi.get('WI_TYPE')) == 'None':
                    if tos_wi.get('WI_ACT') == 'LEAVE_SPACE':
                        self._commands.append(self.make_command(
                            RemoteCommand.STOP, tos_wi, 'START', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_LEAVE_SPACE, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_LEAVE_SPACE, tos_wi, 'FINISH', tos_wis=tos_wis))
                    elif tos_wi.get('WI_ACT') == 'STOP_WORK':
                        self._commands.append(self.make_command(
                            RemoteCommand.STOP, tos_wi, 'START', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_STOP_WORK, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_STOP_WORK, tos_wi, 'FINISH', tos_wis=tos_wis))
            elif tos_wi['WI_STATUS'] == 'CANCEL':
                logger.info("wi status is cancel:" + str(tos_wi))
                self._is_cancel = True
                self._commands.append(self.make_command(
                    RemoteCommand.STOP, tos_wi, 'CANCEL_YARD', tos_wis=tos_wis))
        elif action == 'MOVE':
            self._commands.append(self.make_command(
                RemoteCommand.STOP, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_POSITION, tos_wi, '', tos_wis=tos_wis))
        elif action == 'CHARGE':
            self._commands.append(self.make_command(
                RemoteCommand.STOP, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_CHARGE, tos_wi, '', tos_wis=tos_wis))
        elif action == 'CANCEL':
            logger.info("wi status is cancel:" + str(tos_wi))
            self._is_cancel = True
            self._commands.append(self.make_command(
                RemoteCommand.STOP, tos_wi, 'CANCEL_YARD', tos_wis=tos_wis))
        elif action == 'STOP':
            self._is_cancel = True
            self._commands.append(self.make_command(
                  RemoteCommand.STOP, tos_wi, '', tos_wis=tos_wis))
        else:
            logger.warning('vehicle name:' + str(self._truck_no) + ' unknow action:' + str(action))
        self._commands = list(filter(lambda command: command, self._commands))
        if len(self._commands) > 0 and self._commands[-1] is not None:
            self._commands[-1].command.command_context.command_idx_type = RemoteCommand.CommandContext.LAST_IDX
        logger.info('vehicle name:' + str(self._truck_no) + ' parse result:' + str(self._commands))

    def _get_yard_min_bay(self, destination, tos_wi = {}):
        min_bay = "06"
        if destination is not None:
            if destination.startswith('91') or destination.startswith('J1'):
                min_bay = "20"
            elif destination.startswith('92') or destination.startswith('J2'):
                min_bay = "12"
            else:
                pass
        return min_bay

    def make_base_command_context(self, command_type, tos_wi, db=None) -> RemoteCommand.CommandContext:
        command_context = RemoteCommand.CommandContext()
        command_context.type = command_type
        command_context.vms_type = RemoteCommand.CommandContext.NB_ALIA if not is_fabu_wi(tos_wi) else RemoteCommand.CommandContext.FABU
        command_context.command_idx_type = RemoteCommand.CommandContext.UNKNOWN_IDX
        #todo cancel
        if tos_wi is not None:
            # wi_type = 'WI_' + str(tos_wi.get('WI_TYPE')) if tos_wi.get('WI_TYPE') is not None else 'WI_UNKNOWN'
            wi_type = 'WI_' + str(tos_wi.get('WI_TYPE')) if str(tos_wi.get('WI_TYPE')) != 'None' else 'WI_UNKNOWN'
            command_context.wi_type = RemoteCommand.CommandContext.WiType.Value(wi_type)
            command_context.action_type = RemoteCommand.CommandContext.ActionType.Value(tos_wi.get('CTRL_ACTION'))
            command_context.twin_type = RemoteCommand.CommandContext.TwinType.Value(tos_wi.get('TWIN_TYPE'))
            command_context.tos_command_id = tos_wi.get('ID')
            #to do
            logic_destination = self.get_logic_destination(tos_wi)
            destination = tos_wi.get('TO_POS').split(' ')[0]
            command_context.transfer_point = destination  # tos_wi.get('TO_POS').split(' ')[0]

            if is_container_wi(tos_wi):  # common_util.is_tos_command(tos_wi.get('CTRL_ACTION')):
                command_context.wi_no = str(tos_wi['WI_NO'])
                command_context.twin_wi_no = str(tos_wi['TWIN_WI_NO'])
                if destination is not None and is_gantry_yard_wi(tos_wi) and len(destination) == 2:
                    destination = destination + self._get_yard_min_bay(destination)
                    logger.info('destination has no position, add position default:' + destination)
                if destination is not None and db is not None and destination.startswith('CR'):
                    crane_info = db.query_crane_info(destination, NameConverter().is_debug_truck_no(tos_wi.get('TRUCK_NO')))
                    if crane_info is not None and crane_info['LANE_NO'] is not None:
                        command_context.lane_id = int(crane_info['LANE_NO'])
                    else:
                        logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} {tos_wi.get('TO_POS')} has no lane id,use default 0")
                        system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.CRANE_NO_LANE_ID, destination + ' has no lane id')
                        command_context.lane_id = 0
                    # add vessel direction
                    if crane_info is not None and crane_info['VESSEL_DIRECTION'] is not None and crane_info['VESSEL_DIRECTION'] == 'L':
                        command_context.vessel_direction = VesselDirection.LEFT
                    else:
                        command_context.vessel_direction = VesselDirection.RIGHT

                if len(tos_wi.get('TO_POS').split(' ')) > 1:
                    command_context.warehouse = tos_wi.get('TO_POS').split(' ')[1]
                command_context.container_id = str(tos_wi.get('CTN_NO')) if tos_wi.get('CTN_NO') is not None else ''
                if tos_wi.get('CTN_WEIGHT') is not None:
                    command_context.container_weight = tos_wi.get('CTN_WEIGHT')
                # command_context.container_id = str(tos_wi.get('WI_ID')) if tos_wi.get('WI_ID') is not None else ''
                if tos_wi.get('EQUIT_TYPE') is not None:
                    if tos_wi.get('EQUIT_TYPE') == 'BINDED_TWIN':
                        command_context.container_size = ContainerSize.SIZE_BINDED_TWIN
                    elif tos_wi.get('EQUIT_TYPE').startswith('22'):
                        command_context.container_size = ContainerSize.SIZE_20_FEET
                        db_dict = {
                            'F': RemoteCommand.CommandContext.FRONT,
                            'A': RemoteCommand.CommandContext.BEHIND,
                            'M': RemoteCommand.CommandContext.MIDDLE,
                            '0': RemoteCommand.CommandContext.UNKNOWN_POSITION
                        }
                        command_context.container_position = db_dict[tos_wi.get('TRUCK_POS')] if tos_wi.get('TRUCK_POS') in db_dict else RemoteCommand.CommandContext.UNKNOWN_POSITION
                    elif tos_wi.get('EQUIT_TYPE').startswith('L'):
                        command_context.container_size = ContainerSize.SIZE_45_FEET
                    else:
                        command_context.container_size = ContainerSize.SIZE_40_FEET
                else:
                    logger.warning(
                        f"vehicle:{tos_wi.get('TRUCK_NO')},wi id:{tos_wi['ID']} get no EQUIT_TYPE,may error.use default 40 feet")
                    command_context.container_size = ContainerSize.SIZE_40_FEET
            command_context.destination_id = destination
            if str(tos_wi.get('LOCK_FLAG')) in NEED_LOCK_FLAG_LIST or str(tos_wi.get('LOCK_FLAG')).startswith('U'):
                lock_flag = str(tos_wi.get('LOCK_FLAG'))
                # lock_pavilion = str(tos_wi.get('LOCK_PAVILION'))
                # if not lock_flag and lock_pavilion:
                #     lock_flag = [f"YQ{lock_pavilion[:-2].zfill(2)}D00{[1, 4][int(lock_pavilion[-1]) % 2]}"]
                command_context.lock_flag = lock_flag
                if tos_wi.get('LOCK_PAVILION') and tos_wi.get('LOCK_PAVILION') != "None":
                    command_context.pavilion = str(tos_wi.get('LOCK_PAVILION'))
                if is_container_wi(tos_wi):
                    if tos_wi.get('VESSEL_POS'):
                        command_context.vessel_pos = tos_wi.get('VESSEL_POS')
                    # 2022.07.25 lock_container_id 以 wi_id 作为与箱对应的标识 （由于 CTN_NO 箱号有时候会没有）
                    # if len(command_context.container_id) > 0:
                    if tos_wi.get('WI_ID'):
                        command_context.lock_container_id.append(str(tos_wi.get('WI_ID')))
                    else:
                        logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')},ID:{tos_wi.get('ID')} has no WI_ID")
                        system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TOS_CMD_INVALID, 'ID:' + str(tos_wi.get('ID')) + ' has no WI_ID')
            command_context.priority = RemoteCommand.CommandContext.NORMAL
            if tos_wi.get('WI_TYPE') == 'LOAD' and tos_wi.get('WI_ACT') == 'UNLOAD':
                if (truck_seq := eval(tos_wi.get('TRUCK_SEQ'))) < 100:
                    if truck_seq == 1:
                        command_context.priority = RemoteCommand.CommandContext.URGENT
                    elif truck_seq == 2:
                        command_context.priority = RemoteCommand.CommandContext.HIGH
                    elif truck_seq == 3:
                        command_context.priority = RemoteCommand.CommandContext.NORMAL
                    elif truck_seq == 0:
                        command_context.priority = RemoteCommand.CommandContext.NORMAL
                else:  # X  2022.06.17 truck_seq 100
                    command_context.priority = RemoteCommand.CommandContext.LOW

        return command_context

    def is_need_make_command(self, command_type, tos_wi, db = None):
        ret = False
        lock_container_id = str(tos_wi.get('WI_ID')) if tos_wi is not None else None
        truck_seq = tos_wi.get('TRUCK_SEQ') if tos_wi else None
        queue_node = tos_wi.get('CTRL_QUEUE_NODE') if tos_wi is not None else None
        if command_type == RemoteCommand.MOVE_TO_CRANE or command_type == RemoteCommand.MOVE_VIA_CRANE_CPS or \
                command_type == RemoteCommand.STOP_CRANE_DETECTION or command_type == RemoteCommand.WAIT_CRANE_OFF:
            if truck_seq and int(truck_seq) < 100:
                ret = True
        elif command_type == RemoteCommand.MOVE_TO_REST:
            if truck_seq and int(truck_seq) < 100 and (tos_wi['TWIN_TYPE'] == 'ALONE' or tos_wi['TWIN_TYPE'] == 'SECOND'):  # 双小箱第二条 大箱 单小箱
                ret = True
        elif command_type == RemoteCommand.WAIT_IN_LINE or command_type == RemoteCommand.MOVE_TO_QUEUE:
            if truck_seq and int(truck_seq) >= 100 and queue_node:
                ret = True
        elif command_type == RemoteCommand.MOVE_TO_LOCK or command_type == RemoteCommand.WAIT_LOCK_OFF:
            is_locked = self._lock_cache.get_container_lock_is_exist(lock_container_id)  # , "False"
            if str(tos_wi.get('LOCK_FLAG')) in NEED_LOCK_FLAG_LIST and tos_wi.get(
                    'LOCK_PAVILION') and tos_wi.get('CTRL_LOCK_NODE') and len(
                is_locked) > 0 and is_locked != "True":  # 双小箱第二个不执行锁亭作业 and tos_wi.get('TWIN_TYPE') != 'SECOND'
                ret = True
        elif command_type == RemoteCommand.MOVE_TO_UNLOCK or command_type == RemoteCommand.WAIT_UNLOCK_OFF:
            # lock_container_id = self.get_lock_container_id(tos_wi, db)
            if tos_wi.get('WI_ACT') == 'UNLOAD' and str(tos_wi.get('LOCK_FLAG')).startswith('U') and tos_wi.get(
                    'LOCK_PAVILION') and tos_wi.get('CTRL_LOCK_NODE') and lock_container_id and len(
                lock_container_id) > 0 and tos_wi.get('TWIN_TYPE') != 'SECOND':  # 双小箱第二个不执行锁亭作业
                self._lock_cache.set_container_lock_is_exist(lock_container_id, "True")  # if not exist ,then set True else do nothing
                if self._lock_cache.get_container_lock_is_exist(lock_container_id) == "True":
                    ret = True

            # # 休息区暂时在箱区  卸船装箱指令默认去卸锁，若没有下一步指令，在去休息区前需要先去卸锁亭
            # # 卸船装箱指令 发LOCK_PAVILION或LOCK_NODE这两个：LOCK_FLAG
            # if tos_wi.get('WI_ACT') == 'LOAD' and tos_wi.get('WI_TYPE') == 'DSCH' and (tos_wi.get(
            #     'LOCK_PAVILION') or tos_wi.get('CTRL_LOCK_NODE')) and lock_container_id and len(lock_container_id) > 0:
            #     self._lock_cache.set_container_lock_is_exist(lock_container_id, "True")  # if not exist ,then set True else do nothing
            #     if self._lock_cache.get_container_lock_is_exist(lock_container_id) == "True":
            #         ret = True
        elif command_type == RemoteCommand.MOVE_TO_GANTRY or command_type == RemoteCommand.WAIT_GANTRY_COME \
                or command_type == RemoteCommand.MOVE_VIA_GANTRY_CPS or command_type == RemoteCommand.WAIT_GANTRY_OFF:
            ret = True
        else:
            ret = True

        if ret:
            logger.info(
                f'vehicle_name:{self._truck_no},command_type:{RemoteCommand.CommandType.Name(command_type)} need make command,tos_wi order id:{self._id}')
        else:
            logger.info(
                f'vehicle_name:{self._truck_no},command_type:{RemoteCommand.CommandType.Name(command_type)} no need make command,tos_wi order id:{self._id}')
        return ret

    def make_command(self, command_type, tos_wi, finish_status, db=None, tos_wis:list = []) -> CommandWithStatus:
        if not self.is_need_make_command(command_type, tos_wi, db):
            return None
        command = RemoteCommand()
        command.timestamp_ms = int(time.time() * 1000)
        command.uuid = str(uuid.uuid1())
        command.type = command_type
        command_context = self.make_base_command_context(command_type, tos_wi, db)
        command.command_context.CopyFrom(command_context)
        for wi in tos_wis:
            command_context_info = self.make_base_command_context(command_type, wi, db)
            if command_context_info is not None:
                command.mul_command_context.append(command_context_info)
        destination = command_context.transfer_point
        if destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_GANTRY:
            points = self.get_path_points(tos_wi, RemoteCommand.MOVE_TO_GANTRY)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_gantry_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            utm_x,utm_y = TransferPoint().get_transfer_point_loc(tos_wi.get('CTRL_WAIT_NODE'))
            if utm_x is not None and utm_y is not None:
                extreme_point = command.move_to_gantry_command.extreme_point
                extreme_point.utm_x, extreme_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get wait_node point:{tos_wi.get('CTRL_WAIT_NODE')}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(tos_wi.get('CTRL_WAIT_NODE')) + ' wait node is not exist')
            utm_x,utm_y = TransferPoint().get_transfer_point_loc(destination)
            if utm_x is not None and utm_y is not None:
                terminal_point = command.move_to_gantry_command.terminal_point
                terminal_point.utm_x, terminal_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get destination:{destination}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.YARD_NOT_EXIST, destination + ' is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_CRANE:
            command.move_to_crane_command.crane_id = int(destination[2:])
            crane_info = db.query_crane_info(destination, NameConverter().is_debug_truck_no(tos_wi.get('TRUCK_NO')))
            if not crane_info is None:
                for i in range(1, 4):
                    bay = 'BAY%d' % i
                    if bay in crane_info and not crane_info[bay] is None:
                        command.move_to_crane_command.crane_bay.append(crane_info[bay])
            else:
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.CRANE_NOT_EXIST, destination + ' is not exist')
            command.move_to_crane_command.lane_id = command_context.lane_id
            if crane_info is not None and crane_info['VESSEL_DIRECTION'] == 'L':
                command.move_to_crane_command.vessel_direction = VesselDirection.LEFT
            else:
                command.move_to_crane_command.vessel_direction = VesselDirection.RIGHT
            #route path
            points = self.get_path_points(tos_wi, RemoteCommand.MOVE_TO_CRANE)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_crane_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            #to do
            # if command.command_context.priority == RemoteCommand.CommandContext.LOW:
            #     #queue node
            #     utm_x,utm_y = TransferPoint().get_transfer_point_loc(tos_wi.get('CTRL_QUEUE_NODE'))
            #     if utm_x is not None and utm_y is not None:
            #         queue_point = command.move_to_crane_command.nb_alia.queue_point
            #         queue_point.utm_x, queue_point.utm_y = utm_x, utm_y
            #     else:
            #         logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get queue point:{tos_wi.get('CTRL_QUEUE_NODE')}")
            #         system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(tos_wi.get('CTRL_QUEUE_NODE')) + ' queue point is not exist')
            #to do
            # utm_x,utm_y = TransferPoint().get_transfer_point_loc(tos_wi.get('CTRL_WAIT_NODE'))
            # if utm_x is not None and utm_y is not None:
            #     wait_point = command.move_to_crane_command.nb_alia.wait_point
            #     wait_point.utm_x, wait_point.utm_y = utm_x, utm_y
            # else:
            #     logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get wait_node point:{tos_wi.get('CTRL_WAIT_NODE')}")
            #     system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(tos_wi.get('CTRL_WAIT_NODE')) + ' wait node is not exist')
            #destination
            utm_x,utm_y = TransferPoint().get_transfer_point_loc(destination)
            if utm_x is not None and utm_y is not None:
                approximate_point = command.move_to_crane_command.approximate_point
                approximate_point.utm_x, approximate_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get destination:{destination}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.CRANE_NOT_EXIST, destination + ' is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_VIA_CRANE_CPS:
            command.move_via_crane_cps_command.crane_id = int(destination[2:])
            crane_info = db.query_crane_info(destination, NameConverter().is_debug_truck_no(tos_wi.get('TRUCK_NO')))
            if crane_info is not None and crane_info['VESSEL_DIRECTION'] == 'L':
                command.move_via_crane_cps_command.vessel_direction = VesselDirection.LEFT
            else:
                command.move_via_crane_cps_command.vessel_direction = VesselDirection.RIGHT
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.WAIT_CRANE_OFF:
            command.wait_crane_off_command.crane_id = int(destination[2:])
            crane_info = db.query_crane_info(destination, NameConverter().is_debug_truck_no(tos_wi.get('TRUCK_NO')))
            command.wait_crane_off_command.lane_id = command_context.lane_id
            if crane_info is not None and crane_info['VESSEL_DIRECTION'] == 'L':
                command.wait_crane_off_command.vessel_direction = VesselDirection.LEFT
            else:
                command.wait_crane_off_command.vessel_direction = VesselDirection.RIGHT
            #to do
            utm_x,utm_y = TransferPoint().get_transfer_point_loc(tos_wi.get('CTRL_WAIT_NODE'))
            if utm_x is not None and utm_y is not None:
                wait_point = command.wait_crane_off_command.nb_alia.wait_point
                wait_point.utm_x, wait_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get wait_node point:{tos_wi.get('CTRL_WAIT_NODE')}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(tos_wi.get('CTRL_WAIT_NODE')) + ' wait node is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.WAIT_GANTRY_OFF:
            pass
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_LOCK:
            points = self.get_path_points(tos_wi, RemoteCommand.MOVE_TO_LOCK)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_lock_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            # command.move_to_lock_command.nb_alia.need_lock = False
            lock_node = tos_wi.get('CTRL_LOCK_NODE')
            # 装卸锁亭没有指定锁亭的情况会发类似 8Y1-2 则默认的是去8Y1
            lock_pavilion = lock_p if "-" not in (lock_p := tos_wi.get('LOCK_PAVILION')) else lock_p.split("-")[0]
            if lock_pavilion is not None and len(lock_pavilion) > 0:
                command.move_to_lock_command.poi_id = str(lock_pavilion)
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get lock pavilion poi_id:{lock_pavilion}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST,
                                     str(lock_pavilion) + ' lock pavilion poi_id is not exist')
            if lock_node is not None and len(lock_node) > 0:
                terminal_point = command.move_to_lock_command.terminal_point
                utm_x, utm_y = TransferPoint().get_transfer_point_loc(lock_node)
                if utm_x is not None and utm_y is not None:
                    terminal_point.utm_x, terminal_point.utm_y = utm_x, utm_y
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get lock node point:{lock_node}")
                    system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(lock_node) + ' lock node is not exist')
                if len(command_context.lock_container_id) > 0 and len(command_context.lock_container_id[0]) > 0:
                    # command.move_to_lock_command.nb_alia.need_lock = True
                    self._lock_cache.set_container_lock_is_exist(command_context.lock_container_id[0], "False")
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},ID:{tos_wi.get('ID')} has no lock container id")
        elif command_type == RemoteCommand.WAIT_LOCK_OFF:
            pass
            '''
            # command.wait_lock_off_command.need_lock = False
            if str(tos_wi.get('LOCK_FLAG')) in NEED_LOCK_FLAG_LIST:
                lock_node = tos_wi.get('CTRL_LOCK_NODE')
                if lock_node is not None and len(lock_node) > 0:
                    if len(command_context.lock_container_id) > 0 and len(command_context.lock_container_id[0]) > 0:
                        command.wait_lock_off_command.need_lock = True
                        self._lock_cache.set_container_lock_is_exist(command_context.lock_container_id[0], "False")
                    else:
                        logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},ID:{tos_wi.get('ID')} has no lock container id")
            '''
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_QUEUE:
            points = self.get_path_points(tos_wi, RemoteCommand.MOVE_TO_QUEUE)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_queue_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' queue node is not exist')
            queue_node = tos_wi.get('CTRL_QUEUE_NODE')
            queue_poi_id = TransferPoint().get_transfer_point_poi_id(queue_node)
            if queue_poi_id is not None and len(queue_poi_id) > 0:
                command.move_to_queue_command.poi_id = queue_poi_id
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get queue node poi_id:{queue_node}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST,
                                     str(queue_node) + ' queue node poi_id is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_UNLOCK:
            points = self.get_path_points(tos_wi, command_type=RemoteCommand.MOVE_TO_UNLOCK)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_unlock_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            # command.move_to_unlock_command.nb_alia.need_unlock = False
            lock_node = tos_wi.get('CTRL_LOCK_NODE')
            # 装卸锁亭没有指定锁亭的情况 LOCK_PAVILION 会发类似 8Y1-2 则默认的是去8Y1
            unlock_pavilion = unlock_p if "-" not in (unlock_p := tos_wi.get('LOCK_PAVILION')) else unlock_p.split("-")[0]
            if unlock_pavilion is not None and len(unlock_pavilion) > 0:
                command.move_to_unlock_command.poi_id = str(unlock_pavilion)
            # elif tos_wi.get('WI_TYPE') == 'DSCH' and tos_wi.get('WI_ACT') == 'LOAD' and lock_node:  # 休息区在箱区，卸船装箱默认去休息区前先去卸锁
            #     command.move_to_unlock_command.poi_id = f"{int(lock_node[2:4])}Y{[3,4][int(lock_node[-1])%2]}"
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get unlock pavilion poi_id:{unlock_pavilion}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST,
                                     str(unlock_pavilion) + ' unlock pavilion poi_id is not exist')
            # 2022.09.15 # 休息区在箱区，卸船装箱默认去休息区前先去卸锁
            # if not lock_node:
            #     if tos_wi.get('WI_TYPE') == 'DSCH' and tos_wi.get('WI_ACT') == 'LOAD' and points is not None:
            #         lock_node = str(list(points.keys())[0])
            #     else:
            #         logger.warning(f"DSCH LOAD move_to_unlock: lock_node:{lock_node},fail to get unlock pavilion :{unlock_pavilion}")
            if lock_node is not None and len(lock_node) > 0:
                terminal_point = command.move_to_unlock_command.terminal_point
                utm_x, utm_y = TransferPoint().get_transfer_point_loc(lock_node)
                if utm_x is not None and utm_y is not None:
                    terminal_point.utm_x, terminal_point.utm_y = utm_x, utm_y
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get unlock node point:{lock_node}")
                    system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(lock_node) + ' unlock node is not exist')
                if len(command_context.lock_container_id) > 0 and len(command_context.lock_container_id[0]):
                    # command.move_to_unlock_command.nb_alia.need_unlock = True
                    self._lock_cache.set_container_lock_is_exist(command_context.lock_container_id[0], "True")
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},ID:{tos_wi.get('ID')} has no unlock container id")
        elif command_type == RemoteCommand.WAIT_UNLOCK_OFF:
            pass
            '''
            # command.wait_unlock_off_command.need_unlock = False
            lock_node = tos_wi.get('CTRL_LOCK_NODE')
            # 装卸锁亭没有指定锁亭的情况 LOCK_PAVILION 会发类似 8Y1-2 则默认的是去8Y1
            unlock_pavilion = unlock_p if "-" not in (unlock_p := tos_wi.get('LOCK_PAVILION')) else unlock_p.split("-")[
                0]
            # if str(tos_wi.get('LOCK_FLAG')).startswith('U'):
            if (lock_node is not None and len(lock_node) > 0) or (tos_wi.get('WI_ACT') == 'LOAD' and unlock_pavilion):
                if len(command_context.lock_container_id) > 0 and len(command_context.lock_container_id[0]):
                    command.wait_unlock_off_command.need_unlock = True
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},ID:{tos_wi.get('ID')} has no lock container id")
            '''
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_REST:
            points = self.get_path_points(tos_wi, RemoteCommand.MOVE_TO_REST)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_rest_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            wait_node = tos_wi.get('CTRL_WAIT_NODE')
            if wait_node is not None and len(wait_node) > 0:
                reset_poi_id = TransferPoint().get_transfer_point_poi_id(wait_node)
                if reset_poi_id:
                    command.move_to_rest_command.poi_id = str(reset_poi_id)
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get wait node poi_id:{wait_node}")
                    system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(wait_node) + ' wait node poi_id is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_CHARGE:
            points = self.get_path_points(tos_wi)
            if points is not None:
                for (k, v) in points.items():
                    point_pair = command.move_to_charge_command.nb_alia.path_points.add()
                    point_pair.name = str(k)
                    point_pair.position.CopyFrom(v)
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            utm_x, utm_y = TransferPoint().get_transfer_point_loc(destination)
            if utm_x is not None and utm_y is not None:
                terminal_point = command.move_to_charge_command.terminal_point
                terminal_point.utm_x, terminal_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get destination point:{destination}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(destination) + ' destination is not exist')
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.MOVE_TO_POSITION:
            points = self.get_path_points(tos_wi)
            if points is not None:
                for (k, v) in points.items():
                    command.move_to_position_command.nb_alia.path_point[str(k)].CopyFrom(Position(utm_x = v.utm_x, utm_y = v.utm_y))
            else:
                logger.warning(f"vehicle_name:{tos_wi.get('TRUCK_NO')} fail to get path point")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, ' stop node is not exist')
            utm_x, utm_y = TransferPoint().get_transfer_point_loc(destination)
            if utm_x is not None and utm_y is not None:
                terminal_point = command.move_to_position_command.terminal_point
                terminal_point.utm_x, terminal_point.utm_y = utm_x, utm_y
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get destination point:{destination}")
                system_status.insert(tos_wi.get('TRUCK_NO'), SystemStatusDetail.TRANSFER_POINT_NOT_EXIST, str(destination) + ' lock node is not exist')
        elif destination is not None and command_type == RemoteCommand.MOVE_LEAVE_SPACE:
            command.move_leave_space_command.poi_id = destination
        elif destination is not None and command_type == RemoteCommand.WAIT_LEAVE_SPACE:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_STOP_WORK:
            command.move_stop_work_command.poi_id = destination
        elif destination is not None and command_type == RemoteCommand.WAIT_STOP_WORK:
            pass
        elif destination is not None and len(destination) > 0 and command_type == RemoteCommand.STOP:
            # if (finish_status == 'CANCEL_YARD' and (tos_wi['TWIN_FLAG'] == 'Y' or tos_wi['TWIN_FLAG'] == 'T')) \
            #         or finish_status == 'START':
            #     command.stop_command.stop_position = StopCommand.ROUTE_POSITION
            if finish_status == 'START':
                command.stop_command.stop_position = StopCommand.ROUTE_POSITION

        command_status = CommandWithStatus()
        command_status.command.CopyFrom(command)
        command_status.uuid = command.uuid
        command_status.status = CommandStatus.WAIT
        command_status.finish_db_status = finish_status
        command.command_source_type = RemoteCommand.CommandSourceType.TOS
        return command_status

    def get_commands(self) -> List[CommandWithStatus]:
        return self._commands

    def get_id(self):
        return self._id

    def get_wi_status(self):
        return self._wi_status

    def is_end(self):
        return self._wi_status in ['FINISH', 'CANCEL_YARD', 'CANCEL_DASH', 'ABANDON_YARD',
                                   'ABANDON_DASH']


def vehicle_status_to_dict(vehicle_status):
    vehicle_status_dict = dict()
    vehicle_status_dict['truck_no'] = vehicle_status.vehicle_name
    vehicle_status_dict['pos_x'] = vehicle_status.position.longitude
    vehicle_status_dict['pos_y'] = vehicle_status.position.latitude
    vehicle_status_dict['speed'] = vehicle_status.speed
    vehicle_status_dict[
        'sensor_status'] = 'Y' if vehicle_status.sensor_status == VehicleStatus.OK else 'N'
    return vehicle_status_dict

def filter_locks_or_queue(nodes, filter_queue=False):
    if not filter_queue:
        return list(
            filter(lambda node: not (node.startswith("YQ") and (node.endswith("001") or node.endswith("004"))),
                   nodes))
    else:
        return list(filter(lambda node: not (node.startswith("YQ") and (
                    node.endswith("001") or node.endswith("004") or node.endswith("009"))), nodes))

def filter_lock(lock_node, nodes):
    # 如果所有途径点都发过来则需要过滤多余的锁亭
    if lock_node[-1] == "1" and (lock_node_delete := lock_node.replace("1", "4")) in nodes:
        nodes.remove(lock_node_delete)
    elif lock_node[-1] == "4" and (lock_node_delete := lock_node.replace("4", "1")) in nodes:
        nodes.remove(lock_node_delete)
    # if (lock_node_delete := lock_node[:-1]+["1", "4"][int(not bool(["1", "4"].index(lock_node[-1])))]) in nodes:
    #     nodes.remove(lock_node_delete)
    return nodes
