#! -*- coding: utf-8 -*-
import os
import time
import traceback
import requests
import json
from common.logger import logger
from common.constant import DOCKER_NAME
from common.constant import SIMULATION_URL
from config.config_manage import ConfigManage


class WeChatMessage(object):
    def __init__(self, wx_url=None):
        self._wx_url = wx_url
    #mentioned_list=["@all"]
    def send_message(self, content, mentioned_list=[]):
        try:
            if ConfigManage().is_dev_env():
                logger.info(f"dev env not send message")
                return
            data = json.dumps({"msgtype": "text", "text": {
                              "mentioned_list": mentioned_list, "content": content}})
            r = requests.post(self._wx_url, data, timeout=10,
                              auth=('Content-Type', 'application/json'))
            logger.debug(r.json)
            return True
        except Exception as e:
            logger.error(
                f"send_message err:{e}, trace:{traceback.format_exc()}")
            return False
    
    def send_message_to_sim(self, content):
        try:
            # 监控开发环境仿真模式
            if not (ConfigManage().is_dev_env() and ConfigManage().is_sim_mode()):
                logger.info(f"only use in dev env && sim mode")
                return
            content = f'''docker name: [{DOCKER_NAME}]\n''' + content
            data = json.dumps({"msgtype": "text", "text": {"content": content}})
            r = requests.post(SIMULATION_URL, data, timeout=10,
                              auth=('Content-Type', 'application/json'))
            logger.debug(r.json)
            return True
        except Exception as e:
            logger.warning(
                f"send message to sim err{e}, trace:{traceback.format_exc()}")
            return False


if __name__ == '__main__':
    '''
    wx_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7f1dd26a-c860-42de-9466-74f2dfaaaae6"
    wechat = WeChatMessage(wx_url)
    wechat.send_message("您好，这是个微信机器人测试信息")
    '''
    print(WeChatMessage().send_message_to_sim("您好，这是个微信机器人测试信息"))


