import time, os

from common.singleton import Singleton
from common.logger import logger
from threading import Thread


class Master(metaclass=Singleton):
    def __init__(self):
        self._is_master = False
        self._last_update = 0
        self._read_thread = Thread(target=self.read_thread)
        self._read_thread.start()

    def read_thread(self):
        while True:
            with open('/tmp/check_master.info') as f:
                v = f.readline().split()
                if len(v) != 0:
                    if v[0].startswith('master'):
                        self._is_master = True
                        try:
                            self._last_update = int(v[1])
                        except:
                            self._last_update = time.time() * 1000
                    elif v[0].startswith('backup'):
                        self._is_master = False
                        try:
                            self._last_update = int(v[1])
                        except:
                            self._last_update = time.time() * 1000
            time.sleep(1)

    def is_master(self):
        if self._last_update > time.time() * 1000 - 10 * 1000:
            return self._is_master
        else:
            return False

    def join(self):
        self._read_thread.join()



if __name__ == '__main__':
    while True:
        print(Master().is_master())
        time.sleep(1)
    
