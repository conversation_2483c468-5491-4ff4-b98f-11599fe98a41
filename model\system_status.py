import os
import time
import traceback

from sqlalchemy import <PERSON>umn, Integer, String
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession
from proto.system_status_cache_pb2 import SystemStatusDetail
from config.config_manage import ConfigManage

Base = declarative_base()


class SystemStatus(Base):
    __tablename__ = 'system_status'
    id = Column('id', Integer, primary_key=True, autoincrement=True)
    status_source = Column('status_source',  String(255))
    status_code = Column('status_code',  String(255))
    time_ms = Column('time_ms', Integer)
    comment = Column('comment',  String(255))
    name = Column('name',  String(255))


def is_support():
    if ConfigManage().is_iecs_scene() or ConfigManage().is_yz_cs_scene():
        return False
    else:
        return True

def get_one():
    session = MysqlSession().acquire()
    try:
        info = session.query(SystemStatus).first()
        return info
    finally:
        session.close()

def insert_event(name, status_code, comment, status_source, timestamp_sec=0):
    if not is_support():
        return
    start = time.time()
    session = MysqlSession().acquire()
    try:
        if timestamp_sec == 0 :
            timestamp_sec = int(time.time() * 1000)
            system_error_code = SystemStatusDetail.StatusCode.Name(status_code)
            status_source=SystemStatusDetail.StatusSource.Name(status_source)
        else:
            timestamp_sec = timestamp_sec
            system_error_code = status_code
        status = SystemStatus(name=name,
                              time_ms=timestamp_sec,
                              status_source=status_source,
                              status_code=system_error_code,
                              comment=comment)
        logger.info(f"Insert SystemStatus :{status.__dict__}")
        session.add(status)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert SystemStatus err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()
    end = time.time()
    if (end - start) > 1.0:
        logger.warning(f"SystemStatus insert over cost execeed round time:{round(end - start)}, execeed time:{end - start}")
    return


def insert(name, status_code, comment, status_source=SystemStatusDetail.ANTENNA_SERVER, timestamp_sec=0):
    #insert_event(name, status_code, comment, status_source, timestamp_sec)
    return




if __name__ == '__main__':
    info = get_one()
    print(info.__dict__)
