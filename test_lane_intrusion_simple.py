#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试8L、XG箱区的1.5米车道侵入offset功能
"""

import sys
import os
import math

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_constants():
    """测试常量导入"""
    try:
        from common.constant import LANE_INTRUSION_YARDS, LANE_INTRUSION_OFFSET_DISTANCE
        print(f"✓ 成功导入常量:")
        print(f"  LANE_INTRUSION_YARDS = {LANE_INTRUSION_YARDS}")
        print(f"  LANE_INTRUSION_OFFSET_DISTANCE = {LANE_INTRUSION_OFFSET_DISTANCE}")
        return True
    except Exception as e:
        print(f"✗ 导入常量失败: {e}")
        return False

def test_transfer_point():
    """测试TransferPoint功能"""
    try:
        from transfer_point_map.transfer_point import TransferPoint
        print(f"✓ 成功导入TransferPoint")
        
        tp = TransferPoint()
        print(f"✓ 成功创建TransferPoint实例")
        
        # 测试用例
        test_cases = [
            {"point": "8L10", "expected_offset": True, "desc": "8L箱区（应该有offset）"},
            {"point": "8M10", "expected_offset": False, "desc": "8M箱区（不应该有offset）"},
            {"point": "XG20", "expected_offset": True, "desc": "XG箱区（应该有offset）"},
            {"point": "XF20", "expected_offset": False, "desc": "XF箱区（不应该有offset）"},
        ]
        
        print(f"\n开始测试车道侵入offset功能:")
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {case['desc']}")
            try:
                result = tp.get_transfer_point_loc_v2(
                    case['point'], 'AT800', 'SIZE_40_FEET', 'FRONT', 'howo'
                )
                if result and len(result) == 2 and result[0] is not None and result[1] is not None:
                    print(f"  结果: ({result[0]:.3f}, {result[1]:.3f})")
                    print(f"  ✓ 获取坐标成功")
                else:
                    print(f"  ✗ 获取坐标失败: {result}")
            except Exception as e:
                print(f"  ✗ 测试异常: {e}")
        
        return True
    except Exception as e:
        print(f"✗ TransferPoint测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== 8L、XG箱区车道侵入offset功能测试 ===\n")
    
    success = True
    
    # 测试常量导入
    print("1. 测试常量导入:")
    success &= test_constants()
    
    print("\n" + "="*50 + "\n")
    
    # 测试TransferPoint功能
    print("2. 测试TransferPoint功能:")
    success &= test_transfer_point()
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("✓ 所有测试通过！")
        print("\n总结:")
        print("- 已添加LANE_INTRUSION_YARDS常量，包含8L和XG箱区")
        print("- 已添加LANE_INTRUSION_OFFSET_DISTANCE常量，设置为1.5米")
        print("- 已在TransferPoint.get_transfer_point_loc_v2()中实现车道侵入offset")
        print("- 8L和XG箱区会自动应用1.5米的车道侵入offset")
        print("- 其他箱区不受影响")
    else:
        print("✗ 部分测试失败，请检查实现")
    
    return success

if __name__ == "__main__":
    main()
