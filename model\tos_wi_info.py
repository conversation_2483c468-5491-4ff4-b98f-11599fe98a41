from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base

import traceback

from common import common_util
from common.db import SessionPool
from common.logger import logger

Base = declarative_base()
Base.to_dict = common_util.to_dict
Base.from_dict = common_util.from_dict


class TosWiInfo(Base):
    __tablename__ = 'T_WI_INFO_4V_TOS'
    id = Column('ID', Integer, primary_key=True)
    truck_no = Column('TRUCK_NO', String)
    truck_seq = Column('TRUCK_SEQ', Integer)
    wi_no = Column('WI_NO', Integer)
    ctn_no = Column('CTN_NO', String)
    equit_type = Column('EQUIT_TYPE', String)
    teu = Column('TEU', Integer)
    from_pos = Column('FROM_POS', String)
    to_pos = Column('TO_POS', String)
    wi_type = Column('WI_TYPE', String)
    wi_act = Column('WI_ACT', String)
    wi_status = Column('WI_STATUS', String)
    twin_flag = Column('TWIN_FLAG', String)
    twin_wi_no = Column('TWIN_WI_NO', Integer)
    twin_ctn_no = Column('TWIN_CTN_NO', String)
    truck_pos = Column('TRUCK_POS', String)
    dispatch_time = Column('DISPATCH_TIME', DateTime)
    cancel_time = Column('CANCEL_TIME', String)
    confirmed_time = Column('CONFIRMED_TIME', DateTime)
    remark1 = Column('REMARK1', String)
    remark2 = Column('REMARK2', String)
    remark3 = Column('REMARK3', String)
    insert_time = Column('INSERT_TIME', DateTime)
    update_time = Column('UPDATE_TIME', DateTime)
    version = Column('VERSION', Integer)
    lock_flag= Column('LOCK_FLAG', String)
    lock_pavilion = Column('LOCK_PAVILION', String)
    vessel_pos = Column('VESSEL_POS', String)
    vessel_ref = Column('VESSEL_REF', String)
    vessel_class = Column('VESSEL_CLASS', String)


def get_by_id(id):
    session = SessionPool().acquire()
    info = session.query(TosWiInfo).filter(TosWiInfo.id == id).first()
    session.close()
    return info

def is_wi_confirmed(id):
    session = SessionPool().acquire()
    info = session.query(TosWiInfo).filter(TosWiInfo.id == id).first()
    session.close()
    return info != None and info.wi_status=='FINISH'

if __name__ == '__main__':
    print(is_wi_confirmed(100182))
    print(is_wi_confirmed(100183))
    print(is_wi_confirmed(1001813))
