import datetime
import time
from enum import Enum
from typing import Optional
import sqlalchemy
from sqlalchemy import Column, String, TIMESTAMP, Integer
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession
from config.config_manage import ConfigManage

Base = declarative_base()


class Event(Enum):
    takeover = "TAKEOVER"
    reauto = "REAUTO"
    online = "ONLINE"
    offline = "OFFLINE"


class VehicleEvent(Base):
    __tablename__ = 'vehicle_event'

    id = Column('id', Integer, primary_key=True, autoincrement=True)
    vehicle_name = Column('vehicle_name', String(255))
    event = Column('event', String(255))
    event_detail = Column('event_detail', String(255))
    event_description = Column('event_description', String(255))
    timestamp_ms = Column('timestamp_ms', Integer)
    create_time = Column('create_time', TIMESTAMP)
    driver_id = Column('driver_id', Integer)
    driver_name = Column('driver_name', String(255))
    driver_license = Column('driver_license', String(255))
    version = Column('version', Integer)
    take_over_type = Column('take_over_type', String(50))

def is_support():
    if ConfigManage().is_iecs_scene():
        return False
    else:
        return True

def insert(vehicle_name, event, timestamp_ms, event_description=None, driver_id=0, driver_name=None, driver_license=None, version=0, take_over_type=None) -> bool:
    if not is_support():
        return True
    # mysql unique key of <vehicle_name, event, timestamp_ms> to keep insert idempotent
    ret = True
    start = time.time()
    session = MysqlSession().acquire()
    try:
        event = VehicleEvent(vehicle_name=vehicle_name,
                             event=event,
                             timestamp_ms=timestamp_ms,
                             create_time=datetime.datetime.now(),
                             event_description=event_description,
                             driver_id=driver_id,
                             driver_license=driver_license,
                             driver_name=driver_name,
                             version=version,
                             take_over_type=take_over_type)
        logger.info(f"Insert VehicleEvent :{event.__dict__}")
        session.add(event)
        session.commit()
        ret = True
    except Exception as e:
        logger.warning(f"Insert VehicleEvent err{e}")
        ret = False
    finally:
        session.close()
    end = time.time()
    if (end - start) > 1.0:
        logger.warning(f"VehicleEvent insert over cost execeed round time:{round(end - start)}, execeed time:{end - start}")
    return ret


def query(vehicle_name, event, timestamp_ms) -> Optional[VehicleEvent]:
    session = MysqlSession().acquire()
    try:
        return session.query(VehicleEvent).filter_by(vehicle_name=vehicle_name). \
            filter_by(event=event). \
            filter_by(timestamp_ms=timestamp_ms).first()
    except Exception as e:
        logger.warning(f"Query VehicleEvent err{e}")
    finally:
        session.close()


def query_by_id(id, default_ret = None):
    if not is_support():
        return None
    ret = default_ret
    session = MysqlSession().acquire()
    try:
        ret = session.query(VehicleEvent).filter_by(id=id).first()
    except Exception as e:
        logger.warning(f"query_by_id VehicleEvent err{e}")
    finally:
        session.close()
    return ret


def query_by_name(vehicle_name) -> Optional[VehicleEvent]:
    session = MysqlSession().acquire()
    try:
        return session.query(VehicleEvent).filter_by(vehicle_name=vehicle_name).order_by(sqlalchemy.asc(VehicleEvent.timestamp_ms)).all()
    except Exception as e:
        logger.warning(f"Query VehicleEvent err{e}")
    finally:
        session.close()

def query_all() -> Optional[VehicleEvent]:
    session = MysqlSession().acquire()
    try:
        return session.query(VehicleEvent).order_by(sqlalchemy.asc(VehicleEvent.vehicle_name)).order_by(sqlalchemy.asc(VehicleEvent.timestamp_ms)).all()
    except Exception as e:
        logger.warning(f"Query VehicleEvent err{e}")
    finally:
        session.close()


def update_detail(vehicle_name, event, timestamp_ms, event_description=None, driver_id=0, driver_name=None, driver_license=None, version=0):
    if not is_support():
        return True
    # mysql unique key of <vehicle_name, event, timestamp_ms> to keep insert idempotent
    session = MysqlSession().acquire()
    try:
        session.query(VehicleEvent).filter_by(vehicle_name=vehicle_name). \
            filter_by(event=event). \
            filter_by(timestamp_ms=timestamp_ms). \
            update({"event_description": event_description, 
                    "driver_id": driver_id, 
                    "driver_name": driver_name, 
                    "driver_license": driver_license,
                    "version": version})
        session.commit()
    except Exception as e:
        logger.warning(f"Insert VehicleEvent err{e}")
    finally:
        session.close()


def create_talbe():
    import pymysql
    pymysql.install_as_MySQLdb()
    from sqlalchemy_utils import database_exists, create_database, drop_database
    print(f"now start create_talbe ServiceRunStatus....")
    engine=MysqlSession().engine()
    # 1, 删除数据库
    # if  database_exists(engine.url):
    #     drop_database(engine.url)

    # 2, 创建新数据库
    if  not database_exists(engine.url):
        create_database(engine.url)

    VehicleEvent.metadata.drop_all(engine) # Create a table with multiple items
    VehicleEvent.metadata.create_all(engine)
    print(f"now end create_talbe ServiceRunStatus....")

if __name__ == '__main__':
    #create_talbe()
    #insert("AT888", "test", "123","1")
    #update_detail("AT888", "test", "123", "222")
    # q = query("AT888", "test", "123")
    # q = query_all()
    q = query_by_name('JT810')
    for info in q:
        print(info.__dict__)
    #print(q.__dict__)
