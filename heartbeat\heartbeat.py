from cache.ecs_command_cache import EcsCommandCache
from cache.scheduling_cache import ShipLoadSchedulingCache
from cache.services_heartbeat_cache import ServicesHeartbeatCache
from common.constant import MSGSM_WECHAT_URL, DOCKER_NAME
from common.logger import logger
from common.periodical_task import PeriodicalTask
from common.singleton import Singleton
from config.config_manage import ConfigManage
from model import wechat_message
from cache.lock_cache import LockCache
from proto import ecs_command_pb2
from model.hdmap_client import HdmapClient
from proto import cache_pb2
from cache import vehicle_cache
from common.common_util import is_in_polygon
from common.name_converter import NameConverter
import uuid
import time


class Heartbeat(metaclass=Singleton):
    def __init__(self):
        self._services_heartbeat_detection = PeriodicalTask(target=self.services_heartbeat_detection, interval=1,
                                                            master_only=True)
        self._ecs_command_cache = EcsCommandCache()
        self._services_heartbeat_cache = ServicesHeartbeatCache()
        self._ship_load_sch_cache = ShipLoadSchedulingCache()
        self._lock_cache = LockCache()
        self._services_heartbeat_disable_count = 0
        self._services_last_frame_alive_dict = {}  # 记录上次检测状态
        self._services_config = ConfigManage().get_keepalive_services()
        self._services_heartbeat_functions = {'nb-ecs': self.nb_ecs_heartbeat_function,
                                              'ship-load-sch': self.ship_load_sch_heartbeat_function,
                                              'lock-pavilion': self.lock_pavilion_heartbeat_function}  # 不同服务的处理函数
        self._lock_service_bind_vehicles = []
        self._lock_station_fence_map = {}  # key: 锁亭名称, value: 矩形区域范围 (梅山)

    def start(self):
        self._services_heartbeat_detection.start()

    def services_heartbeat_detection(self):
        # 服务重启完成后, 前一千次检测(60s)不使能
        if self._services_heartbeat_disable_count < 30:
            logger.debug(f'[services_heartbeat_detection] count: {self._services_heartbeat_disable_count}')
            self._services_heartbeat_disable_count += 1
            return

        service_status = cache_pb2.HeartbeatServiceStatus()

        for server_name in self._services_config:
            if not self._services_config[server_name]['enable']:
                continue
            acceptable_delay_ms = self._services_config[server_name]['acceptable_delay_ms']
            is_service_alive = self._services_heartbeat_cache.is_service_alive(server_name, acceptable_delay_ms)
            service_status.service_status[server_name] = is_service_alive
            last_frame_is_service_alive = self._services_last_frame_alive_dict.get(server_name, True)
            self._services_heartbeat_functions[server_name](server_name, is_service_alive, last_frame_is_service_alive)

        self._services_heartbeat_cache.set_keep_alived_service_status(service_status)
        logger.debug(f'[services_heartbeat_detection] loop, {service_status}')


    def nb_ecs_heartbeat_function(self, server_name, is_service_alive, last_frame_is_service_alive):
        """
            云控服务心跳检测函数，异常全场刹停
        """
        logger.debug(f'[nb_ecs_heartbeat_function] now: {is_service_alive}, last: {last_frame_is_service_alive}')
        if is_service_alive:
            # 服务本次检测正常但上次检测异常，更新服务状态
            if not last_frame_is_service_alive:
                self._services_last_frame_alive_dict[server_name] = True
                logger.info(f'[nb_ecs_heartbeat_function] {DOCKER_NAME} build connection with {server_name}')
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(
                    f'INFO:  {DOCKER_NAME} build connection with {server_name} ')
        else:
            # 服务本次检测异常但上次检测正常，更新服务状态，开始异常处理：全场停车、发送异常提示
            # 服务本次检测异常但上次检测异常，不再重复处理
            if last_frame_is_service_alive:
                self._services_last_frame_alive_dict[server_name] = False
                self._ecs_command_cache.dispatch_all_slow_brake_command("antenna-server与云控心跳超时")
                logger.warning(f'[nb_ecs_heartbeat_function] {DOCKER_NAME} lose connection with {server_name}')
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(
                    f'WARNING:  {DOCKER_NAME} lose connection with {server_name}')

    def ship_load_sch_heartbeat_function(self, server_name, is_service_alive, last_frame_is_service_alive):
        """
            装船调度服务心跳检测函数，异常备份配置，正常恢复
        """
        logger.debug(f'[ship_load_sch_heartbeat_function] now: {is_service_alive}, last: {last_frame_is_service_alive}')
        if is_service_alive:
            # 服务本次检测正常但上次检测异常，更新服务状态，开始恢复处理：恢复装船调度
            if not last_frame_is_service_alive:
                self._services_last_frame_alive_dict[server_name] = True
                self._ship_load_sch_cache.recovery_ship_load_sch_config()
                logger.info(f'[ship_load_sch_heartbeat_function] {DOCKER_NAME} build connection with {server_name}')
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(
                    f'INFO:  {DOCKER_NAME} build connection with {server_name}, recovery ship load scheduling')
        else:
            # 服务本次检测异常但上次检测正常，更新服务状态，开始异常处理：关闭装船调度
            # 服务本次检测异常但上次检测异常，不再重复处理
            if last_frame_is_service_alive:
                self._services_last_frame_alive_dict[server_name] = False
                self._ship_load_sch_cache.disable_ship_load_sch_config()
                logger.warning(f'[ship_load_sch_heartbeat_function] {DOCKER_NAME} lose connection with {server_name}')
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(
                    f'WARNING:  {DOCKER_NAME} lose connection with {server_name}, disable ship load scheduling')

    def dispatch_lock_except_brake(self, vehicle_name, execute_type):
        command = ecs_command_pb2.EcsCommand()
        command.vehicle_name = vehicle_name
        command.user_id = 'FMS'
        command.timestamp = int(time.time())
        command.command_type = ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE
        command.execute_type = execute_type
        if command.execute_type == ecs_command_pb2.EcsCommandExecuteType.CANCEL:
            self._ecs_command_cache.cancel_brake_command(command)
        elif command.execute_type == ecs_command_pb2.EcsCommandExecuteType.DISPATCH:
            self._ecs_command_cache.dispatch_brake_command(command)

    def in_which_lock_station(self, localization):
        for name, fence in self._lock_station_fence_map.items():
            if is_in_polygon(fence, localization):
                return name
        return None

    def lock_pavilion_heartbeat_function(self, server_name, is_service_alive, last_frame_is_service_alive):
        """
            锁亭服务心跳检测函数，异常刹停所有锁亭周边车辆，正常恢复
        """
        logger.debug(f'[lock_pavilion_heartbeat_function] now: {is_service_alive}, last: {last_frame_is_service_alive}')
        if is_service_alive:
            # 服务本次检测正常但上次检测异常，更新服务状态，开始恢复处理
            if not last_frame_is_service_alive:
                self._services_last_frame_alive_dict[server_name] = True
                vehicle_set = []
                for truck_no in NameConverter().all_truck_no():
                    # 找到所有因为锁亭服务异常导致急刹的车辆并解除
                    brake_status = self._ecs_command_cache.get_brake_command_status(ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE, truck_no)
                    if brake_status.status == ecs_command_pb2.EcsCommandExecuteStatus.DISPATCH:
                        vehicle_set.append(truck_no)
                        self.dispatch_lock_except_brake(truck_no, ecs_command_pb2.EcsCommandExecuteType.CANCEL)
                # 取消所有因为锁站异常导致的车辆刹车
                info_log = (f'[lock_pavilion_heartbeat_function] {DOCKER_NAME} build connection with {server_name}, '
                            f'cancel slow brake: {vehicle_set}')
                logger.info(info_log)
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(info_log)
        else:
            if last_frame_is_service_alive:
                # 服务本次检测异常但上次检测正常，更新服务状态并告警
                self._services_last_frame_alive_dict[server_name] = False
                error_log = (f'[lock_pavilion_heartbeat_function] {DOCKER_NAME} lose connection with {server_name}, '
                             f'dispatch slow brake: {self._lock_service_bind_vehicles}')
                logger.warning(error_log)
                wechat_message.WeChatMessage(MSGSM_WECHAT_URL).send_message(error_log)
            else:
                # 服务连续异常期间, 找到所有锁亭电子围栏内的车辆并刹车, 如果已经在刹车则不重复派发
                self._lock_station_fence_map = HdmapClient().get_electronic_fence(self._lock_cache.get_ms_online_lock_names(), 5, 17, "port_meishan")
                vehicle_set = []
                # 1.获取所有在线车辆
                for truck_no in vehicle_cache.get_online_trucks():
                    logger.debug(f'[lock_pavilion_heartbeat_function] online vehicles: {truck_no}')
                    status = vehicle_cache.get_vehicle_status(truck_no)
                    if status is not None:
                        # 2. 获取车辆所属锁亭
                        localization = (status.position.utm_x, status.position.utm_y)
                        lock_name = self.in_which_lock_station(localization)
                        if lock_name is not None and self._lock_cache.is_lock_station_emergency_brake_enable(lock_name):
                            # 3. 如果该锁亭配置中启用刹车控制并且单车没有刹车，需要派发刹车，已经派发刹车不需要重复派发，否则内存会升高
                            brake_status = self._ecs_command_cache.get_brake_command_status(ecs_command_pb2.EcsCommandType.LOCK_SERVICE_EXCEPT_UNRECOVERABLE_EMERGENCY_BRAKE, truck_no)
                            if brake_status.status == ecs_command_pb2.EcsCommandExecuteStatus.CANCELED:
                                vehicle_set.append(truck_no)
                                self.dispatch_lock_except_brake(truck_no, ecs_command_pb2.EcsCommandExecuteType.DISPATCH)
                logger.warning(f'[lock_pavilion_heartbeat_function], dispatch slow brake: {vehicle_set}')

