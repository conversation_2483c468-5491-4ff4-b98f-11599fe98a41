DROP TABLE IF EXISTS T_WI_INFO_4V_TOS;
DROP TABLE IF EXISTS T_WI_INFO_4V_DC;
DROP TABLE IF EXISTS T_TRUCK_INFO_4V;
DROP TABLE IF EXISTS T_CRANE_INFO_4V;
DROP TABLE IF EXISTS T_CRANE_INFO_4V_DEBUG;

CREATE TABLE T_WI_INFO_4V_TOS
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12),
  TRUCK_SEQ VARCHAR(12),
  WI_NO bigint,
  WI_ID bigint,
  CTN_NO VARCHAR(12),
  EQUIT_TYPE VARCHAR(4),
  TEU int,
  FROM_POS VARCHAR(12),
  TO_POS VARCHAR(12),
  WI_TYPE VARCHAR(4),
  WI_ACT VARCHAR(12),
  WI_STATUS VARCHAR(12),
  TWIN_FLAG VARCHAR(1),
  TWI<PERSON>_WI_NO bigint,
  TWIN_CTN_NO VARCHAR(12),
  T<PERSON>UCK_POS VARCHAR(1),
  DISPATCH_TIME DATETIME,
  CANCEL_TIME DATETIME,
  CONFIRMED_TIME DATETIME,
  REMARK1 VARCHAR(50),
  REMARK2 VARCHAR(50),
  REMARK3 VARCHAR(50),
  CTN_WEIGHT int,
  POW_NAME VARCHAR(12),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  VERSION int default 0,
  RESET_VERSION int default 0,
  VESSEL_POS VARCHAR(12),
  ARRIVE_AREA VARCHAR(12),
  TOS_SOURCE VARCHAR(12),
  DEVICE_DISABLE VARCHAR(12),
  CTRL_SPEED int,
  CTRL_STOP_NODE TEXT,
  CTRL_WAIT_NODE TEXT,
  CTRL_LOCK_NODE TEXT,
  CTRL_QUEUE_NODE TEXT,
  CTRL_TURN_NODE TEXT,
  CTRL_CHECK_NODE TEXT,
  CTRL_ACTION VARCHAR(12),
  CTRL_CUR_WIS VARCHAR(50),
  CTRL_CUR_WI VARCHAR(50),
  CTRL_CUR_IDS VARCHAR(50),
  CTRL_CUR_ID VARCHAR(50),
  CTRL_INSERT_TIME DATETIME,
  CTRL_UPDATE_TIME DATETIME,
  CTRL_STATUS VARCHAR(12),
  CTRL_VERSION int default 0,
  CTRL_RESET_VERSION int default 0,
  CMD_TYPE  VARCHAR(50),
  INDEX(TRUCK_NO),
  INDEX(WI_NO),
  INDEX(WI_ID),
  INDEX(TWIN_WI_NO),
  #yongzhou_cs
  REST_POS VARCHAR(12),
  LANE_NO VARCHAR(12),
  VESSEL_DIRECTION VARCHAR(12),
  BRIDGE_UP VARCHAR(12),
  BRIDGE_DOWN VARCHAR(12),
  TWIN_TYPE VARCHAR(12),
  TO_POS_TYPE int default 0,
  TO_POS_X DOUBLE,
  TO_POS_Y DOUBLE
);

CREATE TABLE T_WI_INFO_4V_DC
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12),
  TRUCK_SEQ VARCHAR(12),
  WI_NO bigint,
  WI_ID bigint,
  CTN_NO VARCHAR(12),
  EQUIT_TYPE VARCHAR(4),
  TEU int,
  FROM_POS VARCHAR(12),
  TO_POS VARCHAR(12),
  WI_TYPE VARCHAR(4),
  WI_ACT VARCHAR(12),
  WI_STATUS VARCHAR(12),
  TWIN_FLAG VARCHAR(1),
  TWIN_WI_NO bigint,
  TWIN_CTN_NO VARCHAR(12),
  TRUCK_POS VARCHAR(1),
  DISPATCH_TIME DATETIME,
  CANCEL_TIME DATETIME,
  CONFIRMED_TIME DATETIME,
  REMARK1 VARCHAR(50),
  REMARK2 VARCHAR(50),
  REMARK3 VARCHAR(50),
  CTN_WEIGHT int,
  POW_NAME VARCHAR(12),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  START_TIME DATE,
  ARRIVE_TIME DATE,
  FINISH_TIME DATE,
  TRUCK_STATUS VARCHAR(12),
  VERSION int default 0,
  RESET_VERSION int default 0,
  VESSEL_POS VARCHAR(12),
  ARRIVE_AREA VARCHAR(12),
  TOS_SOURCE VARCHAR(12),
  DEVICE_DISABLE VARCHAR(12),
  CTRL_SPEED int,
  CTRL_STOP_NODE TEXT,
  CTRL_WAIT_NODE TEXT,
  CTRL_LOCK_NODE TEXT,
  CTRL_QUEUE_NODE TEXT,
  CTRL_TURN_NODE TEXT,
  CTRL_CHECK_NODE TEXT,
  CTRL_ACTION VARCHAR(12),
  CTRL_CUR_WIS VARCHAR(50),
  CTRL_CUR_WI VARCHAR(50),
  CTRL_CUR_IDS VARCHAR(50),
  CTRL_CUR_ID VARCHAR(50),
  CTRL_INSERT_TIME DATETIME,
  CTRL_UPDATE_TIME DATETIME,
  CTRL_STATUS VARCHAR(12),
  CTRL_VERSION int default 0,
  CTRL_RESET_VERSION int default 0,
  CMD_TYPE  VARCHAR(50),
  INDEX(TRUCK_NO),
  INDEX(WI_NO),
  INDEX(WI_ID),
  INDEX(TWIN_WI_NO),
  #yongzhou_cs
  REST_POS VARCHAR(12),
  LANE_NO VARCHAR(12),
  VESSEL_DIRECTION VARCHAR(12),
  BRIDGE_UP VARCHAR(12),
  BRIDGE_DOWN VARCHAR(12),
  TWIN_TYPE VARCHAR(12),
  TO_POS_TYPE int default 0,
  TO_POS_X DOUBLE,
  TO_POS_Y DOUBLE
);


CREATE TABLE T_TRUCK_INFO_4V
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12) NOT NULL,
  POS_X DOUBLE,
  POS_Y DOUBLE,
  ALTITUDE FLOAT,
  ANGEL FLOAT,
  CUR_POS VARCHAR(12),
  POWER_TYPE VARCHAR(14),
  REST_OIL FLOAT,
  REST_ELECTRIC FLOAT,
  SPEED FLOAT,
  SENSOR_STATUS VARCHAR(50),
  TRUCK_MODE int,
  ACTION VARCHAR(12),
  CUR_WIS VARCHAR(50),
  CUR_IDS VARCHAR(50),
  TRUCK_STATUS VARCHAR(12),
  DRIVING_STATE int,
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  NODE_PATH TEXT,
  PATH1 TEXT,
  PATH2 TEXT,
  INDEX(TRUCK_NO)
);

CREATE TABLE T_CRANE_INFO_4V
(
  ID int PRIMARY KEY auto_increment,
  CRANE_ID VARCHAR(4),
  LANE_NO VARCHAR(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  BERTH VARCHAR(3),
  DRIVE_DIRECTION VARCHAR(1),
  BRIDGE_UP VARCHAR(4),
  BRIDGE_DOWN VARCHAR(4),
  VESSEL_REFNO VARCHAR(16),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  INDEX(CRANE_ID)
);

CREATE TABLE T_CRANE_INFO_4V_DEBUG
(
  ID int PRIMARY KEY auto_increment,
  CRANE_ID VARCHAR(4),
  LANE_NO VARCHAR(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  BERTH VARCHAR(3),
  DRIVE_DIRECTION VARCHAR(1),
  BRIDGE_UP VARCHAR(4),
  BRIDGE_DOWN VARCHAR(4),
  VESSEL_REFNO VARCHAR(16),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  INDEX(CRANE_ID)
);

CREATE TABLE ship_info (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    SHIP_NAME VARCHAR(255) COLLATE utf8mb4_bin  NOT NULL,
    SHIP_LENGTH VARCHAR(255) COLLATE utf8mb4_bin ,
    BERTHING_DIRECTION VARCHAR(50) COLLATE utf8mb4_bin ,
    WORK_FACE VARCHAR(255) COLLATE utf8mb4_bin ,
    STATUS VARCHAR(100) COLLATE utf8mb4_bin ,
    INSERT_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ;

SET GLOBAL event_scheduler = ON;
SET @@global.event_scheduler = ON;


DELIMITER //
CREATE PROCEDURE KeepDatasWith30Days()
BEGIN
  SELECT @maxId:=max(ID) FROM T_WI_INFO_4V_TOS  WHERE DISPATCH_TIME<DATE_SUB(NOW(),INTERVAL 1 YEAR);
  DELETE FROM T_WI_INFO_4V_TOS  WHERE ID <@maxId LIMIT 100000;
  DELETE FROM T_WI_INFO_4V_DC  WHERE ID <@maxId LIMIT 100000;
END //
DELIMITER ;
DROP EVENT IF EXISTS del_event;
CREATE EVENT del_event
  ON SCHEDULE EVERY 1 MONTH STARTS '2023-01-01 00:00:00'
  DO CALL KeepDatasWith30Days();

SHOW EVENTS;
