import datetime
import time
import traceback

from sqlalchemy import Column, String, TIMESTAMP, Integer
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession
from proto.antenna_pb2 import CommandStatus
from config.config_manage import ConfigManage

Base = declarative_base()


class SubCommandExecutionInfo(Base):
    __tablename__ = 'sub_command_execution_info'

    id = Column('id', Integer, primary_key=True)
    sub_command_uuid = Column('sub_command_uuid', String)
    vehicle_name = Column('vehicle_name', String)
    status = Column('status', String)
    detail_status = Column('detail_status', String)
    timestamp_ms = Column('timestamp_ms', Integer)
    create_time = Column('create_time', TIMESTAMP)


def is_support():
    if ConfigManage().is_sim_mode() or ConfigManage().is_iecs_scene():
        return False
    else:
        return True

def insert(vehicle_name, sub_command_uuid, status, detail_status, insert_time = 0):
    if not is_support():
        return
    start = time.time()
    session = MysqlSession().acquire()
    try:
        result = SubCommandExecutionInfo(vehicle_name=vehicle_name,
                                         sub_command_uuid=sub_command_uuid,
                                         status=CommandStatus.ExecuteStatus.Name(status),
                                         detail_status=CommandStatus.DetailStatus.Name(detail_status),
                                         timestamp_ms=int(time.time() * 1000),
                                         create_time=datetime.datetime.now()
                                         )
        logger.info(f"Insert SubCommandExecutionInfo :{result.__dict__}")
        session.add(result)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert SubCommandExecutionInfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()
    end = time.time()
    if (end - start) > 1.0 or (end - insert_time) > 1.0:
        logger.warning(f"SubCommandExecutionInfo insert over cost execeed round time:{round(end - start)}, execeed time:{end - start},"
                     f"execute duration round time:{round(end - insert_time)},execute duration:{end-insert_time}")
    return


def async_insert(async_handler, vehicle_name, sub_command_uuid, status, detail_status):
    if not is_support():
        return
    logger.debug(f"Insert SubCommandExecutionInfo :vehicle_name:{vehicle_name},sub_command_uuid:{sub_command_uuid},status:{status}")
    async_handler.submit(insert, vehicle_name, sub_command_uuid, status, detail_status, time.time())
