import json
import os

_truck_no_map = None
_truck_no_data_dir: str = "data/meishan/vehicle"


def init_truck_no_map():
    global _truck_no_map
    if _truck_no_map is not None:
        return
    for vehicle_file in os.listdir(_truck_no_data_dir):
        if not vehicle_file.endswith(".json"):
            continue
        _truck_no_map = json.load(open(os.path.join(_truck_no_data_dir, vehicle_file)))


def get_vin(truck_no: str) -> str:
    """
    查询vin，例如输入 T500 返回 KW353745。
    """
    if _truck_no_map is None:
        init_truck_no_map()
    return _truck_no_map.get(truck_no, None)


if __name__ == "__main__":
    print(get_vin("T500"))
