import datetime

from sqlalchemy import Column, Integer, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base

from model.connection import GisSession

Base = declarative_base()


class DeviceBase(Base):
    __tablename__ = 'DEVICE_BASE'
    gid = Column('GID', Integer, primary_key=True)
    code = Column('CODE', String)
    x = Column('X', Float)
    y = Column('Y', Float)
    gps_time = Column('GPSTIME', DateTime)
    update_time = Column('UPDATETIME', DateTime)


def get_by_code(code):
    session = GisSession().acquire()
    try:
        info = session.query(DeviceBase).filter(DeviceBase.code == code).first()
        return info
    finally:
        session.close()


def get_all_crane():
    session = GisSession().acquire()
    try:
        threshold = datetime.datetime.now() + datetime.timedelta(seconds=10)
        info = session.query(DeviceBase).filter(
            DeviceBase.code.like('QC%')).filter(
            DeviceBase.gps_time > threshold).all()
        return info
    finally:
        session.close()


if __name__ == '__main__':
    info = get_all_crane()
    print(len(info))
    for i in info:
        print(i.code, i.x, i.y)
