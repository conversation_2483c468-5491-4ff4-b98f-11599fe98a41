from typing import List, Dict

from sqlalchemy import Column, Integer, String, DateTime, inspect
from sqlalchemy.ext.declarative import declarative_base

from model.connection import TosSession

TABLE_CRANE_INFO_DEBUG = 'T_CRANE_INFO_4V_DEBUG'
TABLE_CRANE_INFO = 'T_CRANE_INFO_4V'

Base = declarative_base()


class CraneInfo(Base):
    __tablename__ = 'T_CRANE_INFO_4V'
    gid = Column('ID', Integer, primary_key=True)
    crane_id = Column('CRANE_ID', String)
    land_no = Column('LANE_NO', String)
    bay1 = Column('BAY1', String)
    bay2 = Column('BAY2', String)
    bay3 = Column('BAY3', String)
    vessel_direction = Column('VESSEL_DIRECTION', String)
    gps_time = Column('INSERT_TIME', DateTime)
    update_time = Column('UPDATE_TIME', DateTime)

    def _asdict(self):
        return {c.key: getattr(self, c.key)
                for c in inspect(self).mapper.column_attrs}


def get_by_crane_no(crane_no, debug=False):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        info = session.query(CraneInfo).filter(CraneInfo.crane_id == f"CR{crane_no}").first()
        return info
    finally:
        session.close()


def batch_get_by_crane_no(crane_nos: List[int], debug: bool):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        crane_ids = []
        for id in crane_nos:
            crane_ids.append(f"CR{id}")
        cranes = session.query(CraneInfo).filter(CraneInfo.crane_id.in_(crane_ids)).all()
        results = {}
        for crane in cranes:
            crane_no = int(crane.crane_id[2:])
            results[crane_no] = crane
        return results
    finally:
        session.close()


def get_all_crane(debug=False):
    session = TosSession().acquire()
    CraneInfo.__table__.name = TABLE_CRANE_INFO_DEBUG if debug else TABLE_CRANE_INFO
    try:
        info = session.query(CraneInfo).all()
        return info
    finally:
        session.close()


if __name__ == '__main__':
    infos = get_all_crane(True)
    for k in infos:
        print(k.__dict__)
    print(len(infos))
    print(CraneInfo.__table__.name)
