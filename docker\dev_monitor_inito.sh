#!/usr/bin/env bash

DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
source $DOCKER_PATH/common.sh

SHELL=zsh

if [ -S $SSH_AUTH_SOCK ]; then
    ln -f $SSH_AUTH_SOCK ${SSH_AGENT_DIR}/agent.sock 2>/dev/null >/dev/null
fi

docker exec \
    -e COLORTERM=$COLORTERM \
    -e DISPLAY=${DOCKER_DISPLAY} \
    -u $USER \
    -it $DOCKER_NAME \
    /bin/$SHELL
