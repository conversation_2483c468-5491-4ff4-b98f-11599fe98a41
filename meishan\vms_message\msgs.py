import datetime
import json
from dataclasses import dataclass,field
from typing import List
from dataclasses_json import config,dataclass_json
from common.logger import logger





#iecs发送给fabu的topic
VMS_IECS_INFO_TOPIC = "ods_blctms_iecs2fabu_info"
#iecs堆高机作业贝type
VMS_STACK_INFO_TYPE = "IECS_WORKBAY"

#fms发送给ntos的topic
FMS_TO_NTOS_TOPIC = "ods_blctms_fabu2ntos_info"
#fms触发达到type
FMS_ARRIVE_TYPE = "FMS_ARRIVE"

#ntos发送给fabu
NTOS_TO_FMS_TOPIC = "ods_blctms_ntos2fabu_info"
#龙门吊作业位置信息
FMS_RTGWORKPOS_TYPE = "FMS_RTGWORKPOS"

VMS_IECS_TRUCK_ARRIVE_TOPIC = "ods_blctms_fabu2iecs_info"
VMS_IECS_TRUCK_ARRIVE_TYPE = "IECS_TRUCK_ARRIVE"

VMS_GPS_INFO_TOPIC = "ods_blctms_qx_gps_vehicleinfo"
VMS_GPS_INFO_TYPE = "FMS_GPS"

def get_object_hook(cls):
    def hook(dct):
        obj = cls.__new__(cls)
        for key, value in dct.items():
            if key not in cls.__annotations__:
                continue
            typ = cls.__annotations__[key]
            if typ is int or typ is str or typ is float:
                setattr(obj, key, value)
            elif typ is datetime:
                #d = datetime.datetime.strptime(value, 'datetime.datetime(%Y, %m, %d, %H, %M, %S)')
                d = datetime.fromtimestamp(value)
                setattr(obj, key, d)
            elif typ == list:  # 处理列表的情况
                # 假设列表中的元素都是同一类型的对象
                element_cls = typ.__args__[0]  # 获取列表中元素的类型
                setattr(obj, key, [get_object_hook(element_cls)(el) for el in value])
            else:
                # 如果类型不是基本类型，也不是datetime，也不是列表，那么递归处理
                setattr(obj, key, hook(value))
        return obj

    return hook

def from_json(cls, json_str):
    return json.loads(json_str, object_hook=get_object_hook(cls))

def make_msg(msg_type:str,msg_body:str):
    current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")
    msg = VmsMsg(MSG_ID = current_time,
          MSG_TYPE = "PUSH",
          MSG_JOB_TYPE = msg_type,
          MSG_TIME = current_time[:-3],
          MSG_SOURCE = "MSCT-FMS-V1",
          MSG_STATUS = "0",
          MSG_BODY = msg_body)
    return msg

@dataclass
class MsgHead:
    MSG_ID: str
    MSG_TYPE: str
    MSG_JOB_TYPE: str
    MSG_TIME: str
    MSG_SOURCE: str
    MSG_STATUS: str
    MSG_BODY: str


@dataclass_json
@dataclass
class VmsMsg:
    MSG_ID: str
    MSG_TYPE: str
    MSG_JOB_TYPE: str
    MSG_TIME: str
    MSG_SOURCE: str
    MSG_STATUS: str
    MSG_BODY: str

@dataclass
class VmsWorkBayMsg:
    YARD_ID: str   = ""
    BAYSET: List[int] = field(default_factory=list)

@dataclass
class VmsArriveMsg:
    ARRIVE_TYPE: int = 0
    TRUCK_NO: str   = ""
    BLOCK_CODE: str   = ""
    ARRIVE_STATE:int = 0
    WI_NO: str = ""
    CNT_NO: str = ""
    ARRIVE_TIME: str = ""
    ARRIVE_STATE_EXPECT: int = 0


if __name__ == '__main__':
    pass
