DROP TABLE T_WI_INFO_4V_TOS;
DROP TABLE T_WI_INFO_4V_DC;
DROP TABLE T_CONTROL_4V;
DROP TABLE T_TRUCK_INFO_4V;
DROP TABLE T_CRANE_INFO_4V;
DROP TABLE T_CRANE_INFO_4V_DEBUG;

CREATE TABLE T_WI_INFO_4V_TOS
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12),
  TRUCK_SEQ VARCHAR(12),
  WI_NO int,
  WI_ID int,
  CTN_NO VARCHAR(12),
  EQUIT_TYPE VARCHAR(4),
  TEU int,
  FROM_POS VARCHAR(12),
  TO_POS VARCHAR(12),
  WI_TYPE VARCHAR(4),
  WI_ACT VARCHAR(12),
  WI_STATUS VARCHAR(12),
  TWIN_FLAG VARCHAR(1),
  TWIN_WI_NO int,
  TWIN_CTN_NO VARCHAR(12),
  TR<PERSON>K_POS VARCHAR(1),
  D<PERSON><PERSON><PERSON>H_TIME DATETIME,
  CANCEL_TIME DATETIME,
  CONFIRMED_TIME DATETIME,
  REMARK1 VARCHAR(12),
  REMARK2 VARCHAR(12),
  REMARK3 VARCHAR(12),
  CTN_WEIGHT int,
  POW_NAME VARCHAR(12),
  LOCK_FLAG VARCHAR(12),
  LOCK_PAVILION VARCHAR(12),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  VERSION int default 0,
  RESET_VERSION int default 0,
  VESSEL_POS VARCHAR(12),
  VESSEL_REF VARCHAR2(12),
  VESSEL_CLASS VARCHAR2(12),
  CTRL_SPEED int,
  CTRL_STOP_NODE TEXT,
  CTRL_WAIT_NODE TEXT,
  CTRL_LOCK_NODE TEXT,
  CTRL_QUEUE_NODE TEXT,
  CTRL_TURN_NODE TEXT,
  CTRL_CHECK_NODE TEXT,
  CTRL_ACTION VARCHAR(12),
  CTRL_CUR_WIS VARCHAR(50),
  CTRL_CUR_WI VARCHAR(50),
  CTRL_CUR_IDS VARCHAR(50),
  CTRL_CUR_ID VARCHAR(50),
  CTRL_INSERT_TIME DATETIME,
  CTRL_UPDATE_TIME DATETIME,
  CTRL_STATUS VARCHAR(12),
  CTRL_VERSION int default 0,
  CTRL_RESET_VERSION int default 0,
  CMD_TYPE  VARCHAR(50),
  INDEX(TRUCK_NO)
);

CREATE TABLE T_WI_INFO_4V_DC
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12),
  TRUCK_SEQ VARCHAR(12),
  WI_NO int,
  WI_ID int,
  CTN_NO VARCHAR(12),
  EQUIT_TYPE VARCHAR(4),
  TEU int,
  FROM_POS VARCHAR(12),
  TO_POS VARCHAR(12),
  WI_TYPE VARCHAR(4),
  WI_ACT VARCHAR(12),
  WI_STATUS VARCHAR(12),
  TWIN_FLAG VARCHAR(1),
  TWIN_WI_NO int,
  TWIN_CTN_NO VARCHAR(12),
  TRUCK_POS VARCHAR(1),
  DISPATCH_TIME DATETIME,
  CANCEL_TIME DATETIME,
  CONFIRMED_TIME DATETIME,
  REMARK1 VARCHAR(12),
  REMARK2 VARCHAR(12),
  REMARK3 VARCHAR(12),
  CTN_WEIGHT int,
  POW_NAME VARCHAR(12),
  LOCK_FLAG VARCHAR(12),
  LOCK_PAVILION VARCHAR(12),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  START_TIME DATE,
  ARRIVE_TIME DATE,
  FINISH_TIME DATE,
  TRUCK_STATUS VARCHAR(12),
  VERSION int default 0,
  RESET_VERSION int default 0,
  CTRL_SPEED int,
  CTRL_STOP_NODE TEXT,
  CTRL_WAIT_NODE TEXT,
  CTRL_LOCK_NODE TEXT,
  CTRL_QUEUE_NODE TEXT,
  CTRL_TURN_NODE TEXT,
  CTRL_CHECK_NODE TEXT,
  CTRL_ACTION VARCHAR(12),
  CTRL_CUR_WIS VARCHAR(50),
  CTRL_CUR_WI VARCHAR(50),
  CTRL_CUR_IDS VARCHAR(50),
  CTRL_CUR_ID VARCHAR(50),
  CTRL_INSERT_TIME DATETIME,
  CTRL_UPDATE_TIME DATETIME,
  CTRL_STATUS VARCHAR(12),
  CTRL_VERSION int default 0,
  CTRL_RESET_VERSION int default 0,
  CMD_TYPE  VARCHAR(50),
  INDEX(TRUCK_NO)
);

CREATE TABLE T_CONTROL_4V
(
  CID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12) NOT NULL,
  SPEED int,
  STOP_NODE TEXT,
  WAIT_NODE TEXT,
  LOCK_NODE TEXT,
  QUEUE_NODE TEXT,
  TURN_NODE TEXT,
  CHECK_NODE TEXT,
  ACTION VARCHAR(12),
  CUR_WIS VARCHAR(50),
  CUR_IDS VARCHAR(50),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  STATUS VARCHAR(12),
  VERSION int default 0,
  INDEX(TRUCK_NO)
);

CREATE TABLE T_TRUCK_INFO_4V
(
  ID int PRIMARY KEY auto_increment,
  TRUCK_NO VARCHAR(12) NOT NULL,
  POS_X float(11,8),
  POS_Y float(11,8),
  ALTITUDE FLOAT,
  ANGEL FLOAT,
  CUR_POS VARCHAR(12),
  POWER_TYPE VARCHAR(14),
  REST_OIL FLOAT,
  REST_ELECTRIC FLOAT,
  SPEED FLOAT,
  SENSOR_STATUS VARCHAR(50),
  TRUCK_MODE int,
  ACTION VARCHAR(12),
  CUR_WIS VARCHAR(50),
  CUR_IDS VARCHAR(50),
  TRUCK_STATUS VARCHAR(12),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  NODE_PATH TEXT,
  PATH1 TEXT,
  PATH2 TEXT,
  INDEX(TRUCK_NO)
);

CREATE TABLE T_CRANE_INFO_4V
(
  ID int PRIMARY KEY auto_increment,
  CRANE_ID VARCHAR(4),
  LANE_NO VARCHAR(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  BERTH VARCHAR(3),
  DRIVE_DIRECTION VARCHAR(1),
  BRIDGE_UP VARCHAR(4),
  BRIDGE_DOWN VARCHAR(4),
  VESSEL_REFNO VARCHAR(16),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  INDEX(CRANE_ID)
);

CREATE TABLE T_CRANE_INFO_4V_DEBUG
(
  ID int PRIMARY KEY auto_increment,
  CRANE_ID VARCHAR(4),
  LANE_NO VARCHAR(4),
  BAY1 VARCHAR(4),
  BAY2 VARCHAR(4),
  BAY3 VARCHAR(4),
  VESSEL_DIRECTION VARCHAR(1),
  BERTH VARCHAR(3),
  DRIVE_DIRECTION VARCHAR(1),
  BRIDGE_UP VARCHAR(4),
  BRIDGE_DOWN VARCHAR(4),
  VESSEL_REFNO VARCHAR(16),
  INSERT_TIME DATETIME,
  UPDATE_TIME DATETIME,
  INDEX(CRANE_ID)
);


