import time
import traceback
import psutil

from google.protobuf.text_format import Message<PERSON><PERSON><PERSON>tring
from google.protobuf.json_format import MessageToDict
from common.logger import logger
from proto.cache_pb2 import GantryStoreInfo, GantryStoreWarningInfo
from common.periodical_task import PeriodicalTask
from cache import plc_cache
from plc.model import STORE_GANTRY_IP_MAP, GANTRY_STORE_PORT, GANTRY_STORE_MAX_MSG_NO, make_gantry_store_plc
from common.common_util import time_valid
from plc.gantry_storer import GantryStorer
from config.config_manage import ConfigManage
from cache.config_cache import ConfigCache
from model.tcp_server import PersistentTcpServer


class GantryStore(object):
    def __init__(self):
        self._running = False
        self._store_threads = []
        self._check_alive_thread = None
        self._update_store_gantry_no_thread = None
        self._auto_delete_deadline_thread = None
        self._auto_delete_deadline_threshold = 60.0
        self._storers = []
        self._plc_cache = None
        self._interval = 0.15
        self._storers_plc_dict = {}
        self._storers_warning_plc_dict = {}
        self._storers_gantry_warning_plc_dict = {}
        self._storers_gantry = sorted(list(STORE_GANTRY_IP_MAP.keys()))
        self._global_config = dict()
        self._check_alive_config = dict()
        self._check_alive_interval = 0.3
        self._check_alive_count = 0
        self._check_unalive_count = 0
        self._check_alive_status = False
        self._config_cache = None
        self._force_work = False

    def init_config_for_test(self):
        # dev环境下根据配置默认打开
        if ConfigManage().get_config_gantry_store_init_test():
            self._config_cache.set_gantry_ctrl_mode_enable_config(True)
            self._config_cache.update_gantries_ctrl_enable_config(
                enable_gantries=self._storers_gantry)

    def start_init(self):
        if not (self._force_work or ConfigManage().get_config_gantry_store_start()):
            return
        self._plc_cache = plc_cache
        self._config_cache = ConfigCache()
        self._storers = [GantryStorer(gantry_no, ip, port)
                         for gantry_no, (ip, port) in STORE_GANTRY_IP_MAP.items()]
        if self._force_work or ConfigManage().is_sim_mode():
            ip, port = ConfigManage().get_gantry_control_server_address()
            self._storers = [
                GantryStorer(103, ip, port),  # test 103
                GantryStorer(175, ip, port),  # test 175
                GantryStorer(182, ip, port),  # test 182
                GantryStorer(183, ip, port),  # test 183
                GantryStorer(184, ip, port),  # test 184
            ]
            self._storers_gantry = [103, 175, 182, 183, 184]
        self.init_config_for_test()
        for storer in self._storers:
            self._store_threads.append(PeriodicalTask(
                target=self.store, interval=0.001, master_only=True, storer=storer))
        self._update_store_gantry_no_thread = PeriodicalTask(
            target=self.update_online_store_gantries, interval=2, master_only=True)
        self._auto_delete_deadline_thread = PeriodicalTask(
            target=self.auto_delete_deadline, interval=120, master_only=True)
        self._check_alive_config = ConfigManage().get_config_gantry_store_check_alive()
        if self._check_alive_config.get('enable', False):
            self._check_alive_thread = PeriodicalTask(
                target=self.check_alive_loop, interval=self._check_alive_interval, master_only=True)
        self.update_global_config(init=True)
        self._running = True

    def start(self):
        self.start_init()
        if self._check_alive_thread is not None:
            self._check_alive_thread.start()
        for store_thread in self._store_threads:
            store_thread.start()
        if self._update_store_gantry_no_thread is not None:
            self._update_store_gantry_no_thread.start()
        if self._auto_delete_deadline_thread is not None:
            self._auto_delete_deadline_thread.start()

    def filter_store_info(self, infos):
        # infos = sorted(infos, key=lambda info: info.timestamp_ns, reverse=True)
        ret = None
        for info in infos:
            if info.ctrl_mode == 2:
                ret = info
                break
            elif ret is None or info.ctrl_mode > ret.ctrl_mode:
                ret = info
        return ret

    def store(self, storer):
        time.sleep(storer.interval)
        start = time.time()
        try:
            info = None
            warning_info = None
            if self.is_ctrl_mode_enable():
                infos = self._plc_cache.get_store_gantry_infos(
                    storer.gantry_no())
                info = self.filter_store_info(infos)
                # warning_info = self._plc_cache.get_store_gantry_warning_info(storer.gantry_no())
            if info is None:
                info = make_gantry_store_plc(storer.gantry_no(), storer.msg_no, truck_no = 0, mode = 0)
            warning_info = self._plc_cache.get_store_gantry_proximity_worning_info(storer.gantry_no())
            info.msg_no = (storer.increase_msg_no() % GANTRY_STORE_MAX_MSG_NO)
            info.warning_level = warning_info.warning_level if warning_info else GantryStoreWarningInfo.UNKNOW_WARNING
            if (info.msg_no % int(60/self._interval)) == 1:
                logger.debug(
                    f'[store] gantry_no:{storer.gantry_no()} send store info:{MessageToDict(info, including_default_value_fields=True,preserving_proto_field_name=True)}')
            if (last_warning_info:=self._storers_warning_plc_dict.get(storer.gantry_no())) is None or last_warning_info.warning_level != warning_info.warning_level:
                if last_warning_info:
                    logger.debug(
                        f'[store] gantry_no:{storer.gantry_no()} update store waring info:{MessageToDict(warning_info, including_default_value_fields=True,preserving_proto_field_name=True)}')
                self._storers_warning_plc_dict[storer.gantry_no(
                )] = warning_info
            if (last_info:=self._storers_plc_dict.get(storer.gantry_no())) is not None and (last_info.ctrl_mode != info.ctrl_mode or last_info.warning_level != info.warning_level):
                logger.debug(
                    f'[store] gantry_no:{storer.gantry_no()} update store info:{MessageToDict(info, including_default_value_fields=True,preserving_proto_field_name=True)}')
            ret = storer.store(info)
            if ret:
                self._storers_plc_dict[storer.gantry_no()] = info
            else:
                logger.debug(
                    f'[store] gantry_no:{storer.gantry_no()} fail to store')
        except Exception as e:
            logger.debug(
                f"[store] gantry_no:{storer.gantry_no()},error:{e},trace:{traceback.format_exc()}")
        interval = time.time() - start
        if interval > 0.5:
            logger.warning(
                f"gantry_no:{storer.gantry_no()} store loop over cost execeed round time:{round(interval)}, execeed time:{interval}")
        storer.interval = (
            self._interval - interval) if (self._interval - interval) > 0 else 0

    def update_global_config(self, init=False):
        if (ctrl_mode_enable:=self._config_cache.get_gantry_ctrl_mode_enable_config()) != self._global_config.get('CTRL_MODE_ENABLE', False):
            if not init:
                logger.debug(
                    f"update_global_config:CTRL_MODE_ENABLE {self._global_config.get('CTRL_MODE_ENABLE')} to {ctrl_mode_enable}")
            self._global_config['CTRL_MODE_ENABLE'] = ctrl_mode_enable

    def is_ctrl_mode_enable(self):
        return self._global_config.get('CTRL_MODE_ENABLE', False)

    def update_online_store_gantries(self):
        gantry_nos = []
        start_time = time.time()
        for gantry in self._storers_gantry:
            info = self._storers_plc_dict.get(gantry, None)
            if info is None:
                # logger.debug(f"gantry:{gantry} update online store gantry error due to None")
                continue
            if time_valid(info.timestamp_ns, 20., cur_time=time.time()):
                gantry_nos.append(gantry)
        plc_cache.refresh_online_store_gantries(gantry_nos)
        self.update_global_config()
        end_time = time.time()
        logger.debug(
            f"online_store_gantrys:{len(gantry_nos)}:{sorted(gantry_nos)},{end_time - start_time}")

    def auto_delete_deadline(self):
        start_time = time.time()
        for gantry_no in self._storers_gantry:
            infos = self._plc_cache.get_store_gantry_infos(gantry_no, ex=None)
            infos = [info for info in infos if info is not None and
                     not time_valid(info.timestamp_ns, ex=self._auto_delete_deadline_threshold, cur_time=start_time)]
            for info in infos:
                logger.debug(
                    f"delete gantry_no:{info.gantry_no}:{MessageToString(info, as_one_line=True)}")
                plc_cache.delete_store_gantry(info.gantry_no)
        end_time = time.time()
        logger.debug(
            f"######auto_delete_deadline:time cost:{end_time - start_time}######")

    def check_alive_loop(self):
        start_time = time.time()
        self._check_alive_count = self._check_alive_count + 1
        try:
            check_interface_name = self._check_alive_config.get(
                'check_interface_name', '')
            check_interface_addr = self._check_alive_config.get('check_ip', '')
            keep_alive_time_ms = self._check_alive_config.get(
                'alive_time_ms', 0)
            default_alive = self._check_alive_config.get(
                'default_alive', False)
            check_interface = None
            interfaces = psutil.net_if_addrs()
            if default_alive or (interfaces and (check_interface:=interfaces.get(check_interface_name)) is not None
                                 and len(check_interface) > 0 and check_interface[0].address == check_interface_addr):
                self._plc_cache.refresh_store_gantry_server_alive(
                    keep_alive_time_ms)
                if not self._check_alive_status:
                    if self._check_alive_count > 1:
                        logger.info(f"check gantry store restore alive:check_interface_name:{check_interface_name},"
                                    f"keep_alive_time_ms:{keep_alive_time_ms},check_interface_addr:{check_interface_addr},"
                                    f"check_interface:{check_interface},self._check_alive_interval:{self._check_alive_interval},"
                                    f",default_alive:{default_alive}")
                    self._check_alive_status = True
                    self._check_unalive_count = 0
            else:
                self._check_alive_status = False
                self._check_unalive_count = self._check_unalive_count + 1
                if self._check_unalive_count % (30 // self._check_alive_interval) == 0:
                    logger.warning(f"fail to check gantry store alive:check_interface_name:{check_interface_name},"
                                   f"keep_alive_time_ms:{keep_alive_time_ms},check_interface_addr:{check_interface_addr},"
                                   f"check_interface:{check_interface},check_alive_interval:{self._check_alive_interval},"
                                   f"check_unalive_count:{self._check_unalive_count},default_alive:{default_alive}")
                    logger.debug(
                        f"fail to check gantry store alive:interfaces:{interfaces}")
            if self._check_alive_count % (30 // self._check_alive_interval) == 0:
                logger.debug(f"check alive loop:check_alive_status:{self._check_alive_status},check_interface_name:{check_interface_name},"
                             f"keep_alive_time_ms:{keep_alive_time_ms},check_interface_addr:{check_interface_addr},"
                             f"check_interface:{check_interface},check_alive_interval:{self._check_alive_interval},"
                             f"check_alive_count:{self._check_alive_count},check_unalive_count:{self._check_unalive_count},"
                             f"default_alive:{default_alive}")
        except Exception as e:
            logger.warning(
                f"[store] check_alive_loop:fail to get check alive,error:{e},trace:{traceback.format_exc()}")
        end_time = time.time()
        if (end_time - start_time) > 0.1:
            logger.warning(
                f"check_alive_loop over cost execeed round time:{round(end_time - start_time)}, execeed time:{end_time - start_time}")
