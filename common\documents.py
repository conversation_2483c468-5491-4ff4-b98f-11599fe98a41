from curses.ascii import isdigit
import time
import uuid
import re
import base64
import struct
import traceback
from typing import List

from cache.command_cache import CommandCache
from cache.lock_cache import LockCache
from cache import vehicle_cache
from cache.wi_cache import WiCache
from common import common_util
from common.common_util import get_ms_change_station_point
from common.constant import SUPPORT_TRAILER_CENTER_TERMINAL,SUPPORT_NEW_TRANSPOINT_API
from common.logger import logger
from common.name_converter import NameConverter
from config.config_manage import ConfigManage
from model import system_status
from plc.crane_receiver import CraneInfoContainer
from transfer_point_map.transfer_point import TransferPoint
from proto.antenna_pb2 import RemoteCommand, StopCommand, WaitNewTosCommand, CommandStatus, VesselDirection, ContainerSize, VehicleStatus, DestinationType
from proto.cache_pb2 import CommandWithStatus, MoveCommandWithStatus, PreWiInfo
from tos_db import TosDb


class TosWi(object):
    def __init__(self, tos_wi, db: TosDb, tos_wis: list = None):
        self._id = tos_wi['ID']
        self._truck_no = tos_wi['TRUCK_NO']
        self._command_cache = CommandCache()
        self._wi_status = 'FREE'
        self._commands = list()
        self._is_cancel = False
        self._parse_timestamp_ns = int(time.time() * 1000000000)  # 解析本条TOS指令的时间，各子指令的解析时间戳应保持一致
        self._lock_cache = LockCache()
        self._crane_info = dict()
        self._last_pre_wi_enabled = True
        if db is not None and common_util.is_crane_valid_wi(tos_wi) and (crane_no:=self._parse_crane_no(tos_wi)):
            self._crane_info = db.query_crane_info(crane_no,NameConverter().is_debug_truck_no(self._truck_no))
        self.parse_commands(tos_wi, db, tos_wis)


    def is_crane_for_binded(self, tos_wi):
        if not tos_wi['TO_POS'].startswith('CR') or not tos_wi.get('TWIN_TO_POS','CR').startswith('CR'):
            logger.warning(f"not is_crane_for_binded for not CR:tos_wi:{tos_wi}")
            return False, ''
        #DSCH LOAD才可能会有C_TRUCK_POS
        if tos_wi.get('C_TRUCK_POS') == 'B':
            #只有绑定的逻辑这里
            return True, 'BINDED_TWIN'
        elif tos_wi.get('C_TRUCK_POS') in ['F','M','A','C','L','FA']:
            #其他的,此处不管
            return False, ''
        if tos_wi.get('TWIN_TO_POS') is not None and tos_wi['TO_POS'][:4] != tos_wi.get('TWIN_TO_POS','CR')[:4]:
            logger.warning(f"not is_crane_for_binded for different CR:tos_wi:{tos_wi}")
            return False, ''

        #LOAD UNLOAD双箱指令根据指令来
        if tos_wi.get('WI_TYPE') == 'LOAD' and tos_wi.get('WI_ACT') == 'UNLOAD':
            if tos_wi.get('TWIN_FLAG') == 'Y':
                return False, ''
            else:
                return True, 'BINDED_TWIN'
        #DSCH LOAD根据吊具尺寸,区分箱型
        crane = CraneInfoContainer().get_crane_info(int(tos_wi['TO_POS'][2:4]))
        size = crane.spr_size if crane is not None else 0
        if size == 1:
            return False, '22GP'
        elif size == 2:
            return True, '43G'
        elif size == 3:
            return True, 'L5G'
        elif size == 4:
            return True, 'BINDED_TWIN'
        else:
            logger.warning('Invalid crane spr size: ' + str(size) + 'assuming ' + str(tos_wi['TWIN_FLAG'] == 'T'))
            if tos_wi['TWIN_FLAG'] == 'T':
                return True, 'BINDED_TWIN'
            else:
                return False, ''

    def _parse_crane_no(self,tos_wi):
        destination = str(tos_wi.get('TO_POS','')).split(' ')[0]
        return (destination if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else None)


    def _parse_normal_commands(self, tos_wi, db: TosDb, tos_wis: list = None):
        if tos_wi.get('WI_TYPE') == 'LOAD':
            if tos_wi['WI_ACT'] == 'LOAD':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
            elif tos_wi['WI_ACT'] == 'UNLOAD':
                if tos_wi['TWIN_TYPE'] == 'SECOND':
                    is_one, _ = self.is_crane_for_binded(tos_wi)
                    if not is_one:
                        self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_COME, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_NEW_TOS, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                else:
                    if tos_wi['TWIN_TYPE'] == 'FIRST':
                        is_binded, _ = self.is_crane_for_binded(tos_wi)
                        if is_binded:
                            for wi in tos_wis:
                                wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                                wi['TWIN_TYPE'] = 'ALONE'
                            tos_wi['EQUIT_TYPE'] = 'BINDED_TWIN'
                            tos_wi['TWIN_TYPE'] = 'ALONE'
                    self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_LOCK, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_LOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_CRANE_COME, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                            RemoteCommand.WAIT_NEW_TOS, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        elif tos_wi.get('WI_TYPE') == 'DSCH' and tos_wi.get('CTN_NO') != 'S':
            if tos_wi['WI_ACT'] == 'LOAD':
                if tos_wi['TWIN_TYPE'] == 'SECOND':
                    pass
                else:
                    if tos_wi['LOCK_PAVILION']:
                        self.make_pre_lock_commands(tos_wi)
                    is_one, equit_type = self.is_crane_for_binded(tos_wi)
                    if is_one:
                        for wi in tos_wis:
                            wi['EQUIT_TYPE'] = equit_type
                            wi['TWIN_TYPE'] = 'ALONE'
                        tos_wi['EQUIT_TYPE'] = equit_type
                        tos_wi['TWIN_TYPE'] = 'ALONE'
                        self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                    elif tos_wi.get('C_TRUCK_POS') in ['F','M','A','C','L']:
                        if tos_wi.get('C_TRUCK_POS') in ['F','M','A']:
                            tos_wi['EQUIT_TYPE'] = '22GP'
                        elif tos_wi.get('C_TRUCK_POS') in ['C']:
                            tos_wi['EQUIT_TYPE'] = '43GP'
                        elif tos_wi.get('C_TRUCK_POS') in ['L']:
                            tos_wi['EQUIT_TYPE'] = 'L5G'
                        tos_wi['TWIN_TYPE'] = 'ALONE'
                        self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
                    else:#'FA'和默认走这里
                        tos_wi['EQUIT_TYPE'] = '22GP'
                        tos_wi['TRUCK_POS'] = 'F'
                        tos_wi['TWIN_TYPE'] = 'FIRST'
                        self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_OFF, tos_wi, '', db, tos_wis=tos_wis))
                        tos_wi['TRUCK_POS'] = 'A'
                        tos_wi['TWIN_TYPE'] = 'SECOND'
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_VIA_CRANE_CPS, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.WAIT_CRANE_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                        self._commands.append(self.make_command(
                            RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
            elif tos_wi['WI_ACT'] == 'UNLOAD':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        elif tos_wi.get('WI_TYPE') == 'YARD':
            self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_GANTRY, tos_wi, 'ARRIVE', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.WAIT_GANTRY_COME, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_VIA_GANTRY_CPS, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.WAIT_GANTRY_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        elif tos_wi.get('WI_ACT') == 'LEAVE_SPACE':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_LEAVE_SPACE, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_LEAVE_SPACE, tos_wi, 'FINISH', tos_wis=tos_wis))
        elif tos_wi.get('WI_ACT') == 'STOP_WORK':
            self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_STOP_WORK, tos_wi, 'ARRIVE', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.WAIT_STOP_WORK, tos_wi, 'FINISH', tos_wis=tos_wis))
        elif tos_wi.get('WI_TYPE') == 'GOTO':
            if tos_wi.get('WI_ACT') == 'MOVE':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_POSITION, tos_wi, 'FINISH', tos_wis=tos_wis))
            if tos_wi.get('WI_ACT') == 'CHANGE':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_CHANGE, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_VIA_CHANGE_CPS, tos_wi, 'ARRIVE', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_VIA_CHANGE_SCANS_CPS, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_VIA_CHANGE_SCANS_CPS, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_CHANGE_LOCK, tos_wi, '', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_CHANGE_OFF, tos_wi, 'FINISH', tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        elif tos_wi.get('CTN_NO') == 'S':
            if tos_wi.get('LOCK_PAVILION'):
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
            else:
                is_one, equit_type = self.is_crane_for_binded(tos_wi)
                if is_one:
                    for wi in tos_wis:
                        wi['EQUIT_TYPE'] = equit_type
                        wi['TWIN_TYPE'] = 'ALONE'
                    tos_wi['EQUIT_TYPE'] = equit_type
                    tos_wi['TWIN_TYPE'] = 'ALONE'
                else:  # 'FA'和默认走这里
                    tos_wi['EQUIT_TYPE'] = '22GP'
                    tos_wi['TRUCK_POS'] = 'F'
                    tos_wi['TWIN_TYPE'] = 'FIRST'
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_CRANE, tos_wi, '', db, tos_wis=tos_wis))
        return


    def _parse_stacker_commands(self, tos_wi, db: TosDb, tos_wis: list = None):
        if common_util.is_stacker_horizontal_wi(tos_wi):
            #横向堆高机
            #Arrive过的(排除箱区口指令，做标记),绑定过的(给指令就绑定过的或绑定过后又取消的,做标记),解绑是让出关路,TO_POS变化的清标记
            self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
            if tos_wi['WI_TYPE'] == 'DSCH' and tos_wi['WI_ACT'] == 'UNLOAD':
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_STACKER, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.WAIT_STACKER_BIND, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_VIA_STACKER, tos_wi, '', tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.WAIT_STACKER_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_FORWARD, tos_wi, '', db, tos_wis=tos_wis))
            self._commands.append(self.make_command(
                RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        else:#纵向堆高机
            if tos_wi['WI_ACT'] == 'UNLOAD':
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                if tos_wi['WI_TYPE'] == 'DSCH':
                    self._commands.append(self.make_command(
                        RemoteCommand.MOVE_TO_UNLOCK, tos_wi, '', db, tos_wis=tos_wis))
                    self._commands.append(self.make_command(
                        RemoteCommand.WAIT_UNLOCK_OFF, tos_wi, '', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_STACKER, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_STACKER_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
            else:
                self._commands.extend(self.make_prestep_commands(tos_wi, tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_STACKER, tos_wi, 'ARRIVE', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.WAIT_STACKER_OFF, tos_wi, 'FINISH', db, tos_wis=tos_wis))
                self._commands.append(self.make_command(
                    RemoteCommand.MOVE_TO_REST, tos_wi, '', db, tos_wis=tos_wis))
        return

    def parse_commands(self, tos_wi, db: TosDb, tos_wis: list = None):
        logger.info('parse command:' + str(tos_wi))
        self._commands.clear()
        if tos_wi['WI_STATUS'] == 'DISPATCH' or tos_wi['REMARK2'] == 'DISPATCH':
            if common_util.is_stacker_wi(tos_wi):
                self._parse_stacker_commands(tos_wi=tos_wi, db=db, tos_wis=tos_wis)
            else:
                self._parse_normal_commands(tos_wi=tos_wi, db=db, tos_wis=tos_wis)
        elif tos_wi['WI_STATUS'] == 'CANCEL':
            logger.info("wi status is cancel:" + str(tos_wi))
            self._is_cancel = True
            self._commands.append(self.make_command(
                RemoteCommand.STOP, tos_wi, 'CANCEL_YARD', tos_wis=tos_wis))
        self._commands = list(filter(lambda command: command != None, self._commands))
        if len(self._commands) > 0 and self._commands[-1] is not None:
            self._commands[-1].command.command_context.command_idx_type = RemoteCommand.CommandContext.LAST_IDX
        # 重新解析指令后需要移除自动 reset 指令标记位
        self._command_cache.del_command_reset_mode(tos_wi['TRUCK_NO'])
        logger.info(f'truck_no:{self._truck_no} parse result id {self._id}:{str(self._commands)}')

    def make_prestep_commands(self, tos_wi, tos_wis=[]):
        commands = []
        # 添加停车指令
        commands.append(self.make_command(
            RemoteCommand.STOP, tos_wi, 'START', tos_wis=tos_wis))
        # 添加临时移动指令
        tos_id = tos_wi.get('ID')
        move_command_info: MoveCommandWithStatus = self._command_cache.get_move_command_info(tos_wi['TRUCK_NO'])
        if move_command_info and move_command_info.tos_command_id == tos_id:
            if move_command_info.command_status == MoveCommandWithStatus.CommandStatus.DISPATCH:
                move_command = self.make_command(
                    RemoteCommand.MOVE_TO_POSITION, tos_wi, '', tos_wis=tos_wis)
                move_command.command.move_to_position_command.position.utm_x = move_command_info.command_context.destination_point.utm_x
                move_command.command.move_to_position_command.position.utm_y = move_command_info.command_context.destination_point.utm_y
                move_command.command.move_to_position_command.need_park = move_command_info.command_context.is_auto_park
                move_command.command.command_context.purpose_type = move_command_info.command_context.purpose_type
                commands.append(move_command)
            else:
                logger.info(f'move command status is not dispatch, {tos_id}, {move_command_info.command_status}')
        return commands

    def make_pre_lock_commands(self, tos_wi):
        current_enabled = ConfigManage().get_config_pre_wi_start()
        if current_enabled:
            self._last_pre_wi_enabled = True
            pre_wi_info = PreWiInfo()
            pre_wi_info.truck_no = tos_wi.get('TRUCK_NO')
            pre_wi_info.recv_timestamp = tos_wi.get('INSERT_TIME').strftime('%Y-%m-%d %H:%M:%S')
            pre_wi_info.wi_status = "DISPATCH"
            pre_wi_info.cycle_mode = "S"
            pre_wi_info.lock_pavilion = tos_wi.get('LOCK_PAVILION')
            pre_wi_info.lock_flag = tos_wi.get('LOCK_FLAG')
            WiCache().hset_pre_wi_info(pre_wi_info)
        elif self._last_pre_wi_enabled:
            WiCache().clear_all_pre_wi_info()
            self._last_pre_wi_enabled = False

    def _get_yard_rest_point(self, destination):
        if destination.startswith('XF') or destination.startswith('XG'):
            return (404903.53,3296069.61)
        elif destination.startswith('91') or destination.startswith('J1'):
            # 9yard 横一路,for master_driverless
            return (405011.56, 3295627.25)
        elif destination.startswith('9') or destination.startswith('J'):
            # 8yard 横一路
            return (404879.1, 3295480.7)
        elif destination.startswith('8'):
            return (404879.1, 3295480.7)
        else:
            if destination[:2] <= "66":
                return (404185.09775559895, 3295282.0873800376)
            else:
                return (404364.62252597715, 3295067.3874050993)

    def _get_yard_min_bay(self, destination, tos_wi={}):
        min_bay_dict = {'J1': '20', 'J2': '12'}
        return min_bay_dict.get(str(destination)[:2], '06')

    def _get_stacker_yard_min_bay(self, destination, tos_wi={}):
        min_bay_dict = {'91':'20','J1':'20',
                        '92':'12','J2':'12','Y2':'12',
                        'Y1':'14','X2':'14',
                        'X1':'18','Y7':'14'}
        return min_bay_dict.get(str(destination)[:2],'10')

    def _parse_remark1(self, tos_wi):
        result = {}
        if not NameConverter().is_debug_truck_no(tos_wi.get('TRUCK_NO')):
            return result
        remark1 = tos_wi.get('REMARK1')
        if remark1 is not None:
            match = re.match(r'FB:(\S*)', remark1)
            if match is not None:
                key_list = ['FB_CS','FB_DD']
                for i in range(len(key_list)):
                    result[key_list[i]] = match.group(1)[i:i+1]
        return result

    def _get_yard_vert_road_wait_point(self, destination):
        if len(destination) < 2:
            logger.warning(f'invalid destination:{destination}')
            return None
        point_dict = {'7L':(404171.35,3295285.97),'7M':(404171.35,3295285.97),
                      # '8L':(404427.67,3295534.58),'8M':(404427.67,3295534.58),
                      '9L':(404685.6,3295781.2),'9M':(404685.6,3295781.2)}
        return point_dict.get(destination[:2])

    def _get_yard_vert_road_wait_poi(self, destination):
        if len(destination) < 2:
            logger.warning(f'invalid destination:{destination}')
            return None
        return destination[:2]

    def _is_stacker_yard_wait_tos(self, destination, tos_wi):
        #1.大贝位 2.当前已经是绑定的(直接上去) 3.已绑定过且已达到过(车尾需要让出关路的) 4.同箱区第二个指令
        if (not destination[2:].isdigit() or int(destination[2:]) > int(self._get_stacker_yard_min_bay(destination))) or tos_wi.get('YC_ID') \
            or (tos_wi.get('TRUCK_YC_ID') is not None and tos_wi.get('TRUCK_ARRIVE_TIME') is not None) \
            or (tos_wi.get('TWIN_TYPE') == 'SECOND' and tos_wi.get('TO_POS','')[:2] == tos_wi.get('TWIN_TO_POS','')[:2]):
            return True
        else:
            #小贝位且没有绑定直接去纵路关路上等
            return False

    def _get_stacker_offset(self, tos_wi):
        offset = 0
        transfer_point =tos_wi.get('TO_POS').split(' ')[0]
        twin_transfer_point = tos_wi.get('TWIN_TO_POS','').split(' ')[0]
        if tos_wi.get('YC_ID') is None:#当前不是绑定状态
            if tos_wi.get('TRUCK_YC_ID') is not None and tos_wi.get('TRUCK_ARRIVE_TIME') is not None:
                offset = 0 #车尾关路,已经达到并绑定过,直接去作业点
            elif tos_wi.get('TWIN_TYPE') == 'SECOND' \
                and len(transfer_point) > 2 and len(twin_transfer_point) > 2 \
                and transfer_point[:2] == twin_transfer_point[:2] \
                and (not transfer_point[2:].isdigit() or ((bay_dist:=int(transfer_point[2:])-int(twin_transfer_point[2:])) >= 0 and bay_dist <= 0)):
                offset = 8.5 #车尾关路,直接上去让出关路,离箱子覆盖贝位边缘2m:1.双小箱同箱区同贝位 2.双小箱第二个贝位与第一个贝位差小于12大于0(小于12目前不需要支持,只要双小箱同贝位第二个小箱时才需要让出8.5m关路)
            elif len(transfer_point) > 2 and (not transfer_point[2:].isdigit() or (int(transfer_point[2:]) > int(self._get_stacker_yard_min_bay(transfer_point)))):
                offset = -13.5
            else:
                offset = 0
        else:#当前是绑定状态直接去作业点
            offset = 0
        return offset

    def _parase_work_lane_id(self,vehicle_name,crane_info,tos_wi):
        if (c_manual_lane_no:=str(tos_wi.get('C_MANUAL_LANE_NO',''))) and c_manual_lane_no.isdigit():
            logger.info(f"_parase_work_lane_id:vehicle:{vehicle_name} use C_MANUAL_LANE_NO:{c_manual_lane_no} for id:{tos_wi.get('ID')}")
            return c_manual_lane_no, RemoteCommand.CommandContext.CommandContextSource.MANUAL
        elif (c_lane_no:=str(tos_wi.get('C_LANE_NO',''))) and c_lane_no.isdigit():
            logger.info(f"_parase_work_lane_id:vehicle:{vehicle_name} use C_LANE_NO:{c_lane_no} for id:{tos_wi.get('ID')}")
            return c_lane_no, RemoteCommand.CommandContext.CommandContextSource.ECS
        elif crane_info and str(crane_info.get('LANE_NO','')).isdigit():
            return crane_info.get('LANE_NO'), RemoteCommand.CommandContext.CommandContextSource.TOS
        else:
            return None, None

    def _get_cur_destionation(self, vehicle_name, command_context, tos_wi):
        cur_destination_id, cur_destination_type = None, None
        if command_context.type == RemoteCommand.STOP:
            pass
        elif command_context.type == RemoteCommand.MOVE_TO_POSITION:
            cur_destination_type = RemoteCommand.CommandContext.RANDOM_DEST
        elif command_context.type == RemoteCommand.MOVE_TO_REST:
            cur_destination_type = RemoteCommand.CommandContext.REST_DEST
        elif command_context.type in [RemoteCommand.MOVE_STOP_WORK,RemoteCommand.WAIT_STOP_WORK]:
            cur_destination_id,cur_destination_type = command_context.destination_id, RemoteCommand.CommandContext.STOP_WORK_DEST
        elif command_context.type in [RemoteCommand.MOVE_LEAVE_SPACE,RemoteCommand.WAIT_LEAVE_SPACE]:
            cur_destination_id,cur_destination_type = command_context.destination_id, RemoteCommand.CommandContext.LEAVE_SPACE_DEST
        elif command_context.device_type == RemoteCommand.CommandContext.LOCK_STATION:
            cur_destination_id, cur_destination_type = command_context.pavilion, RemoteCommand.CommandContext.LOCK_DEST
        elif command_context.device_type == RemoteCommand.CommandContext.GANTRY:
            cur_destination_id, cur_destination_type = command_context.destination_id, RemoteCommand.CommandContext.YARD_DEST
        elif command_context.device_type == RemoteCommand.CommandContext.STACKER:
            cur_destination_type = RemoteCommand.CommandContext.YARD_DEST
            if command_context.work_version_tag != RemoteCommand.CommandContext.WORK_VERSION_TAG2:
                cur_destination_id = command_context.destination_id
                if self._is_stacker_yard_wait_tos(cur_destination_id,tos_wi):
                    #箱区内等待点
                    if self._get_stacker_offset(tos_wi) != 0:
                        cur_destination_type = RemoteCommand.CommandContext.YARD_WAIT_DEST
                else:
                    #纵路等待点
                    cur_destination_type = RemoteCommand.CommandContext.YARD_VERT_ROAD_WAIT_DEST
            elif len(command_context.transfer_points) == 2 and len(command_context.transfer_points[0]) > 1:
                #非常规堆高机场地:CY1450.1-->YC+贝位
                cur_destination_id = command_context.transfer_points[0][1] + command_context.transfer_points[0][0] + \
                    (command_context.transfer_points[1] if len(command_context.transfer_points[1]) > 0 else command_context.transfer_points[0][2:])
            else:
                cur_destination_id = command_context.destination_id
                logger.warning(f"vehicle:{vehicle_name} fail to get cur destionation")
        elif command_context.device_type == RemoteCommand.CommandContext.CRANE:
            cur_destination_id  = command_context.destination_id if command_context.priority != RemoteCommand.CommandContext.LOW else None
            cur_destination_type = RemoteCommand.CommandContext.CRANE_DEST if command_context.priority != RemoteCommand.CommandContext.LOW else RemoteCommand.CommandContext.HANGING_DEST
        else:
            logger.warning(f"vehicle:{vehicle_name} fail to get cur destionation")
        return cur_destination_id, cur_destination_type


    def make_base_command_context(self, command_type, tos_wi, db: TosDb = None) -> RemoteCommand.CommandContext:
        command_context = RemoteCommand.CommandContext()
        command_context.parse_timestamp_ns = self._parse_timestamp_ns
        command_context.truck_no = self._truck_no
        if tos_wi.get('DISPATCH_TIME') is not None:
            command_context.dispatch_timestamp = int(time.mktime(tos_wi.get('DISPATCH_TIME').now().timetuple()))
        command_context.action_type = RemoteCommand.CommandContext.ActionType.Value(tos_wi.get('WI_ACT'))
        wi_type = 'WI_' + str(tos_wi.get('WI_TYPE')) if str(tos_wi.get('WI_TYPE')) in ['LOAD','DSCH','YARD','GOTO'] else 'WI_UNKNOWN'
        command_context.wi_type = RemoteCommand.CommandContext.WiType.Value(wi_type)
        command_context.type = command_type
        command_context.vms_type = RemoteCommand.CommandContext.NB_RAW_TOS if not common_util.is_fabu_wi(
            tos_wi) else RemoteCommand.CommandContext.FABU
        command_context.twin_type = RemoteCommand.CommandContext.TwinType.Value(tos_wi.get('TWIN_TYPE'))
        command_context.tos_command_id = tos_wi.get('ID')
        command_context.wi_id = str(tos_wi.get('WI_ID','0'))
        command_context.command_idx_type = RemoteCommand.CommandContext.UNKNOWN_IDX
        command_context.server_info.node_name = str(tos_wi.get('SERVER_NODE_NAME'))
        command_context.context_source.wi_status_source = RemoteCommand.CommandContext.CommandContextSource.ECS \
          if tos_wi.get('WI_STATUS') == 'FINISH' and tos_wi.get('REMARK2') == 'DISPATCH' else RemoteCommand.CommandContext.CommandContextSource.TOS
        command_context.context_source.destination_source = RemoteCommand.CommandContext.CommandContextSource.ECS \
          if common_util.is_yard_bay_to_enter(tos_wi) else RemoteCommand.CommandContext.CommandContextSource.TOS
        if (bridge_up:=tos_wi.get('BRIDGE_UP')):
            command_context.bridge_up = common_util.parse_map_ship_bridge_index(bridge_up)
        if (bridge_down:=tos_wi.get('BRIDGE_DOWN')):
            command_context.bridge_down = common_util.parse_map_ship_bridge_index(bridge_down)
        if (ship_uuid:=tos_wi.get('SHIP_UUID')):
            command_context.ship_uuid = ship_uuid
        to_pos = tos_wi.get('TO_POS')
        command_context.is_twin_same_box = True if (to_pos == tos_wi.get('TWIN_TO_POS')) else False
        destination = to_pos.split(' ')[0]
        #TO_POS 被改后,对这里是有影响的
        #TODO 这里还需要修改解析destination
        command_context.transfer_point = destination
        command_context.transfer_points.append(destination)
        if tos_wi.get('POS_TYPE') == 'BAY_TYPE' and (bay_no:=common_util.parse_cr_bay_no(to_pos)) != None and bay_no.isdigit():
            command_context.bay_no = "%02d" % (int(bay_no))
        if common_util.is_stacker_vertical_wi(tos_wi):
            if (box_point:=TransferPoint().get_stacker_vertical_box_loc(destination)):
                command_context.transfer_points.append("%02d" % (box_point[2]))
            else:
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} fail to get box_point for {destination}")
                command_context.transfer_points.append('')
        if common_util.is_container_wi(tos_wi):
            command_context.wi_no = str(tos_wi['WI_NO'])
            command_context.twin_wi_no = str(tos_wi['TWIN_WI_NO'])
            if destination is not None and common_util.is_yard_wi(tos_wi) and len(destination) == 2:
                destination = destination + self._get_yard_min_bay(destination)
                logger.info(f"destination has no position, add position default:{destination}")
            if destination is not None and destination.startswith('CR'):
                crane_info = self._crane_info
                if crane_info and str(crane_info.get('LANE_NO','')).isdigit():
                    lane_id, lane_id_source = self._parase_work_lane_id(tos_wi.get('TRUCK_NO'),crane_info,tos_wi)
                    if lane_id is not None:
                        command_context.lane_id = int(lane_id)
                        command_context.context_source.lane_id_source = lane_id_source
                        if command_context.context_source.lane_id_source in (
                            RemoteCommand.CommandContext.CommandContextSource.ECS,
                            RemoteCommand.CommandContext.CommandContextSource.MANUAL):
                            if str(crane_info.get('LANE_NO','')).isdigit():
                                command_context.crane_lane_id = int(crane_info.get('LANE_NO')) #真实的桥吊作业车道
                    else:
                        command_context.lane_id = 0
                else:
                    command_context.lane_id = 0
                if command_context.lane_id == 0:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} {to_pos} has no lane id,use default 0")

                # add vessel direction
                if crane_info and crane_info.get('VESSEL_DIRECTION') == 'L':
                    command_context.vessel_direction = VesselDirection.LEFT
                else:
                    command_context.vessel_direction = VesselDirection.RIGHT

            command_context.container_id = str(tos_wi.get('CTN_NO'))
            if (twin_container_id:=tos_wi.get('TWIN_CTN_NO')):
                command_context.twin_container_id = twin_container_id

            if tos_wi.get('CTN_WEIGHT') is not None:
                command_context.container_weight = tos_wi.get('CTN_WEIGHT')

            if len(to_pos.split(' ')) > 1:
                command_context.warehouse = to_pos.split(' ')[1]
            if len(tos_wi.get('C_TRUCK_POS','')) > 0:
                truck_pos =  tos_wi.get('C_TRUCK_POS')
                command_context.context_source.container_position_source = RemoteCommand.CommandContext.CommandContextSource.MANUAL
            else:
                truck_pos = tos_wi.get('TRUCK_POS')
                command_context.context_source.container_position_source = RemoteCommand.CommandContext.CommandContextSource.TOS
            if tos_wi.get('EQUIT_TYPE') is not None:
                if tos_wi.get('EQUIT_TYPE') == 'BINDED_TWIN':
                    command_context.container_size = ContainerSize.SIZE_BINDED_TWIN
                elif tos_wi.get('EQUIT_TYPE').startswith('2'): #22UT、25UT、22GP等都是20
                    command_context.container_size = ContainerSize.SIZE_20_FEET
                    db_dict = {
                        'F': RemoteCommand.CommandContext.FRONT,
                        'M': RemoteCommand.CommandContext.MIDDLE,
                        'A': RemoteCommand.CommandContext.BEHIND,
                        '0': RemoteCommand.CommandContext.UNKNOWN_POSITION
                    }
                    command_context.container_position = db_dict[truck_pos] if truck_pos in db_dict else RemoteCommand.CommandContext.UNKNOWN_POSITION
                elif tos_wi.get('EQUIT_TYPE').startswith('L'):
                    command_context.container_size = ContainerSize.SIZE_45_FEET
                else:
                    command_context.container_size = ContainerSize.SIZE_40_FEET
            else:
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')},wi id:{tos_wi['ID']} get no EQUIT_TYPE,may error.use default 40 feet")
                command_context.container_size = ContainerSize.SIZE_40_FEET


        command_context.destination_id = destination

        if common_util.is_set_rest_point_wi(tos_wi):
            command_context.reset_point.utm_x, command_context.reset_point.utm_y = self._get_yard_rest_point(destination)

        #龙门吊贝位作业点
        if common_util.is_gantry_yard_wi(tos_wi):
            work_point = TransferPoint().get_transfer_point_loc_v2(destination,self._truck_no, \
                          ContainerSize.Name(command_context.container_size), \
                          RemoteCommand.CommandContext.ContainerPosition.Name(command_context.container_position), \
                          'center')
            if work_point != (None,None):
                command_context.work_point.utm_x, command_context.work_point.utm_y = work_point[0], work_point[1]
            else:
                logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")

        # 悬空逻辑，仅装船卸箱工况
        command_context.priority = RemoteCommand.CommandContext.NORMAL
        if tos_wi['WI_TYPE'] == 'LOAD' and tos_wi['WI_ACT'] == 'UNLOAD':
            seq, source = self.get_truck_seq(tos_wi)
            command_context.priority = self.get_priority_by_seq(seq)
            command_context.context_source.priority_source = source
        # 调度缓冲,上泊位做船指令都支持
        if (tos_wi['WI_TYPE'] == 'LOAD' and tos_wi['WI_ACT'] == 'UNLOAD') or (tos_wi['WI_TYPE'] == 'DSCH' and tos_wi['WI_ACT'] == 'LOAD'):
            seq, source, buffer_type = self.get_truck_buffer(tos_wi)
            if buffer_type is not None and buffer_type != RemoteCommand.CommandContext.BufferType.NO_BUFFER:
                command_context.command_buffer = buffer_type
                command_context.priority = self.get_priority_by_seq(seq)
                command_context.context_source.priority_source = source
        # 关键箱：记录原始优先级, 装船才有关键箱逻辑
        if tos_wi['WI_TYPE'] == 'LOAD':
            seq = tos_wi.get('TRUCK_SEQ')
            command_context.original_priority = seq
            if self.is_auto_reset(seq, tos_wi, command_context):
                command_context.priority = self.get_priority_by_seq(seq)
                command_context.context_source.priority_source = RemoteCommand.CommandContext.CommandContextSource.AUTO_RESET

        if common_util.is_may_lock_wi(tos_wi):
            # 装卸锁逻辑
            lock_flag, lock_pavilion, source = self.get_lock_config(tos_wi)
            if lock_flag:
                command_context.lock_flag = lock_flag
                command_context.context_source.lock_source = source
            if lock_flag and lock_flag != 'N' and lock_pavilion:
                # 证明此时车辆需要去装卸锁
                pavilion_list = common_util.parse_lock_pavilion(lock_pavilion)
                command_context.pavilion = self.get_default_lock_station(pavilion_list)
                command_context.original_pavilion = lock_pavilion
                if tos_wi.get('S_LOCK_PAVILION'):
                    # 无论lock_pavilion是人为设置或指令设置，双锁亭都要经过调度，所以锁亭有调度结果就默认采用调度结果
                    command_context.pavilion = tos_wi.get('S_LOCK_PAVILION')
                    command_context.scheduling_pavilion = tos_wi.get('S_LOCK_PAVILION')
                    command_context.context_source.lock_source = RemoteCommand.CommandContext.CommandContextSource.SCHEDULE
            command_context.lock_container_id.append(str(tos_wi.get('WI_NO')))
            if tos_wi.get('TWIN_WI_NO') is not None and str(tos_wi.get('TWIN_WI_NO')) != "0":
                command_context.lock_container_id.append(str(tos_wi['TWIN_WI_NO']))
        # 配合作业设备类型
        if common_util.is_container_wi(tos_wi):
            if tos_wi.get('VESSEL_POS') and tos_wi.get('VESSEL_POS') != "None":
                command_context.vessel_pos = tos_wi.get('VESSEL_POS')
            if tos_wi.get('VESSEL_REF') and tos_wi.get('VESSEL_REF') != "None":
                command_context.vessel_ref = tos_wi.get('VESSEL_REF')
            if tos_wi.get('VESSEL_CLASS') and tos_wi.get('VESSEL_CLASS') != "None":
                command_context.vessel_class = tos_wi.get('VESSEL_CLASS')
            if command_type in [RemoteCommand.MOVE_TO_LOCK,RemoteCommand.WAIT_LOCK_OFF,\
                                RemoteCommand.MOVE_TO_UNLOCK,RemoteCommand.WAIT_UNLOCK_OFF]:
                command_context.device_type = RemoteCommand.CommandContext.LOCK_STATION
                command_context.device_id = command_context.pavilion
            elif common_util.is_stacker_wi(tos_wi):
                command_context.device_type = RemoteCommand.CommandContext.STACKER
                if ((yc_id:=str(tos_wi.get('YC_ID',''))).startswith('P') and len(yc_id) > 1 and yc_id[1:].isdigit()) or yc_id.isdigit():
                    command_context.device_id = yc_id[1:] if yc_id.startswith('P') else yc_id
            elif destination is not None and common_util.is_yard_wi(tos_wi):
                command_context.device_type = RemoteCommand.CommandContext.GANTRY
            elif destination is not None and common_util.is_crane_wi(tos_wi):
                command_context.device_type = RemoteCommand.CommandContext.CRANE
                if destination.startswith('CR') and len(destination) > 2 and destination[2:4].isdigit():
                    command_context.device_id = destination[2:4]
                if common_util.is_cycle_mode_wi(tos_wi):
                    command_context.cycle_mode = RemoteCommand.CommandContext.DOUBLE_CYCLE_MODE \
                        if self._crane_info.get('CYCLE_MODE') == 'D' else RemoteCommand.CommandContext.SINGLE_CYCLE_MODE
            else:
                pass

        #功能版本模式
        if common_util.is_stacker_wi(tos_wi):
            command_context.work_version_tag = \
              RemoteCommand.CommandContext.WORK_VERSION_TAG1 \
              if common_util.is_stacker_horizontal_wi(tos_wi) else RemoteCommand.CommandContext.WORK_VERSION_TAG2
        # 指令来源
        command_source =  str(self._parse_remark1(tos_wi).get('FB_CS'))
        # 预指令 模拟指令时循环类型占用箱号字段
        cycle_mode = tos_wi.get('CTN_NO')
        if command_source == 'None':
            command_context.remote_command_source = RemoteCommand.CommandContext.TOS
        elif command_source == 'T':#'TEST'
            command_context.remote_command_source = RemoteCommand.CommandContext.TEST
        elif command_source == 'S':#'SIMULATION':
            command_context.remote_command_source = RemoteCommand.CommandContext.SIMULATION
        else:
            command_context.remote_command_source = RemoteCommand.CommandContext.UNKNOWN
            logger.warning(f"unconfirmed RemoteCommandSource, REMARK1:{tos_wi.get('REMARK1')}, wi id:{tos_wi['ID']}")

        if cycle_mode == 'S':  # 'PRE_TOS'
            command_context.remote_command_source = RemoteCommand.CommandContext.PRE_TOS

        if common_util.is_off_work_wi(tos_wi):
            purpos_dict = {
                          'RECH': RemoteCommand.CommandContext.CHARGE_BATTERY,
                          'EXCH': RemoteCommand.CommandContext.CHANGE_BATTERY
                        }
            command_context.purpose_type = purpos_dict.get(tos_wi.get('WI_TYPE'), RemoteCommand.CommandContext.UNKNOWN_PURPOSE)

        cur_destination_id,cur_destination_type = self._get_cur_destionation(tos_wi.get('TRUCK_NO'),command_context,tos_wi)
        if cur_destination_id is not None:
            command_context.cur_destination_id = cur_destination_id
        if cur_destination_type is not None:
            command_context.cur_destination_type = cur_destination_type
        return command_context

    def is_need_make_command(self, command_type, tos_wi, db: TosDb = None):
        ret = False
        if command_type == RemoteCommand.MOVE_TO_REST:
            if str(tos_wi.get('TWIN_TYPE')) != "FIRST" and (self._crane_info.get('CYCLE_MODE') != 'D' or not common_util.is_cycle_mode_wi(tos_wi)):
                ret = True
        elif command_type == RemoteCommand.MOVE_TO_LOCK or command_type == RemoteCommand.WAIT_LOCK_OFF:
            lock_flag, lock_pavilion, source = self.get_lock_config(tos_wi)
            if self._lock_cache.judge_lock_business_scene(tos_wi.get('BUSINESS_SCENE')) \
                    and self._lock_cache.need_lock(lock_flag) \
                    and self._lock_cache.get_container_lock_is_exist(tos_wi['WI_NO']) != 'True':
                # 仅针对 指定场景内车辆 && 指令装锁标志正确 && 当前箱子未装锁 拆解出装锁指令
                # 询不到箱子装卸锁状态时默认需要装锁
                # 车端执行完成后rpc反馈更新, 避免reset指令后重复扭锁
                ret = True
        elif command_type == RemoteCommand.MOVE_TO_UNLOCK or command_type == RemoteCommand.WAIT_UNLOCK_OFF:
            lock_flag, lock_pavilion, source = self.get_lock_config(tos_wi)
            if self._lock_cache.judge_lock_business_scene(tos_wi.get('BUSINESS_SCENE')) \
                    and self._lock_cache.need_unlock(lock_flag) \
                    and (self._lock_cache.get_container_lock_is_exist(tos_wi['WI_NO']) != 'False' or (
                    str(tos_wi['TWIN_WI_NO']) != '0'
                    and self._lock_cache.get_container_lock_is_exist(tos_wi['TWIN_WI_NO']) != 'False') or (
                    str(tos_wi['WI_NO']) == '-3' and tos_wi['CTN_NO'] == 'S'
            )):
                # 仅针对 指定场景内车辆 && 指令卸锁标志正确 && 当前箱子或双箱作业的另一个箱子未卸锁 拆解出卸锁指令
                # 询不到箱子装卸锁状态时默认需要卸锁
                # 车端执行完成后rpc反馈更新, 避免reset指令后重复扭锁
                # 重车去锁站逻辑 模拟指令
                ret = True
        elif command_type in [RemoteCommand.MOVE_VIA_CRANE_CPS,RemoteCommand.WAIT_GANTRY_COME,RemoteCommand.MOVE_VIA_GANTRY_CPS,\
              RemoteCommand.WAIT_GANTRY_OFF,RemoteCommand.WAIT_CRANE_OFF,RemoteCommand.STOP_CRANE_DETECTION]:
              ret = (self._parse_remark1(tos_wi).get('FB_DD', '0') == '0')
        elif command_type == RemoteCommand.MOVE_TO_FORWARD:
            if tos_wi.get('YC_KIND') == 1 and tos_wi.get('TWIN_TYPE') == "FIRST" and \
                (to_pos:=tos_wi.get('TO_POS','')) and (twin_to_pos:=tos_wi.get('TWIN_TO_POS','')):
                #第一条双箱指令做完需要让出关路:1.双小箱同箱同贝位 2.同箱不同贝,但两贝之差小于等于10贝
                if (to_pos == twin_to_pos) or \
                  ((to_pos[:2] == twin_to_pos[:2]) and to_pos[2:].isdigit() and twin_to_pos[2:].isdigit() and \
                    (bay_gap:=(int(twin_to_pos[2:])-int(to_pos[2:]))) <= 10 and 0 <= bay_gap):
                    ret = True
        elif command_type == RemoteCommand.MOVE_TO_STACKER:#MoveToStacker 解绑状态或不是Arrive状态
            if (common_util.is_stacker_horizontal_wi(tos_wi) and (tos_wi.get('YC_ID') is None or tos_wi.get('TRUCK_ARRIVE_TIME') is None)) \
              or common_util.is_stacker_vertical_wi(tos_wi):
                ret = True
        elif command_type == RemoteCommand.WAIT_STACKER_BIND:
            if tos_wi.get('YC_ID') is None:
                ret = True
        elif command_type == RemoteCommand.WAIT_CRANE_COME:
            if tos_wi.get('POS_TYPE') == 'BAY_TYPE':
                ret = True
        elif command_type == RemoteCommand.WAIT_NEW_TOS:
            if common_util.is_cycle_mode_wi(tos_wi) and self._crane_info.get('CYCLE_MODE') == 'D' and str(tos_wi.get('TWIN_TYPE')) != "FIRST":
                ret = True
        else:
            ret = True

        #箱位反了，先把后箱指令finish掉，在做前箱指令的时候放的是后箱，所以上传的状态不是finish,前箱指令还是会重新被执行一次，人工再把后箱做了
        #然后前小箱指令确认掉，但后箱指令REMARK2被改成了DISPATCH，后箱指令接着取出执行其实箱子已经装掉了，人工远控驾驶状态下后箱指令会一直卡着
        if ret and tos_wi.get('WI_STATUS') == 'FINISH' and tos_wi.get('REMARK2') == 'DISPATCH' and common_util.is_gantry_yard_wi(tos_wi) \
           and not tos_wi.get('SAFE_MODE') and not tos_wi.get('AUTO_DRIVE') and tos_wi.get('TWIN_TYPE') == "SECOND":
              if (tos_wi.get('WI_ACT') == 'LOAD' and tos_wi.get('CONTAINER_FRONT') and tos_wi.get('CONTAINER_REAR')) or \
                  (tos_wi.get('WI_ACT') == 'UNLOAD' and not tos_wi.get('CONTAINER_FRONT') and not tos_wi.get('CONTAINER_REAR')):
                    logger.warning(f"vehicle_name:{self._truck_no},tos_wi id:{self._id} force no need make command")
                    ret = False
        if ret:
            logger.info(
                f'vehicle_name:{self._truck_no},command_type:{RemoteCommand.CommandType.Name(command_type)} need make command,tos_wi id:{self._id}')
        else:
            logger.info(
                f'vehicle_name:{self._truck_no},command_type:{RemoteCommand.CommandType.Name(command_type)} no need make command,tos_wi id:{self._id}')
        return ret

    def _make_command_error_info(self, command, tos_wi, tos_wis):
        if command.type == RemoteCommand.STOP:
            return
        command_type = command.type
        command_context = command.command_context
        if (error_key:=tos_wi.get('ERROR_KEY')):
            if error_key == 'TRUCK_POS':
                error_info = command_context.error_info.add()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.TRUCK_POS_ERROR
                error_info.contexts = 'INVALID TOS TRUCK_POS[' + str(tos_wi.get('ERROR_INFO')) + ']'
        if not common_util.is_support_tos_wi(tos_wi):
            error_info = command_context.error_info.add()
            error_info.code = RemoteCommand.CommandContext.ErrorInfo.TOS_NOT_SUPPORT_ERROR
            error_info.contexts = 'TOS NOT SUPPORT ERROR'
        if command_context.HasField('lane_id') and command_context.lane_id <= 0:
            error_info = command_context.error_info.add()
            error_info.code = RemoteCommand.CommandContext.ErrorInfo.LANE_NO_ERROR
            error_info.contexts = 'INVALID TOS LANE_NO[' + str(command_context.lane_id) + ']'
        if (command_type == RemoteCommand.MOVE_TO_GANTRY and not command.move_to_gantry_command.HasField('terminal_point')) \
            or (command_type == RemoteCommand.MOVE_TO_CRANE and not command.move_to_crane_command.HasField('approximate_point')):
            error_info = command_context.error_info.add()
            error_info.code = RemoteCommand.CommandContext.ErrorInfo.TERMINAL_POINT_ERROR
            error_info.contexts = 'INVALID TERMINAL_POINT[' + str(command_context.destination_id) + ']'
        elif (command_type == RemoteCommand.MOVE_TO_UNLOCK and command.move_to_unlock_command.poi_id == '') \
            or (command_type == RemoteCommand.MOVE_TO_LOCK and command.move_to_lock_command.poi_id == ''):
            error_info = command_context.error_info.add()
            error_info.code = RemoteCommand.CommandContext.ErrorInfo.LOCK_PAVILION_ERROR
            error_info.contexts = 'INVALID LOCK_PAVILION[' + str(tos_wi.get('LOCK_PAVILION')) + ']'

        elif command_type == RemoteCommand.MOVE_TO_UNLOCK:
            if command.move_to_unlock_command.poi_id == '':
                error_info = command_context.error_info.add()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.LOCK_PAVILION_ERROR
                error_info.contexts = 'INVALID LOCK_PAVILION[' + str(tos_wi.get('LOCK_PAVILION')) + ']'
            if command_context.HasField('bridge_down') and \
                common_util.parse_lock_bridge_index(command.move_to_unlock_command.poi_id) != command_context.bridge_down:
                error_info = command_context.error_info.add()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.PAVILION_BRIDGE_ERROR
                error_info.contexts = 'CONFLICT LOCK_PAVILION WITH BRIDGE[' + command.move_to_unlock_command.poi_id + '!=' + command_context.bridge_down + ']'
        elif command_type == RemoteCommand.MOVE_TO_LOCK:
            if command.move_to_lock_command.poi_id == '':
                error_info = command_context.error_info.add()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.LOCK_PAVILION_ERROR
                error_info.contexts = 'INVALID LOCK_PAVILION[' + str(tos_wi.get('LOCK_PAVILION')) + ']'
            if command_context.HasField('bridge_up') and \
                common_util.parse_lock_bridge_index(command.move_to_lock_command.poi_id) != command_context.bridge_up:
                error_info = command_context.error_info.add()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.PAVILION_BRIDGE_ERROR
                error_info.contexts = 'CONFLICT LOCK_PAVILION WITH BRIDGE[' + command.move_to_lock_command.poi_id + '!=' + command_context.bridge_up + ']'
        else:
            pass
        if len(command_context.error_info) > 0:
            for cmd_ctx in command.mul_command_context:
                cmd_ctx.error_info.extend(command_context.error_info)
        tos_wis_dict={wi.get('ID'):wi for wi in tos_wis}
        for cmd_ctx in command.mul_command_context:
            wi = tos_wis_dict.get(cmd_ctx.tos_command_id)
            if common_util.is_yard_bay_to_enter(wi):
                error_info = RemoteCommand.CommandContext.ErrorInfo()
                error_info.code = RemoteCommand.CommandContext.ErrorInfo.DEST_CYCLE_ERROR
                error_info.contexts = 'YARD BAY TO ENTER'
                cmd_ctx.error_info.append(error_info)
        return

    def make_command(self, command_type, tos_wi, finish_status, db: TosDb = None,
                     tos_wis: list = None) -> CommandWithStatus:
        if not self.is_need_make_command(command_type, tos_wi, db):
            return None
        command = RemoteCommand()
        command.timestamp_ms = int(time.time() * 1000)
        command.uuid = str(uuid.uuid1())
        command.type = command_type
        command_context = self.make_base_command_context(command_type, tos_wi, db)
        command.command_context.CopyFrom(command_context)

        if tos_wis is not None:
            for wi in tos_wis:
                command_context_info = self.make_base_command_context(command_type, wi, db)
                if command_context_info is not None:
                    command.mul_command_context.append(command_context_info)

        destination = command_context.destination_id
        if destination is not None and command_type == RemoteCommand.MOVE_TO_GANTRY:
            terminal_point = command.move_to_gantry_command.terminal_point
            if SUPPORT_NEW_TRANSPOINT_API:
                transfer_point = TransferPoint.round_down_even_bay(destination)
                work_point = TransferPoint().get_transfer_point_loc_v2(transfer_point, self._truck_no, \
                          ContainerSize.Name(ContainerSize.SIZE_40_FEET),
                          RemoteCommand.CommandContext.ContainerPosition.Name(RemoteCommand.CommandContext.UNKNOWN_POSITION), \
                          'howo')
            else:
                work_point = TransferPoint().get_transfer_point_loc(destination)
            if work_point is not None and work_point[0] is not None and work_point[1] is not None:
                terminal_point.utm_x, terminal_point.utm_y = work_point[0], work_point[1]
            else:
                logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
            if SUPPORT_TRAILER_CENTER_TERMINAL:
                command.move_to_gantry_command.is_trailer_center_point = True
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_CRANE:
            crane_id = int(destination[2:]) if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else 0
            if crane_id > 0:
                command.move_to_crane_command.crane_id = crane_id
                crane_info = self._crane_info
                if crane_info:
                    for i in range(1, 4):
                        bay = 'BAY%d' % i
                        if bay in crane_info and not crane_info.get(bay) is None:
                            command.move_to_crane_command.crane_bay.append(crane_info.get(bay))
                else:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')},crane_id:{crane_id} fail to get crane info")
                if tos_wi.get('POS_TYPE') == 'BAY_TYPE' and tos_wi.get('BAY_POS_X') and tos_wi.get('BAY_POS_Y'):
                    position = (tos_wi.get('BAY_POS_X'),tos_wi.get('BAY_POS_Y'))
                    command.move_to_crane_command.terminal_type = DestinationType.DEST_CRANE_BAY_POINT
                else:
                    position = CraneInfoContainer().get_position_by_crane_no(crane_id)
                if position is not None:
                    approximate_point = command.move_to_crane_command.approximate_point
                    approximate_point.utm_x, approximate_point.utm_y = position[0], position[1]
                else:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')},destination:{destination} fail to get approximate point")
            else:
                crane_info = dict()
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} invalid destination:{destination}")

            command.move_to_crane_command.lane_id = command_context.lane_id
            self._command_cache.set_working_lane_in_berth(tos_wi.get('TRUCK_NO'), command.move_to_crane_command.lane_id)
            if crane_info and crane_info.get('VESSEL_DIRECTION') == 'L':
                command.move_to_crane_command.vessel_direction = VesselDirection.LEFT
            else:
                command.move_to_crane_command.vessel_direction = VesselDirection.RIGHT
            # command.command_context.dest_type = RemoteCommand.CommandContext.DEST_DOCK
        elif destination is not None and command_type == RemoteCommand.MOVE_VIA_CRANE_CPS:
            crane_id = int(destination[2:]) if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else 0
            if crane_id > 0:
                command.move_via_crane_cps_command.crane_id = crane_id
                crane_info = self._crane_info
                if crane_info and crane_info.get('VESSEL_DIRECTION') == 'L':
                    command.move_via_crane_cps_command.vessel_direction = VesselDirection.LEFT
                else:
                    command.move_via_crane_cps_command.vessel_direction = VesselDirection.RIGHT
            else:
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} invalid destination:{destination}")
            if tos_wi.get('POS_TYPE') == 'BAY_TYPE' and tos_wi.get('BAY_POS_X') and tos_wi.get('BAY_POS_Y'):
                approximate_point = command.move_via_crane_cps_command.approximate_point
                approximate_point.utm_x, approximate_point.utm_y = tos_wi.get('BAY_POS_X'), tos_wi.get('BAY_POS_Y')
                command.move_via_crane_cps_command.terminal_type = DestinationType.DEST_CRANE_BAY_POINT
        elif destination is not None and command_type == RemoteCommand.WAIT_CRANE_COME:
            crane_id = int(destination[2:]) if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else 0
            if crane_id > 0:
                command.wait_crane_come_command.crane_id = crane_id
            else:
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} invalid destination:{destination}")
        elif destination is not None and command_type == RemoteCommand.WAIT_CRANE_OFF:
            crane_id = int(destination[2:]) if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else 0
            if crane_id > 0:
                command.wait_crane_off_command.crane_id = crane_id
                crane_info = self._crane_info
                command.wait_crane_off_command.lane_id = command_context.lane_id
                if crane_info and crane_info.get('VESSEL_DIRECTION') == 'L':
                    command.wait_crane_off_command.vessel_direction = VesselDirection.LEFT
                else:
                    command.wait_crane_off_command.vessel_direction = VesselDirection.RIGHT
            else:
                logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} invalid destination:{destination}")
        elif destination is not None and command_type == RemoteCommand.WAIT_NEW_TOS:
            if common_util.is_cycle_mode_wi(tos_wi) and self._crane_info and self._crane_info.get('CYCLE_MODE') == 'D':
                crane_id = int(destination[2:]) if destination.startswith('CR') and len(destination) > 2 and destination[2:].isdigit() else 0
                if crane_id > 0:
                    command.wait_new_tos_command.wait_reason = WaitNewTosCommand.CRANE_DOUBLE_CYCLE
                    command.wait_new_tos_command.crane_double_cycle.crane_id = crane_id
                else:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} invalid destination:{destination}")
        elif destination is not None and command_type == RemoteCommand.WAIT_GANTRY_OFF:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_STACKER:
            if SUPPORT_TRAILER_CENTER_TERMINAL:
                command.move_to_stacker_command.is_trailer_center_point = True
            terminal_point = command.move_to_stacker_command.terminal_point
            #横向堆高机
            if command_context.work_version_tag == RemoteCommand.CommandContext.WORK_VERSION_TAG1:
                #等待点前3m上报到达
                command.move_to_stacker_command.nb_raw.pre_arrive_yard_bay_range = 3
                #1.大贝位 2.当前已经是绑定的(直接上去) 3.已绑定过且已达到过(车尾需要让出关路的) 4.同箱区第二个指令
                if self._is_stacker_yard_wait_tos(destination,tos_wi):
                    #绑定过再解绑再重新解析指令,是要回到贝位前还是让出关路的位置
                    command.move_to_stacker_command.offset = self._get_stacker_offset(tos_wi)
                    work_point = (None,None)
                    if SUPPORT_NEW_TRANSPOINT_API:
                        #如果是要让出关路的,为兼容车端,以40尺大箱的位置作业贝基准来作偏移;如果是直接上去作业的,则用贝位标定的新接口通过传入箱位和箱型来定
                        if command.move_to_stacker_command.offset != 0:
                            transfer_point = TransferPoint.round_down_even_bay(destination)
                            container_size = ContainerSize.SIZE_40_FEET
                            container_position = RemoteCommand.CommandContext.UNKNOWN_POSITION
                        else:
                            transfer_point = destination
                            container_size = command_context.container_size
                            container_position = command_context.container_position
                        work_point =TransferPoint().get_transfer_point_loc_v2(transfer_point,self._truck_no, \
                                ContainerSize.Name(container_size),RemoteCommand.CommandContext.ContainerPosition.Name(container_position))
                        command.command_context.container_offset_type = RemoteCommand.CommandContext.ONLY_PERCEPTION_OFFSET_TYPE
                    else:
                        work_point =TransferPoint().get_transfer_point_loc(destination)
                    if (work_point != (None,None)):
                        terminal_point.utm_x, terminal_point.utm_y = work_point[0], work_point[1]
                    else:
                        logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
                else:#小贝位且没有绑定直接去纵路关路上等
                    command.move_to_stacker_command.terminal_type = DestinationType.DEST_PRE_WAIT_VERT_ROAD_POINT
                    if (wait_poi:=self._get_yard_vert_road_wait_poi(destination)) is not None:
                        command.move_to_stacker_command.poi_id = wait_poi
                    else:
                        logger.warning(f"fail to get vert road wait poi for destination:{destination}")
                    if (wait_point:=self._get_yard_vert_road_wait_point(destination)) is not None:
                        terminal_point.utm_x, terminal_point.utm_y = wait_point[0], wait_point[1]
                    else:
                        logger.warning(f"fail to get vert road wait point for destination:{destination}")
            else:#纵向堆高机
                box_point = TransferPoint().get_stacker_vertical_box_loc(destination)
                if box_point:
                    stacker_offset = tos_wi.get('STACKER_OFFSET')
                    if stacker_offset:
                        terminal_point.utm_x, terminal_point.utm_y = box_point[0] + stacker_offset[0], box_point[1] + stacker_offset[1]
                    else:
                        terminal_point.utm_x, terminal_point.utm_y = box_point[0], box_point[1]
                else:
                    logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
        elif destination is not None and command_type == RemoteCommand.MOVE_VIA_STACKER:
            #只有横向堆高机会有MOVE_VIA_STACKER
            terminal_point = command.move_via_stacker_command.terminal_point
            work_point = (None,None)
            if SUPPORT_NEW_TRANSPOINT_API:
                work_point =TransferPoint().get_transfer_point_loc_v2(destination,self._truck_no, \
                        ContainerSize.Name(command_context.container_size),RemoteCommand.CommandContext.ContainerPosition.Name(command_context.container_position))
                command.command_context.container_offset_type = RemoteCommand.CommandContext.ONLY_PERCEPTION_OFFSET_TYPE
            else:
                work_point =TransferPoint().get_transfer_point_loc(destination)
            if (work_point != (None,None)):
                terminal_point.utm_x, terminal_point.utm_y = work_point[0], work_point[1]
            else:
                logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_FORWARD:
            #只有横向堆高机会有MOVE_TO_FORWARD
            terminal_point = command.move_to_forward_command.terminal_point
            work_point = (None,None)
            if SUPPORT_NEW_TRANSPOINT_API:
                #如果是要让出关路的,为兼容车端,以40尺大箱的位置作业贝基准来作偏移
                transfer_point = TransferPoint.round_down_even_bay(destination)
                work_point =TransferPoint().get_transfer_point_loc_v2(transfer_point,self._truck_no, \
                        ContainerSize.Name(ContainerSize.SIZE_40_FEET),RemoteCommand.CommandContext.ContainerPosition.Name(RemoteCommand.CommandContext.UNKNOWN_POSITION))
                command.command_context.container_offset_type = RemoteCommand.CommandContext.ONLY_PERCEPTION_OFFSET_TYPE
            else:
                work_point =TransferPoint().get_transfer_point_loc(destination)
            if (work_point != (None,None)):
                terminal_point.utm_x, terminal_point.utm_y = work_point[0], work_point[1]
            else:
                logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
            command.move_to_forward_command.offset = 8.5 #车尾关路
        elif destination is not None and command_type == RemoteCommand.WAIT_STACKER_OFF:
            command.wait_stacker_off_command.check_container = True
            if command_context.work_version_tag == RemoteCommand.CommandContext.WORK_VERSION_TAG1:
                terminal_point = command.wait_stacker_off_command.terminal_point
                work_point = (None,None)
                if SUPPORT_NEW_TRANSPOINT_API:
                    #如果是要让出关路的,为兼容车端,以40尺大箱的位置作业贝基准来作偏移.这里的逻辑其实有问题,目前没法兼容车端.但实际上车端不会执行所以不会有实际影响
                    transfer_point = TransferPoint.round_down_even_bay(destination)
                    work_point =TransferPoint().get_transfer_point_loc_v2(transfer_point,self._truck_no, \
                            ContainerSize.Name(ContainerSize.SIZE_40_FEET),RemoteCommand.CommandContext.ContainerPosition.Name(RemoteCommand.CommandContext.UNKNOWN_POSITION))
                    command.command_context.container_offset_type = RemoteCommand.CommandContext.ONLY_PERCEPTION_OFFSET_TYPE
                else:
                    work_point =TransferPoint().get_transfer_point_loc(destination)
                if (work_point != (None,None)):
                    terminal_point.utm_x, terminal_point.utm_y = work_point[0], work_point[1]
                else:
                    logger.warning(f"fail to get_transfer_point_loc for destination:{destination}")
                command.wait_stacker_off_command.offset = 8.5 #车尾关路
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_LOCK:
            # 以指令WI_NO为箱子带锁状态的key做唯一性判断, 执行装锁指令时设置始状态为未装锁
            self._lock_cache.set_container_lock_is_exist(command.command_context.wi_no, "False")
            lock_flag, lock_pavilion, source = self.get_lock_config(tos_wi)
            if lock_pavilion is not None:
                pavilion_list = common_util.parse_lock_pavilion(lock_pavilion)
                command.move_to_lock_command.poi_id = self.get_default_lock_station(pavilion_list)
                if tos_wi.get('S_LOCK_PAVILION'):
                    command.move_to_lock_command.poi_id = tos_wi.get('S_LOCK_PAVILION')
                lock_point = self._lock_cache.get_lock_station_point(command.move_to_lock_command.poi_id, 0)
                if lock_point:
                    command.move_to_lock_command.terminal_point.utm_x, command.move_to_lock_command.terminal_point.utm_y = lock_point[0], lock_point[1]
                else:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} id:{tos_wi['ID']} fail to lock poi id point:{command.move_to_lock_command.poi_id}")
            else:
                logger.warning(f"make command error: MOVE_TO_LOCK, empty lock pavilion in TosCommand")
        elif destination is not None and command_type == RemoteCommand.WAIT_LOCK_OFF:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_UNLOCK:
            # 以指令WI_NO为箱子带锁状态的key做唯一性判断, 执行卸锁指令时设置箱子初始状态为已装锁
            self._lock_cache.set_container_lock_is_exist(command.command_context.wi_no, "True")
            lock_flag, lock_pavilion, source = self.get_lock_config(tos_wi)
            if lock_pavilion is not None:
                # 重车去锁站逻辑 需要驻车
                if tos_wi.get('CTN_NO') == 'S':
                    command.move_to_unlock_command.need_park = True
                pavilion_list = common_util.parse_lock_pavilion(lock_pavilion)
                command.move_to_unlock_command.poi_id = self.get_default_lock_station(pavilion_list)
                if tos_wi.get('S_LOCK_PAVILION'):
                    command.move_to_unlock_command.poi_id = tos_wi.get('S_LOCK_PAVILION')
                lock_point = self._lock_cache.get_lock_station_point(command.move_to_unlock_command.poi_id, 1)
                if lock_point:
                    command.move_to_unlock_command.terminal_point.utm_x, command.move_to_unlock_command.terminal_point.utm_y = lock_point[0], lock_point[1]
                else:
                    logger.warning(f"vehicle:{tos_wi.get('TRUCK_NO')} id:{tos_wi['ID']} fail to unlock poi id point:{command.move_to_unlock_command.poi_id}")
            else:
                logger.warning(f"make command error: MOVE_TO_UNLOCK, empty lock pavilion in TosCommand")
        elif destination is not None and command_type == RemoteCommand.WAIT_UNLOCK_OFF:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_CHANGE:
            command.move_to_change_command.poi_id = destination
        elif destination is not None and command_type == RemoteCommand.MOVE_VIA_CHANGE_CPS:
            point = get_ms_change_station_point(tos_wi.get('TO_POS'))
            if point is not None:
                terminal_point = command.move_via_change_cps_command.terminal_point
                terminal_point.utm_x, terminal_point.utm_y = point.get('pointX'), point.get('pointY')
                command.move_via_change_cps_command.poi_id = destination
            else:
                logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get change point:{destination}")
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_REST:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_LEAVE_SPACE:
            command.move_leave_space_command.poi_id = destination
        elif destination is not None and command_type == RemoteCommand.WAIT_LEAVE_SPACE:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_STOP_WORK:
            command.move_stop_work_command.poi_id = destination
        elif destination is not None and command_type == RemoteCommand.WAIT_STOP_WORK:
            pass
        elif destination is not None and command_type == RemoteCommand.MOVE_TO_POSITION:
            if destination.startswith("POINT"):
                utm_x, utm_y = None, None
                purpose_type = RemoteCommand.CommandContext.PurposeType.UNKNOWN_PURPOSE
                purpose_dict = {
                    "RECH": RemoteCommand.CommandContext.PurposeType.CHARGE_BATTERY,
                    "EXCH": RemoteCommand.CommandContext.PurposeType.CHANGE_BATTERY,
                }
                try:
                    # 厘米级精度取整，base64编码的utm坐标
                    utm_str = tos_wi.get("CTN_NO")
                    utm_x, utm_y = struct.unpack("<ii", base64.b64decode(utm_str))
                    utm_x /= 100.0
                    utm_y /= 100.0
                    purpose_str = destination.split("-")[-1]
                    if purpose_str in purpose_dict:
                        purpose_type = purpose_dict[purpose_str]
                except:
                    pass
                if utm_x is not None and utm_y is not None:
                    position = command.move_to_position_command.position
                    position.utm_x = utm_x
                    position.utm_y = utm_y
                    command.command_context.purpose_type = purpose_type
                else:
                    logger.warning(f"vehilce_name:{tos_wi.get('TRUCK_NO')},fail to get destination point:{destination}, {tos_wi.get('CTN_NO')}")
        elif destination is not None and command_type == RemoteCommand.STOP:
            if finish_status == 'START':
                command.stop_command.stop_position = StopCommand.ROUTE_POSITION
        self._make_command_error_info(command, tos_wi, tos_wis)
        command_status = CommandWithStatus()
        command_status.command.CopyFrom(command)
        command_status.uuid = command.uuid
        command_status.status = CommandStatus.WAIT
        command_status.finish_db_status = finish_status
        command.command_source_type = RemoteCommand.CommandSourceType.TOS
        return command_status

    def get_commands(self) -> List[CommandWithStatus]:
        return self._commands

    def get_id(self):
        return self._id

    def get_wi_status(self):
        return self._wi_status

    def is_end(self):
        return self._wi_status in ['FINISH', 'CANCEL_YARD', 'CANCEL_DASH', 'ABANDON_YARD',
                                   'ABANDON_DASH']

    def get_truck_seq(self, tos_wi):
        """
        Brief: 用于获取车辆优先级及来源

        Returns: tuple (车辆优先级，优先级来源)
        """
        seq = tos_wi.get('TRUCK_SEQ')
        source = RemoteCommand.CommandContext.CommandContextSource.TOS
        if tos_wi.get('C_TRUCK_SEQ') is not None:
            # 第一优先级使用人为设置的状态
            seq = int(tos_wi.get('C_TRUCK_SEQ',0))
            source = RemoteCommand.CommandContext.CommandContextSource.MANUAL
        else:
            if tos_wi.get('S_TRUCK_SEQ') is not None:
                # 第二优先级使用调度服务结果
                seq = tos_wi.get('S_TRUCK_SEQ')
                source = RemoteCommand.CommandContext.CommandContextSource.SCHEDULE
        return seq, source


    def get_truck_buffer(self, tos_wi):
        """
        Brief: 用于获取车辆优先级及来源
        Returns: tuple (车辆优先级，优先级来源, 缓冲类型)
        """
        seq = 0
        source = RemoteCommand.CommandContext.CommandContextSource.SCHEDULE
        buffer_type = RemoteCommand.CommandContext.BufferType.NO_BUFFER
        # 第一优先级使用人为设置的状态,则不管调度状态
        if tos_wi.get('C_TRUCK_SEQ') is not None:
            return seq, source, buffer_type

        if str(tos_wi.get('S_TRUCK_BUFFER','')).isdigit():
            # 第二优先级使用调度服务结果
            seq = int(tos_wi.get('S_TRUCK_BUFFER'))
            # 参考:fence_status_to_seq,RELEASED = 1,非悬空-->S_TRUCK_SEQ =0;BLOCKED = 2,悬空-->S_TRUCK_SEQ =100;
            # SINGLE_BUFFER = 3,单级缓冲-->S_TRUCK_BUFFER=200,属于缓冲模式;BLOCKED_AND_SINGLE_BUFFER = 4,BLOCKED且SINGLE_SCH模式-->S_TRUCK_BUFFER=300,属于缓冲模式
            if tos_wi.get('C_CANCLE_BUFFER') == 'NO_BUFFER':#如果取消缓冲,进行降级
              if seq == 200:
                  seq = 0
              elif seq == 300:
                  seq = 100
            seq_to_buffer = {0:RemoteCommand.CommandContext.BufferType.NO_BUFFER,
                    100:RemoteCommand.CommandContext.BufferType.NO_BUFFER,
                    200:RemoteCommand.CommandContext.BufferType.FIRST_BUFFER,
                    300:RemoteCommand.CommandContext.BufferType.FIRST_BUFFER}
            buffer_type = seq_to_buffer.get(seq,RemoteCommand.CommandContext.BufferType.NO_BUFFER)
        return seq, source, buffer_type

    def get_priority_by_seq(self, seq):
        """
        Brief: 将车辆优先级转化为悬空状态，LOW为悬空，其他为非悬空
        """
        if seq == 1:
            return RemoteCommand.CommandContext.URGENT
        elif seq == 2:
            return RemoteCommand.CommandContext.HIGH
        elif seq == 3:
            return RemoteCommand.CommandContext.NORMAL
        elif seq == 0:
            return RemoteCommand.CommandContext.NORMAL
        else:
            return RemoteCommand.CommandContext.LOW

    def is_auto_reset(self, seq, tos_wi, command_context):
        """
        Brief: 判断是否进行自动修改
        """
        s_truck_seq = tos_wi.get('S_TRUCK_SEQ')
        s_truck_buffer = str(tos_wi.get('S_TRUCK_BUFFER', ''))
        c_cancle_buffer = tos_wi.get('C_CANCLE_BUFFER')

        if seq != 1:
            return False

        if s_truck_seq in [100, 200, 300]:
            return True

        if s_truck_buffer.isdigit():
            s_truck_buffer_int = int(s_truck_buffer)
            # 如果缓冲不降级时，需要auto_reset
            if c_cancle_buffer != 'NO_BUFFER' and s_truck_buffer_int in [200, 300]:
                command_context.command_buffer = RemoteCommand.CommandContext.BufferType.NO_BUFFER
                return True
        return False

    def get_lock_config(self, tos_wi):
        """
        Brief: 用于获取车辆装卸锁配置及来源

        Returns: tuple (装卸锁标记，锁亭，配置来源)
        """
        lock_flag = None
        lock_pavilion = None
        # 寻找最高优先级装卸锁标记
        if (tos_wi.get('C_LOCK_FLAG') and tos_wi.get('C_LOCK_PAVILION')) or tos_wi.get('C_LOCK_FLAG') == 'N':
            # 第一优先级考虑人为设置的单车配置
            lock_flag = tos_wi.get('C_LOCK_FLAG')
            lock_pavilion = str(tos_wi.get('C_LOCK_PAVILION',''))
            source = RemoteCommand.CommandContext.CommandContextSource.MANUAL
        else:
            valid_vessel_pos = False
            vessel_pos = tos_wi.get('VESSEL_POS')
            if vessel_pos and vessel_pos.isdigit and len(vessel_pos) == 6:
                # 舱内指令才需要全局装锁标记
                bay_no = int(vessel_pos[:2])
                valid_vessel_pos = True if 2 <= bay_no <= 60 else False
            if tos_wi['WI_TYPE'] == 'LOAD' and tos_wi.get('F_LOCK_FLAG') and valid_vessel_pos:
                # 第二优先级考虑人为设置的全局装锁标记，全局装锁只考虑装船，无法设置卸锁
                lock_flag = tos_wi.get('F_LOCK_FLAG')
                lock_pavilion = tos_wi.get('F_LOCK_PAVILION')
                source = RemoteCommand.CommandContext.CommandContextSource.ALL_MANUAL
            else:
                # 第三优先级考虑TOS指令内的装卸锁标记
                lock_flag = tos_wi.get('LOCK_FLAG')
                lock_pavilion = tos_wi.get('LOCK_PAVILION')
                source = RemoteCommand.CommandContext.CommandContextSource.TOS
        return lock_flag, lock_pavilion, source

    def get_area_info(self):
        vehicle_status = vehicle_cache.get_vehicle_status(self._truck_no)
        current_area_info_list = common_util.get_arrive_areas_by_location(
            (vehicle_status.position.utm_x, vehicle_status.position.utm_y)
        )
        current_lock, current_bridge = None, None
        for area_info in current_area_info_list:
            if area_info["type"] == "BRIDGE_LANE":
                current_lock = area_info
            elif area_info["type"] == "BRIDGE":
                current_bridge = area_info
        return current_lock, current_bridge

    def get_default_lock_station(self, pavilion_list):
        current_lock, current_bridge = self.get_area_info()

        # 不在引桥上或者不在车道上，默认为第一个锁站
        if current_bridge is None or current_lock is None:
            return pavilion_list[0] if pavilion_list else ""

        land_no = current_lock["name"][-1]

        # 如果当前车道没有锁站，或者锁站不在 pavilions 中，则调用 find_closest_lock_pavilion
        if not current_lock["lock_name"] or current_lock["lock_name"] not in pavilion_list:
            return self.find_closest_lock_pavilion(pavilion_list, land_no = land_no)

        # 如果锁站在 pavilions 中，返回当前锁站
        return current_lock["lock_name"]

    def find_closest_lock_pavilion(self, pavilion_list, land_no):
        land_no = int(land_no)
        bridge_index = pavilion_list[0].split('Y', 1)[0]
        nums = sorted({int(p.split('Y', 1)[1]) for p in pavilion_list})

        # 找到最接近当前车道的锁站编号
        closest = min(nums, key=lambda x: abs(x - land_no))
        return bridge_index + "Y" + str(closest)

def vehicle_status_to_dict(vehicle_status):
    vehicle_status_dict = dict()
    vehicle_status_dict['truck_no'] = vehicle_status.vehicle_name
    vehicle_status_dict['pos_x'] = vehicle_status.position.longitude
    vehicle_status_dict['pos_y'] = vehicle_status.position.latitude
    vehicle_status_dict['speed'] = vehicle_status.speed
    vehicle_status_dict[
        'sensor_status'] = 'Y' if vehicle_status.sensor_status == VehicleStatus.OK else 'N'
    return vehicle_status_dict
