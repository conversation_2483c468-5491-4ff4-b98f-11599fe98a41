db:
  mysql:
    tos_mysql:
      mode: local
    data_mysql:
      dsn: mysql://read_write_user:OD3vd4v992GfppR9@192.168.3.110:3306/nb_port_test?charset=utf8
    chassis_mysql:
      dsn: mysql://read_write_user:OD3vd4v992GfppR9@192.168.3.110:3306/nb_port_test_chassis?charset=utf8

  redis:
    mode: cluster
    sentinel:
      master: redis1
      nodes:
        - 192.168.3.110:26380
        - 192.168.3.110:26381
        - 192.168.3.110:26382
    database: 0
    # share_database生产(pro)、预发(pre)用0,其他模式(DEV、FAT)下与database保持一致
    share_database: 0
    # plc_share_database 在portman及DEV模式下与database保持一致,在生产(pro)、预发(pre)、测试(FAT)模式下用8
    plc_share_database: 0
    password: fabu

  kafka:
      producer_enable: True
      consumer_enable: True
      group_id: yz_cs_group_portman
      nodes:
        - 192.168.3.74:9093

  inner_kafka:
      producer_enable: True
      consumer_enable: True
      group_id: yz_cs_group_portman_inner
      nodes:
        - 192.168.3.74:9093

server_attribute:
  #如果是非分布式为默认为Node1-1;分布式主节点为'Node1-1';其他节点格式为'Node[major_num]-[minor_num]',如Node1-2
  #1.cluster_[]:集群,可以与其他多节点同时运行tos_bridge
  #  (1):cluster_master:集群主节点,除了启动tos_bridge,同时启动其他服务
  #  (2):cluster_slave:集群子节点,只启动tos_bridge服务
  #  (3):cluster_back:备节点,当cluster_master所在服务主机挂掉时，所有服务会被cluster_back接管
  #2.single:只单机运行tos_bridge(注意redis的database其他服务没有在用),同时运行其他服务
  nodes:
    -
      node_name: Node1-1
      mode: single

modules:
  timing_record:
    start: false
  vehicle_info:
    start: false
    publisher: false