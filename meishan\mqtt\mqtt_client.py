import paho.mqtt.client as mqtt
import traceback

from cache.config_cache import ConfigCache
from common.logger import logger, init_logger
from config.config_manage import ConfigManage
from common.periodical_task import PeriodicalTask
from meishan.mqtt.message_handler import MQTT_TOPIC_CALLBACK_MAPPING, function_handler_map, handle_unknown_function
from datetime import datetime
from meishan.mqtt.msgs import (
    MQTT_ENCRYPT_KEY_REQ_TOPIC,
    MQTT_ENCRYPT_KEY_RESP_TOPIC,
    MQTT_FMS_REQUEST_TOPIC,
    MQTT_FMS_RESPONSE_TOPIC,
    MQTT_KEEPALIVE_DOWN_TOPIC,
    MQTT_KEEPALIVE_UP_TOPIC,
    MQTT_STATION_REQUEST_TOPIC,
    MQTT_STATION_RESPONSE_TOPIC,
)


class MQTTClient:
    _instance = None

    def __init__(self,only_publish=False):
        if MQTTClient._instance:
            raise Exception("MQTTClient is a singleton!")
        MQTTClient._instance = self
        self._host = ConfigManage().get_config_mqtt_ip()
        self._port = ConfigManage().get_config_mqtt_port()
        self._username = ConfigManage().get_config_mqtt_username()
        self._password = ConfigManage().get_config_mqtt_password()
        self._client_id = ConfigManage().get_config_mqtt_client_id()
        self._subscribe_topics = [
            MQTT_ENCRYPT_KEY_REQ_TOPIC,
            MQTT_KEEPALIVE_UP_TOPIC,
            MQTT_STATION_REQUEST_TOPIC,
            MQTT_STATION_RESPONSE_TOPIC,
        ]
        self._publish_topics = [
            MQTT_ENCRYPT_KEY_RESP_TOPIC,
            MQTT_KEEPALIVE_DOWN_TOPIC,
            MQTT_FMS_REQUEST_TOPIC,
            MQTT_FMS_RESPONSE_TOPIC,
        ]
        self._only_publish = only_publish
        self.client = mqtt.Client(client_id=self._client_id, clean_session=True)
        self.client.username_pw_set(username=self._username, password=self._password)
        self.client.on_connect = self.on_connect
        self.client.on_disconnect = self.on_disconnect
        self.start_connet()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls()
        return cls._instance

    def get_client(self):
        return self.client

    def on_connect(self, client, userdata, flags, rc):
        #TODO 只需publish不需要
        logger.info(f"[MQTT] Connected with result code {rc},_only_publis:{self._only_publish}")
        if self._only_publish:
            return
        for topic in self._subscribe_topics:
            callback = MQTT_TOPIC_CALLBACK_MAPPING.get(topic)
            if callback:
                self.subscribe(topic, callback)

    def on_disconnect(self, client, userdata, rc):
        if rc != 0:
            logger.warning(f"[MQTT]] error code: {rc}，disconnet now,try reconnect...")
            self.client.reconnect()

    def on_publish(self, client, userdata, mid):
        logger.debug(f"[MQTT] mid {mid} publish to Broker")

    def publish(self, topic, message):
        logger.info(f"[MQTT] Publish => topic: {topic}, message: {message}")
        ret = True
        if self.client.is_connected():
            pub_result = self.client.publish(topic, message, 1)
            if pub_result and pub_result.rc != mqtt.MQTT_ERR_SUCCESS:
                logger.warning(f"[MQTT] publish fail,pub_result:{pub_result}")
                ret = False
        else:
            logger.warning(f"[MQTT] not connect")
            ret = False
        return ret

    def subscribe(self, topic, callback):
        logger.info(f"[MQTT] Subscribing to topic: {topic}")
        self.client.subscribe(topic)
        self.client.message_callback_add(topic, callback)

    def loop_start(self):
        self.client.loop_start()

    def start_connet(self):
        try:
            self.client.connect(host=self._host, port=self._port)
            self.loop_start()
        except Exception as e:
            logger.warning(f"[MQTT] connect error:{e},{traceback.format_exc()}")

    def start(self):
        self._listen_thread = PeriodicalTask(
            target=self.listen_loop,
            interval=1,
            master_only=True,
        )
        self._listen_thread.start()

        # 定时发送心跳
        self._keepalive_thread = PeriodicalTask(
            target=self.send_keep_alive,
            interval=30,
            master_only=True,
        )
        self._keepalive_thread.start()

        self._send_M2S_thread = PeriodicalTask(
            target=self.send_M2S_message,
            interval=1,
            master_only=True,
        )
        self._send_M2S_thread.start()

    def send_keep_alive(self):
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.publish(MQTT_KEEPALIVE_DOWN_TOPIC, now)
        logger.debug(f"[MQTT] 定时发送心跳: topic:{MQTT_KEEPALIVE_DOWN_TOPIC}, {now}")

    def send_M2S_message(self):
        infos = ConfigCache().pop_all_ms_change_msg()
        for info in infos:
            if info is None or not info.function:
                continue
            handler = function_handler_map.get(info.function, handle_unknown_function)
            handler(self, info)

    def listen_loop(self):
        pass


if __name__ == "__main__":
    init_logger("mqtt_client")
    MQTTClient.get_instance().start()