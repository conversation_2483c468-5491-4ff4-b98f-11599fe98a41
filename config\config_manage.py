import os

import yaml
import json
import re
import traceback

from common.constant import DB_IP, DB_PORT, DB_SID, DB_USER, DB_PASSWORD, DEV_USER
from common.constant import ENVIRONMENT, SIM_MODE, NODE_NAME_PREFIX, KAFKA_GROUP_ID, INNER_KAFKA_GROUP_ID, LOCAL_REDIS_PORT
from common.singleton import Singleton
from common.logger import logger
from proto.cache_pb2 import ServerNode

DEFAULT_ORACLE_CONNECTIONS = 2
DEFAULT_MYSQL_POOL_SIZE = 10
DEFAULT_MYSQL_MAX_OVER_FLOW = 5
DEFAULT_SHARE_REDIS_DB = 0
DEFAULT_PLC_SHARE_REDIS = 8
DEFAULT_SERVER_NODE_NAME = ''
DEFAULT_KAFKA_GROUP_ID = KAFKA_GROUP_ID
DEFAULT_INNER_KAFKA_GROUP_ID = INNER_KAFKA_GROUP_ID

class ConfigManage(metaclass=Singleton):
    def __init__(self):
        self._global_url = './config/global_config.yaml'
        self._global_configuration = yaml.load(open(self._global_url), Loader=yaml.FullLoader)
        self._scene_url = './config/scene/' + self.get_scene() + '/' + ENVIRONMENT +'.yaml'
        self._scene_configuration = yaml.load(open(self._scene_url), Loader=yaml.FullLoader)

    def get_scene(self):
        try:
            return self._global_configuration['scene']
        except Exception as e:
            return "ms"

    def is_ms_scene(self):
        return self._global_configuration['scene'] == 'ms'

    def is_iecs_scene(self):
        return self._global_configuration['scene'] == 'iecs'

    def is_yz_scene(self):
        return self._global_configuration['scene'] == 'yz'

    def is_yz_cs_scene(self):
        return self._global_configuration['scene'] == 'yz_cs'

    def get_env(self):
        return ENVIRONMENT

    def is_dev_env(self):
        return ENVIRONMENT == 'dev'
    
    def is_sim_mode(self):
        return SIM_MODE

    def is_fat_env(self):
        return ENVIRONMENT == 'fat'
    
    def is_pre_env(self):
        return ENVIRONMENT == 'pre'

    def is_pro_env(self):
        return ENVIRONMENT == 'pro'
  
    def _check_server_nodes_valid(self):
        ret = True
        valid_modes = ['cluster_master','cluster_slave','cluster_back','single']
        if (server_attribute:=self._scene_configuration.get('server_attribute')) is not None\
            and (server_nodes:=server_attribute.get('nodes')) is not None:
            for node in server_nodes:
                node_name = node.get('node_name','')
                match = re.match(f'{NODE_NAME_PREFIX}(\d+)-(\d+)', node_name)
                mode = node.get('mode','')
                if match is None or mode not in valid_modes:
                    logger.warning(f'[_check_server_nodes_valid] invalid node_name:{node_name} or mode:{mode}')
                    ret = False
                    break
        else:
            logger.warning(f'[_check_server_nodes_valid] invalid server node config')
            ret = False
        return ret

    def is_master_server_node(self):
        ret = False
        master_modes = ['cluster_master','single','single_master']
        if (server_attribute:=self._scene_configuration.get('server_attribute')) is not None\
            and (server_nodes:=server_attribute.get('nodes')) is not None:
            for node in server_nodes:
                mode = node.get('mode','')
                if mode in master_modes:
                    ret = True
                    break
        return ret

    def check_config_valid(self):
        if not self._check_server_nodes_valid():
            logger.warning(f'[check_config_valid] fail to check servr nodes')
            return False
        return True

    def get_scheduling_address(self):
        try:
            return f"{self._scene_configuration['service']['scheduler_server']['ip']}:{self._scene_configuration['service']['scheduler_server']['port']}"
        except Exception as e:
            return "localhost:6170"
    
    def get_gantry_control_server_address(self):
        try:
            return (self._scene_configuration['service']["gantry_control_server"]["ip"],self._scene_configuration['service']["gantry_control_server"]["port"])
        except Exception as e:
            return ("127.0.0.1",55555)

    def get_config(self):
        return self._scene_configuration

    def get_url(self):
        return self._scene_url

    def get_config_oracle(self):
        return self._scene_configuration['db']['oracle']

    def is_local_oracle(self):
        return self._scene_configuration['db']['oracle']['mode'] == 'local'

    def get_config_oracle_username(self):
        if self.is_local_oracle():
            return DB_USER
        else:
            return self._scene_configuration['db']['oracle']['username']

    def get_config_oracle_password(self):
        if self.is_local_oracle():
            return DB_PASSWORD
        else:
            return self._scene_configuration['db']['oracle']['password']

    def get_config_oracle_ip(self):
        if self.is_local_oracle():
            return os.environ.get('ANTENNA_DB_IP', DB_IP)
        else:
            return self._scene_configuration['db']['oracle']['ip']

    def get_config_oracle_port(self):
        if self.is_local_oracle():
            return os.environ.get('ANTENNA_DB_PORT', DB_PORT)
        else:
            return self._scene_configuration['db']['oracle']['port']

    def get_config_oracle_sid(self):
        if self.is_local_oracle():
            return os.environ.get('ANTENNA_DB_SID', DB_SID)
        else:
            return self._scene_configuration['db']['oracle']['sid']
    
    def get_config_oracle_service_name(self):
        return self._scene_configuration['db']['oracle']['service_name']

    def get_config_oracle_max_connections(self):
        try:
            return self._scene_configuration['db']['oracle']['max_connections']
        except Exception as e:
            return DEFAULT_ORACLE_CONNECTIONS

    def get_config_oracle_dsn(self):
        username = self.get_config_oracle_username()
        password = self.get_config_oracle_password()
        ip = self.get_config_oracle_ip()
        port = self.get_config_oracle_port()
        dsn = ''
        if 'sid' in self._scene_configuration['db']['oracle']:
            sid = self.get_config_oracle_sid()
            dsn = 'oracle://{}:{}@{}:{}/{}'.format(username, password, ip, port, sid)
        elif 'service_name' in self._scene_configuration['db']['oracle']:
            service_name = self.get_config_oracle_service_name()
            dsn = 'oracle://{}:{}@{}:{}/?service_name={}'.format(username, password, ip, port, service_name)
        return dsn

    def get_config_mysql(self):
        return self._scene_configuration['db']['mysql']

    def get_config_data_mysql(self):
        return self._scene_configuration['db']['mysql']['data_mysql']

    def get_config_data_mysql_dsn(self):
        return self._scene_configuration['db']['mysql']['data_mysql']['dsn']

    def get_config_data_mysql_pool_size(self):
        try:
            return self._scene_configuration['db']['mysql']['data_mysql']['pool_size']
        except Exception as e:
            return DEFAULT_MYSQL_POOL_SIZE

    def get_config_data_mysql_max_overflow(self):
        try:
            return self._scene_configuration['db']['mysql']['data_mysql']['max_overflow']
        except Exception as e:
            return DEFAULT_MYSQL_MAX_OVER_FLOW

    def get_config_monitor_mysql(self):
        return self._scene_configuration['db']['mysql']['monitor_mysql']

    def get_config_monitor_mysql_dsn(self):
        return self._scene_configuration['db']['mysql']['monitor_mysql']['dsn']

    def get_config_monitor_mysql_pool_size(self):
        try:
            return self._scene_configuration['db']['mysql']['monitor_mysql']['pool_size']
        except Exception as e:
            return DEFAULT_MYSQL_POOL_SIZE

    def get_config_monitor_mysql_max_overflow(self):
        try:
            return self._scene_configuration['db']['mysql']['monitor_mysql']['max_overflow']
        except Exception as e:
            return DEFAULT_MYSQL_MAX_OVER_FLOW

    def get_config_chassis_mysql(self):
        return self._scene_configuration['db']['mysql']['chassis_mysql']

    def get_config_chassis_mysql_dsn(self):
        return self._scene_configuration['db']['mysql']['chassis_mysql']['dsn']

    def get_config_chassis_mysql_pool_size(self):
        try:
            return self._scene_configuration['db']['mysql']['chassis_mysql']['pool_size']
        except Exception as e:
            return DEFAULT_MYSQL_POOL_SIZE

    def get_config_chassis_mysql_max_overflow(self):
        try:
            return self._scene_configuration['db']['mysql']['chassis_mysql']['max_overflow']
        except Exception as e:
            return DEFAULT_MYSQL_MAX_OVER_FLOW

    def get_config_plc_mysql(self):
        return self._scene_configuration['db']['mysql']['plc_mysql']

    def get_config_plc_mysql_dsn(self):
        return self._scene_configuration['db']['mysql']['plc_mysql']['dsn']

    def get_config_plc_mysql_pool_size(self):
        try:
            return self._scene_configuration['db']['mysql']['plc_mysql']['pool_size']
        except Exception as e:
            return DEFAULT_MYSQL_POOL_SIZE

    def get_config_plc_mysql_max_overflow(self):
        try:
            return self._scene_configuration['db']['mysql']['plc_mysql']['max_overflow']
        except Exception as e:
            return DEFAULT_MYSQL_MAX_OVER_FLOW

    def get_config_tos_mysql(self):
        return self._scene_configuration['db']['mysql']['tos_mysql']

    def is_local_tos_mysql(self):
        return self._scene_configuration['db']['mysql']['tos_mysql']['mode'] == 'local'

    def get_config_tos_mysql_dsn(self):
        if self.is_local_tos_mysql():
            ip = os.environ.get('ANTENNA_MYSQL_DB_IP', DB_IP)
            port = os.environ.get('ANTENNA_MYSQL_DB_PORT', DB_PORT)
            sid = os.environ.get('ANTENNA_MYSQL_DB_SID', DB_SID)
            user = os.environ.get('ANTENNA_DB_USER', DB_USER)
            password = os.environ.get('ANTENNA_DB_PASSWORD', DB_PASSWORD)
            dsn = f"mysql://{user}:{password}@{ip}:{port}/{sid}?charset=utf8"
        else:
            dsn = self._scene_configuration['db']['mysql']['tos_mysql']['dsn']
        return dsn

    def get_config_tos_mysql_pool_size(self):
        try:
            return self._scene_configuration['db']['mysql']['tos_mysql']['pool_size']
        except Exception as e:
            return DEFAULT_MYSQL_POOL_SIZE

    def get_config_tos_mysql_max_overflow(self):
        try:
            return self._scene_configuration['db']['mysql']['tos_mysql']['max_overflow']
        except Exception as e:
            return DEFAULT_MYSQL_MAX_OVER_FLOW

    def get_config_pscp_redis_ip(self):
        try:
            return self._scene_configuration['db']['pscp_redis']['ip']
        except Exception as e:
            return "localhost"

    def get_config_pscp_redis_port(self):
        try:
            return self._scene_configuration['db']['pscp_redis']['port']
        except Exception as e:
            return LOCAL_REDIS_PORT

    def get_config_pscp_redis_database(self):
        try:
            return self._scene_configuration['db']['pscp_redis']['database']
        except Exception as e:
            return 0

    def get_config_pscp_redis_password(self):
        try:
            return self._scene_configuration['db']['pscp_redis']['password']
        except Exception as e:
            logger.warning(f"get_config_pscp_redis_password: fail to get")
            return ''

    def get_config_redis(self):
        return self._scene_configuration['db']['redis']

    def get_config_redis_mode(self):
        return self._scene_configuration['db']['redis']['mode']

    def is_single_redis(self):
        return self.get_config_redis_mode() == 'single'

    def is_cluster_redis(self):
        return self.get_config_redis_mode() == 'cluster'

    def get_config_redis_ip(self):
        return self._scene_configuration['db']['redis']['ip']

    def get_config_redis_port(self):
        return self._scene_configuration['db']['redis']['port']

    def get_config_redis_sentinel(self):
        return self._scene_configuration['db']['redis']['sentinel']

    def get_config_redis_sentinel_master(self) -> dict:
        return self._scene_configuration['db']['redis']['sentinel']['master']

    def get_config_redis_sentinel_all_nodes(self) -> list:
        node_list = list()
        for node in self._scene_configuration['db']['redis']['sentinel']['nodes']:
            node_dict = {'ip': node.split(":", 1)[0], 'port': node.split(":", 1)[1]}
            node_list.append(node_dict)
        return node_list

    def get_config_redis_database(self):
        return self._scene_configuration['db']['redis']['database']

    def get_config_redis_share_database(self):
        try:
            return self._scene_configuration['db']['redis']['share_database']
        except Exception as e:
            return DEFAULT_SHARE_REDIS_DB

    #如果配置了share_nodes如果没有则用plc_share_nodes,若没有则使用nodes
    def get_config_redis_share_sentinel_all_nodes(self) -> list:
        node_list = list()
        if self._scene_configuration['db']['redis'].get('share_nodes'):
            for node in self._scene_configuration['db']['redis']['share_nodes']:
              node_dict = {'ip': node.split(":", 1)[0], 'port': node.split(":", 1)[1]}
              node_list.append(node_dict)
        else:
            node_list = self.get_config_redis_sentinel_all_nodes()
        return node_list

    def get_config_redis_plc_share_database(self):
        try:
            return self._scene_configuration['db']['redis']['plc_share_database']
        except Exception as e:
            return DEFAULT_PLC_SHARE_REDIS

    #如果配置了plc_share_nodes则用plc_share_nodes,若没有则使用nodes
    def get_config_redis_plc_share_sentinel_all_nodes(self) -> list:
        node_list = list()
        if self._scene_configuration['db']['redis'].get('plc_share_nodes'):
            for node in self._scene_configuration['db']['redis']['plc_share_nodes']:
              node_dict = {'ip': node.split(":", 1)[0], 'port': node.split(":", 1)[1]}
              node_list.append(node_dict)
        else:
            node_list = self.get_config_redis_sentinel_all_nodes()
        return node_list

    def get_config_redis_password(self):
        try:
            return self._scene_configuration['db']['redis']['password']
        except Exception as e:
            logger.warning(f"get_config_redis_password: fail to get")
            return ''


    def get_config_server_attribute(self):
        try:
            return self._scene_configuration['server_attribute']
        except Exception as e:
            logger.warning(f"get_server_node_name:fail to get service server_attribute")
            return ''

    def get_server_node_name(self):
        try:
            return self._scene_configuration['server_attribute']['node_name']
        except Exception as e:
            logger.warning(f"get_server_node_name:fail to get service node name,use default:{DEFAULT_SERVER_NODE_NAME}")
            return DEFAULT_SERVER_NODE_NAME

    def get_server_attribute_mode(self):
        try:
            return self._scene_configuration['server_attribute']['mode']
        except Exception as e:
            logger.warning(f"get_server_node_name:fail to get service mode")
            return ''
        
    def get_modules_config(self, module_name):
        try:
            return self._scene_configuration['modules'][module_name]
        except Exception as e:
            logger.warning(f"get_modules_config: invalid module name")
            return None
        
    def get_modules_start_mode(self, module_name):
        try:
            return self._scene_configuration['modules'][module_name]['start']
        except Exception as e:
            logger.warning(f"get_modules_start_mode: invalid module name or start mode")
            return True

    def get_modules_fun_mode(self, module_name, fun_name):
        try:
            return self._scene_configuration['modules'][module_name][fun_name]
        except Exception as e:
            logger.warning(f"get_modules_fun_mode: invalid module name or fun_name: {fun_name}")
            return True

    def get_config_server_nodes(self):
        nodes = []
        if (server_attribute:=self._scene_configuration.get('server_attribute')) is not None\
            and (server_nodes:=server_attribute.get('nodes')) is not None:
            for node in server_nodes:
                try:
                    node_cache = ServerNode()
                    node_cache.node_name = node.get('node_name','')
                    node_cache.mode = ServerNode.NodeMode.Value(node.get('mode','UNKNOW').upper())
                    nodes.append(node_cache)
                except Exception as e:
                    logger.error(f"{node} fail to get config server nodes,e:{e}, trace:{traceback.format_exc()}")
        else:
            logger.error(f"fail to get config server nodes")
        return nodes

    def get_keepalive_services(self):
        try:
            return self._scene_configuration['keepalive_services']
        except Exception as e:
            return {}

    def get_config_kafka_producer_enable(self):
        try:
            return self._scene_configuration['db']['kafka'].get('producer_enable', False)
        except Exception as e:
            return False
    
    def get_config_kafka_consumer_enable(self):
        try:
            return self._scene_configuration['db']['kafka'].get('consumer_enable', False)
        except Exception as e:
            return False

    def get_config_kafka_nodes(self) -> list:
        node_list = list()
        try:
            nodes_config = self._scene_configuration['db']['kafka'].get('nodes', [])
            for node in nodes_config:
                node_list.append(node)
        except Exception as e:
            pass
        return node_list

    def get_config_kafka_group_id(self):
        try:
            group_id = (self._scene_configuration['db']['kafka'].get('group_id',DEFAULT_KAFKA_GROUP_ID) + "-" + DEV_USER) \
              if self._scene_configuration['db']['kafka'].get('group_id_suffix_enable', False) else self._scene_configuration['db']['kafka'].get('group_id',DEFAULT_KAFKA_GROUP_ID)
            return group_id
        except Exception as e:
            return DEFAULT_KAFKA_GROUP_ID

    def get_config_inner_kafka_producer_enable(self):
        try:
            return self._scene_configuration['db']['inner_kafka'].get('producer_enable', False)
        except Exception as e:
            return self.get_config_kafka_producer_enable()
    
    def get_config_inner_kafka_consumer_enable(self):
        try:
            return self._scene_configuration['db']['inner_kafka'].get('consumer_enable', False)
        except Exception as e:
            return self.get_config_kafka_consumer_enable()

    def get_config_inner_kafka_nodes(self) -> list:
        node_list = list()
        try:
            nodes_config = self._scene_configuration['db']['inner_kafka'].get('nodes', [])
            for node in nodes_config:
                node_list.append(node)
            return node_list
        except Exception as e:
            return self.get_config_kafka_nodes()
        

    def get_config_inner_kafka_group_id(self):
        try:
            return self._scene_configuration['db']['inner_kafka'].get('group_id',DEFAULT_INNER_KAFKA_GROUP_ID)
        except Exception as e:
            return DEFAULT_INNER_KAFKA_GROUP_ID


    def get_config_kafka_sasl(self):
        try:
            sasl_config = self._scene_configuration['db']['kafka'].get('sasl_config',{})
            if sasl_config and sasl_config.get('sasl_enable', False):
                return sasl_config
            else:
                return {}
        except Exception as e:
            return {}

    def get_config_inner_kafka_sasl(self):
        try:
            sasl_config = self._scene_configuration['db']['inner_kafka'].get('sasl_config',{})
            if sasl_config and sasl_config.get('sasl_enable', False):
                return sasl_config
            else:
                return {}
        except Exception as e:
            return {}

    def get_config_trace_server(self):
        try:
            return self._scene_configuration['service'].get('trace_server',{})
        except Exception as e:
            return {}

    def get_config_trace_server_enable(self):
        config = self.get_config_trace_server()
        if config and config.get('enable',False) and len(config.get('end_point','')) > 0:
            return True
        return False

    def get_config_trace_server_end_point(self):
        config = self.get_config_trace_server()
        return config.get('end_point','')

    def get_config_gantry_store_start(self):
        if (module_config:=self._scene_configuration['modules'].get('gantry_store')):
            return module_config.get('start',False)
        else:
            return False

    def get_config_gantry_store_check_alive(self):
        if (module_config:=self._scene_configuration['modules'].get('gantry_store')):
            return module_config.get('check_alive',{})
        else:
            return {}

    def get_config_gantry_store_init_test(self):
        if (module_config:=self._scene_configuration['modules'].get('gantry_store')):
            return module_config.get('init_test',False)
        else:
            return False

    def get_config_mqtt_username(self):
        try:
            return self._scene_configuration['mqtt']['username']
        except Exception as e:
            return {}

    def get_config_mqtt_password(self):
        try:
            return self._scene_configuration['mqtt']['password']
        except Exception as e:
            return {}

    def get_config_mqtt_ip(self):
        try:
            return self._scene_configuration['mqtt']['broker']
        except Exception as e:
            return {}

    def get_config_mqtt_port(self):
        try:
            return self._scene_configuration['mqtt']['port']
        except Exception as e:
            return {}

    def get_config_mqtt_client_id(self):
        try:
            return self._scene_configuration['mqtt']['client_id']
        except Exception as e:
            return {}

    def get_config_pre_wi_start(self):
        if (module_config:=self._scene_configuration['modules'].get('pre_wi')):
            return module_config.get('start',False)
        else:
            return False


if __name__ == '__main__':
    # print(ConfigManage().get_config())
    # print(ConfigManage().get_config_oracle())
    # print(ConfigManage().get_config_redis())
    # print(ConfigManage().get_config_redis_sentinel_all_nodes())
    # print(ConfigManage().get_config_redis_database())

    # test scheduling
    # print(ConfigManage().get_scheduling_address())

    # test scene
    print(ConfigManage().get_scene())
    print(ConfigManage().is_ms_scene())
    print(ConfigManage().is_iecs_scene())

    # test env
    print(ConfigManage().get_env())
    print(ConfigManage().is_dev_env())
    print(ConfigManage().is_fat_env())
    print(ConfigManage().is_pre_env())
    print(ConfigManage().is_pro_env())

    # modules config
    print(ConfigManage().get_modules_config("vehicle_info"))
    print(ConfigManage().get_modules_config("vehicle"))
    print(ConfigManage().get_modules_start_mode("vehicle_info"))
    print(ConfigManage().get_modules_start_mode("vehicle"))
    print(ConfigManage().get_modules_fun_mode("vehicle_info", "publisher"))
    print(ConfigManage().get_modules_fun_mode("vehicle", "publisher"))

    # test
    print(ConfigManage().get_keepalive_services())
