import os
import datetime

from common.constant import CHECK_DELETE_CHASSIS_INTERVAL_TIME, CHASSIS_DATA_DIR, CHASSIS_SUPPOR_BY_FILE
from common.logger import logger
from common.periodical_task import PeriodicalTask
from model.chassis_detail import delete_abandoned_data, BaseChassisInfo

def do_at_midnight():
    dt = datetime.datetime.now()
    logger.warning(f"Execute funciont do_at_midnight at {dt}.......")
    if (dt.hour == 23 and dt.minute > 30) or 0 < dt.hour < 23:
        return
    logger.warning(f"BaseChassisInfo.table_number => {BaseChassisInfo.table_number()}")
    delete_abandoned_data()


def start():
    if not os.path.exists(CHASSIS_DATA_DIR):
        os.makedirs(CHASSIS_DATA_DIR)
        print(f"[scheduled_delete_chassis] create chassis_data dir:{CHASSIS_DATA_DIR}")
    master_only = False if CHASSIS_SUPPOR_BY_FILE else True
    thread=PeriodicalTask(target=do_at_midnight, interval=CHECK_DELETE_CHASSIS_INTERVAL_TIME, master_only=master_only)
    thread.start()


if __name__ == '__main__':
    start()