
import traceback,time
import numpy as np
from common.logger import logger
from common.singleton import Singleton
from common.constant import STACKER_VERTICAL_YARDS_BAYS, SUPPORT_TRAILER_CENTER_TERMINAL
from plc.crane_receiver import <PERSON><PERSON>nfoContainer
from port_box_map.box_region_interface import get_service_point_loc
from port_box_map.box_region_interface_trailer_center import get_service_point_loc as get_trailer_center_service_point_loc
from port_box_map.box_region_interface_meishan_stacker_v2 import get_box_loc as get_stacker_box_loc
from port_box_map.get_point_interface import TargetPointManager
from common.name_converter import NameConverter

class TransferPoint(metaclass=Singleton):
    def __init__(self):
        self._target_point_manager = TargetPointManager()

    def _apply_xf_offset(self, utm_x, utm_y, transfer_point):
        """XF箱区特殊处理 - 向右偏移"""
        if utm_x is None or utm_y is None:
            return utm_x, utm_y

        if transfer_point.startswith('XF'):
            # 两车道中心线距离约3米，向右偏移
            lane_center_offset = 3.0
            utm_x += lane_center_offset
            logger.debug(f"Applied XF offset for {transfer_point}: +{lane_center_offset}m in X direction")

        return utm_x, utm_y

    def _apply_8m_offset(self, utm_x, utm_y, transfer_point):
        """8M箱区特殊处理 - 向左偏移"""
        if utm_x is None or utm_y is None:
            return utm_x, utm_y

        if transfer_point.startswith('8M'):
            # 两车道中心线距离约3米，向左偏移
            lane_center_offset = -3.0
            utm_x += lane_center_offset
            logger.debug(f"Applied 8M offset for {transfer_point}: {lane_center_offset}m in X direction")

        return utm_x, utm_y

    def get_transfer_point_loc(self, destination: str):
        try:
            return (get_service_point_loc(destination) if not SUPPORT_TRAILER_CENTER_TERMINAL \
                                    else get_trailer_center_service_point_loc(destination))
        except Exception as e:
            logger.warning(f"fail to get_transfer_point_loc for destination:{destination},error: {e},{traceback.format_exc()}")
        return None

    def _convert_point_interface(self,vehicle_name = '', ctn_size = '', ctn_pos = '', vehicle_type = ''):
        if not vehicle_type:
            vehicle_type = NameConverter.to_vehicle_type(vehicle_name)
        ctn_type = None
        if ctn_size in ['SIZE_UNKNOWN','SIZE_40_FEET','SIZE_BINDED_TWIN']:
            ctn_type = 'SIZE_40'
        elif ctn_size in ['SIZE_45_FEET']:
            ctn_type = 'SIZE_45'
        elif ctn_size in ['SIZE_20_FEET']:
            if ctn_pos == 'FRONT':
                ctn_type = 'SIZE_20_FRONT'
            elif ctn_pos == 'MIDDLE':
                ctn_type = 'SIZE_20_MID'
            elif ctn_pos == 'BEHIND':
                ctn_type = 'SIZE_20_BEHIND'
            else:
                logger.warning(f"vehicle_name:{vehicle_name} not support ctn_pos:{ctn_pos}")
        else:
            logger.warning(f"vehicle_name:{vehicle_name} not support ctn_size:{ctn_size}")
        return (vehicle_type, ctn_type)

    #将奇数贝转化换为偶数贝
    @staticmethod
    def round_down_even_bay(transfer_point: str):
        if transfer_point[-1].isdigit() and int(transfer_point[-1]) % 2 == 1:
            result = transfer_point[:-1] + str(int(transfer_point[-1]) - 1)
            logger.info(f"round_down_even_bay: convert transfer_point from {transfer_point} to {result}")
            return result
        return transfer_point

    def get_transfer_point_loc_v2(self, transfer_point: str, vehicle_name = '', ctn_size = '', ctn_pos = '', vehicle_type = ''):
        start = time.time()
        utm_x = None
        utm_y = None
        try:
            if transfer_point.startswith('CR'):
                utm_x, utm_y = CraneInfoContainer().get_position_by_crane_no(int(transfer_point[2:]))
            elif len(transfer_point) == 4 or len(transfer_point) == 2:# modified and transfer_point.isdigit()
                transfer_point = transfer_point if len(transfer_point) == 4 else transfer_point + '06'
                (vehicle_type, ctn_type) = self._convert_point_interface(vehicle_name,ctn_size,ctn_pos,vehicle_type)
                logger.debug(f"vehicle_name:{vehicle_name},transfer_point:{transfer_point},ctn_size:{ctn_size},ctn_pos:{ctn_pos}-->vehicle_type:{vehicle_type},ctn_type:{ctn_type}")
                if vehicle_type and ctn_type:
                    utm_x, utm_y = self._target_point_manager.get_point_interface(port='meishan', vehicle_type = vehicle_type, yard_bay = transfer_point, ctn_type = ctn_type)
                    # 应用XF箱区offset
                    utm_x, utm_y = self._apply_xf_offset(utm_x, utm_y, transfer_point)
                    # 应用8M箱区offset
                    utm_x, utm_y = self._apply_8m_offset(utm_x, utm_y, transfer_point)
                if vehicle_type == None or ctn_type == None or utm_x == None or utm_y == None:
                    logger.warning(f"vehicle_name:{vehicle_name},input:[transfer_point:{transfer_point},ctn_size:{ctn_size},ctn_pos:{ctn_pos}]"
                                    f",output:[vehicle_type:{vehicle_type},ctn_type:{ctn_type}] fail to get_point_interface")
            elif transfer_point == "FLD":pass
            else:
                logger.warning(f"not support transfer_point:{transfer_point}")
        except Exception as e:
            logger.warning(
                f"fail to get transfer point utm:{transfer_point} point,error:{e}, trace:{traceback.format_exc()}")
        end = time.time()
        if end - start > 1:
            logger.warning(f"get_transfer_point_loc time out , tp: {transfer_point}")
        return (utm_x, utm_y)

    def get_stacker_vertical_box_loc(self, destination: str):
        support_yards_bays = STACKER_VERTICAL_YARDS_BAYS
        destination = str(destination).split('.')[0]
        try:
            if not ((yard_bays:=support_yards_bays.get(destination[:2],[])) and str(destination[2:4]) in yard_bays):
                logger.warning(f"not support bay:{destination[2:4]} in yard_bays:{yard_bays}")
                return None
            loc = get_stacker_box_loc(destination[:2], destination[2:4], destination[4:])
            return loc
        except Exception as e:
            logger.warning(f"fail to get_stacker_vertical_box_loc for destination:{destination},error:{e},{traceback.format_exc()}")
            return None

if __name__ == '__main__':
    import math
    transfer_point = 'XG44'
    vehicle_name = 'AT800'
    ctn_size = 'SIZE_40_FEET'
    #ctn_size = 'SIZE_45_FEET'
    #ctn_size = 'SIZE_20_FEET'
    #ctn_size = 'SIZE_BINDED_TWIN'
    #ctn_size = 'SIZE_UNKNOWN'
    ctn_pos = 'FRONT'
    ctn_pos = 'MIDDLE'
    #ctn_pos = 'BEHIND'
    vehicle_type = ''
    #vehicle_type = 'center'
    print(f"vehicle_name:{vehicle_name},transfer_point:{transfer_point},ctn_size:{ctn_size},ctn_pos:{ctn_pos},vehicle_type:{vehicle_type}")
    print(f"round_down_even_bay:{TransferPoint.round_down_even_bay(transfer_point)}")
    (utm_x, utm_y) = TransferPoint().get_transfer_point_loc(transfer_point)
    print(f"old api:get_transfer_point_loc:{(utm_x, utm_y)}")
    (utm_x1, utm_y1) = TransferPoint().get_transfer_point_loc_v2(transfer_point,vehicle_name,ctn_size,ctn_pos, vehicle_type)
    print(f"new api:get_transfer_point_loc_v2:{(utm_x1, utm_y1)}")
    if utm_x and utm_y and utm_x1 and utm_y1:
        pow_1 = math.pow(abs(utm_x-utm_x1),2)
        pow_2 = math.pow(abs(utm_y-utm_y1),2)
        dis = math.sqrt(abs(pow_1 + pow_2))
        print(f"dis:{dis},({utm_x},{utm_y})--({utm_x1},{utm_y1})")
