import time
from typing import Optional, Iterable, List

from cache.client import CacheClient, PlcShareCacheClient
from common.logger import logger
from config.config_manage import ConfigManage
from proto import cache_pb2

CRANE_PREFIX = 'crane-info'
GANTRY_PREFIX = 'gantry-info'
LOCK_STATION_PREFIX = 'lock-station-info'
CRANE_NO_SET = 'crane-no-set'
GANTRY_NO_SET = 'gantry-no-set'
CRANE_GPOS_JUMP_PREFIX = 'crane-gpos-jump-prefix'
CRANE_STORE_PREFIX = 'crane-store-info'
CRANE_STORE_NO_SET = 'crane-store-no-set'
GANTRY_STORE_PREFIX = 'gantry-store-info'
GANTRY_STORE_WARNING_PREFIX = 'gantry-store-warning-info'
GANTRY_STORE_PROXIMITY_WARNING_PREFIX = 'gantry-store-proximity-warning-info'
GANTRY_STORE_NO_SET = 'gantry-store-no-set'
GANTRY_STORE_SERVER_STATUS = 'gantry-store-server-status'
CRANE_INFO_MUTEX_LOCK_PREFIX = 'crane-info-mutex-lock'
CONFIG_MANGETIC_CRANE_PREFIX = 'config-mangetic-crane'
GANTRY_INFO_MUTEX_LOCK_PREFIX = 'gantry-info-mutex-lock'
CRANE_EXT_PREFIX = 'crane-ext-info'
DEVICE_OFFSET_SET = 'device-offset-set'

# 现场测试、预发环境使用共享Database
if ConfigManage().is_fat_env() or ConfigManage().is_pre_env():
    PlcCacheClient = PlcShareCacheClient
else:
    PlcCacheClient = CacheClient


# 生产环境同步数据至共享Database内
def is_support_plc_share():
    return ConfigManage().is_pro_env()


def plc_time_valid(info_time_ns, ex=60.0, cur_time=time.time()):
    if ex == None:
        return True
    return cur_time - info_time_ns / 1000000000.0 < ex


def set_crane(crane_no: int, info: cache_pb2.CraneInfo, ex=None):
    name = f"{CRANE_PREFIX}"
    key = f"{crane_no}"
    CacheClient().hset_proto(name, key, info)
    if is_support_plc_share():
        PlcShareCacheClient().hset_proto(name, key, info)


def get_crane(crane_no: int, ex=60.0) -> Optional[cache_pb2.CraneInfo]:
    name = f"{CRANE_PREFIX}"
    key = f"{crane_no}"
    crane = PlcCacheClient().hget_proto(name, key, cache_pb2.CraneInfo)
    if crane is not None and not plc_time_valid(crane.recv_timestamp, ex):
        crane = None
    return crane


def get_cranes(crane_nos, ex=60.0) -> List[Optional[cache_pb2.CraneInfo]]:
    name = f"{CRANE_PREFIX}"
    keys = [f"{crane_no}" for crane_no in crane_nos]
    cranes = PlcCacheClient().hmget_proto(name, keys, cache_pb2.CraneInfo)
    cur_time = time.time()
    cranes = [crane for crane in cranes if crane is not None and plc_time_valid(crane.recv_timestamp, ex, cur_time)]
    return cranes


def get_ex_cranes(ex=60.0) -> List[cache_pb2.CraneInfo]:
    name = f"{CRANE_PREFIX}"
    cranes = PlcCacheClient().hgetvals_proto(name, cache_pb2.CraneInfo)
    cur_time = time.time()
    cranes = [crane for crane in cranes if crane is not None and plc_time_valid(crane.recv_timestamp, ex, cur_time)]
    return cranes


def get_ex_share_cranes(ex=60.0) -> List[cache_pb2.CraneInfo]:
    cranes = []
    name = f"{CRANE_PREFIX}"
    if is_support_plc_share():
        cranes = PlcShareCacheClient().hgetvals_proto(name, cache_pb2.CraneInfo)
        cur_time = time.time()
        cranes = [crane for crane in cranes if crane is not None and plc_time_valid(crane.recv_timestamp, ex, cur_time)]
    return cranes


def set_gpos_jump(crane_no: int, jump: int, ex=None):
    key = f"{CRANE_GPOS_JUMP_PREFIX}:{crane_no}"
    proto = cache_pb2.LastGPosJump()
    proto.crane_no = crane_no
    proto.jump = jump
    proto.timestamp = time.time()
    CacheClient().set_proto(key, proto, ex)
    if is_support_plc_share():
        PlcShareCacheClient().set_proto(key, proto, ex)


def get_gpos_jump(crane_no: int):
    key = f"{CRANE_GPOS_JUMP_PREFIX}:{crane_no}"
    proto = CacheClient().get_proto(key, cache_pb2.LastGPosJump)
    return proto.jump if proto is not None else None


def get_gpos_jump_ts(crane_no: int):
    key = f"{CRANE_GPOS_JUMP_PREFIX}:{crane_no}"
    proto = CacheClient().get_proto(key, cache_pb2.LastGPosJump)
    return proto.timestamp if proto is not None else None


def refresh_online_cranes(crane_nos: Iterable[int]):
    return CacheClient().update_set(CRANE_NO_SET, crane_nos, int)


def get_online_crane_nos() -> List[int]:
    return CacheClient().get_set(CRANE_NO_SET, int)


# ----------------extra crane info-----------------------------#
def set_ext_crane(crane_no: int, info: cache_pb2.CraneExtInfo, ex=None):
    name = f"{CRANE_EXT_PREFIX}"
    key = f"{crane_no}"
    CacheClient().hset_proto(name, key, info)


def get_ext_crane(crane_no: int, ex=60.0) -> Optional[cache_pb2.CraneExtInfo]:
    name = f"{CRANE_EXT_PREFIX}"
    key = f"{crane_no}"
    crane = CacheClient().hget_proto(name, key, cache_pb2.CraneExtInfo)
    if crane is not None and not plc_time_valid(crane.recv_timestamp, ex):
        crane = None
    return crane


def get_ext_cranes(ex=60.0) -> List[cache_pb2.CraneExtInfo]:
    name = f"{CRANE_EXT_PREFIX}"
    cranes = CacheClient().hgetvals_proto(name, cache_pb2.CraneExtInfo)
    cur_time = time.time()
    cranes = [crane for crane in cranes if crane is not None and plc_time_valid(crane.recv_timestamp, ex, cur_time)]
    return cranes


# --------------------------------------------------#
def delete_store_crane(crane_no: int):
    key = f"{CRANE_STORE_PREFIX}" + '-' + f"{crane_no}"
    PlcShareCacheClient().delete(key)


def set_store_crane(crane_no: int, info: cache_pb2.CraneStoreInfo, suffix='', ex=None):
    name = f"{CRANE_STORE_PREFIX}" + '-' + f"{crane_no}"
    key = f"{suffix}"
    PlcShareCacheClient().hset_proto(name, key, info)


def get_store_crane(crane_no: int, suffix='', ex=2.0) -> Optional[cache_pb2.CraneStoreInfo]:
    name = f"{CRANE_STORE_PREFIX}" + '-' + f"{crane_no}"
    key = f"{suffix}"
    crane = PlcShareCacheClient().hget_proto(name, key, cache_pb2.CraneStoreInfo)
    cur_time = time.time()
    if crane is not None and not plc_time_valid(crane.timestamp_ns, ex, cur_time):
        crane = None
    return crane


def get_store_crane_infos(crane_no: int, ex=2.0) -> List[cache_pb2.CraneStoreInfo]:
    name = f"{CRANE_STORE_PREFIX}" + '-' + f"{crane_no}"
    cranes = PlcShareCacheClient().hgetvals_proto(name, cache_pb2.CraneStoreInfo)
    cur_time = time.time()
    cranes = [crane for crane in cranes if crane is not None and plc_time_valid(crane.timestamp_ns, ex, cur_time)]
    return cranes


def refresh_online_store_cranes(crane_nos: Iterable[int]):
    return PlcShareCacheClient().update_set(CRANE_STORE_NO_SET, crane_nos, int)


def get_online_store_crane_nos() -> List[int]:
    return PlcShareCacheClient().get_set(CRANE_STORE_NO_SET, int)


# ----------------------suffix就是vehicle_name--------------------------#
def delete_store_gantry(gantry_no: int):
    key = f"{GANTRY_STORE_PREFIX}" + '-' + f"{gantry_no}"
    PlcShareCacheClient().delete(key)

def set_store_gantry(gantry_no: int, info: cache_pb2.GantryStoreInfo, suffix='', ex=None):
    name = f"{GANTRY_STORE_PREFIX}" + '-' + f"{gantry_no}"
    key = f"{suffix}"
    if info.ctrl_mode == 0:
        return PlcShareCacheClient().hdel(name, key)
    return PlcShareCacheClient().hset_proto(name, key, info)

def get_store_gantry(gantry_no: int, suffix='', ex=2.0) -> Optional[cache_pb2.GantryStoreInfo]:
    name = f"{GANTRY_STORE_PREFIX}" + '-' + f"{gantry_no}"
    key = f"{suffix}"
    gantry = PlcShareCacheClient().hget_proto(name, key, cache_pb2.GantryStoreInfo)
    cur_time = time.time()
    if gantry is not None and not plc_time_valid(gantry.timestamp_ns, ex, cur_time):
        gantry = None
    return gantry

def get_store_gantry_infos(gantry_no: int, ex=2.0) -> List[cache_pb2.GantryStoreInfo]:
    name = f"{GANTRY_STORE_PREFIX}" + '-' + f"{gantry_no}"
    gantries = PlcShareCacheClient().hgetvals_proto(name, cache_pb2.GantryStoreInfo)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if gantry is not None and plc_time_valid(gantry.timestamp_ns, ex, cur_time)]
    return gantries

#龙门干预结果告警信息,源自云控设置,FMS只取
def get_store_gantry_warning_info(gantry_no:int):
    name = f"{GANTRY_STORE_WARNING_PREFIX}"
    key = f"{gantry_no}"
    return PlcShareCacheClient().hget_proto(name, key, cache_pb2.GantryStoreWarningInfo)

def set_store_gantry_warning_info(gantry_no:int, warning_level:int):
    info = cache_pb2.GantryStoreWarningInfo()
    info.gantry_no = gantry_no
    info.warning_level = warning_level
    info.timestamp_ms = int(time.time() * 1000)
    name = f"{GANTRY_STORE_WARNING_PREFIX}"
    key = f"{gantry_no}"
    return PlcShareCacheClient().hset_proto(name, key, info)

#龙门吊转场封路警告信息
def get_store_gantry_proximity_worning_info(gantry_no:int):
    name = f"{GANTRY_STORE_PROXIMITY_WARNING_PREFIX}"
    key = f"{gantry_no}"
    return PlcShareCacheClient().hget_proto(name, key, cache_pb2.GantryStoreWarningInfo)

def set_store_gantry_proximity_warning_info(gantry_no:int, warning_level:int):
    info = cache_pb2.GantryStoreWarningInfo()
    info.gantry_no = gantry_no
    info.warning_level = warning_level
    info.timestamp_ms = int(time.time() * 1000)
    name = f"{GANTRY_STORE_PROXIMITY_WARNING_PREFIX}"
    key = f"{gantry_no}"
    return PlcShareCacheClient().hset_proto(name, key, info)

def refresh_online_store_gantries(gantry_nos: Iterable[int]):
    return PlcShareCacheClient().update_set(GANTRY_STORE_NO_SET, gantry_nos, int)


def get_online_store_gantry_nos() -> List[int]:
    return PlcShareCacheClient().get_set(GANTRY_STORE_NO_SET, int)

#龙门吊干预服务心跳状态保持
def refresh_store_gantry_server_alive(keep_alive_time_ms = 1000):
    key = f"{GANTRY_STORE_SERVER_STATUS}"
    proto = cache_pb2.GantryStoreServerStatus()
    proto.timestamp_ms = int(time.time() * 1000)
    proto.running = True
    return PlcShareCacheClient().set_proto(key, proto, px=keep_alive_time_ms)

def is_store_gantry_server_alive():
    key = f"{GANTRY_STORE_SERVER_STATUS}"
    proto = PlcShareCacheClient().get_proto(key, cache_pb2.GantryStoreServerStatus)
    if proto and proto.running:
        return True
    else:
        return False


# 甬州桥吊信息通过不同的接口发来，但是共存于一个结构体内，所以需要加锁更新
def acquire_crane_info_lock(crane_no, px=100):
    """尝试获取桥吊信息互斥锁"""
    logger.debug(f'acquire_crane_info_lock, crane_no: {crane_no}')
    count = 0
    while True:
        key = f"{CRANE_INFO_MUTEX_LOCK_PREFIX}:{crane_no}"
        # 尝试在Redis中设置一个带有超时的锁
        # px: expire time ms
        # nx参数为True，表示只有在键不存在的情况下才会执行设置操作。如果键已经存在，则不会执行设置操作，也不会覆盖已存在的值
        lock_acquired = CacheClient().set(key, "locked", px=px, nx=True)

        if lock_acquired:
            return True

        # 如果无法获取锁,等待一段时间后重试,预期内无法获取到锁也直接退出
        logger.debug(f'acquire_crane_info_lock failed, crane_no: {crane_no}, count: {count}, try again')
        time.sleep(0.01)
        count += 1
        if count > 5:
            print(f'acquire_crane_info_lock failed, crane_no: {crane_no}, count: {count}, forced return')
            return False


def release_crane_info_lock(crane_no):
    """"释放桥吊信息互斥锁"""
    key = f"{CRANE_INFO_MUTEX_LOCK_PREFIX}:{crane_no}"
    CacheClient().delete(key)
    logger.debug(f'release_crane_info_lock, crane_no: {crane_no}')


def acquire_gantry_info_lock(gantry_no, px=100):
    """尝试获取轨道吊信息互斥锁"""
    logger.debug(f'acquire_gantry_info_lock, gantry_no: {gantry_no}')
    count = 0
    while True:
        key = f"{GANTRY_INFO_MUTEX_LOCK_PREFIX}:{gantry_no}"
        # 尝试在Redis中设置一个带有超时的锁
        # px: expire time ms
        # nx参数为True，表示只有在键不存在的情况下才会执行设置操作。如果键已经存在，则不会执行设置操作，也不会覆盖已存在的值
        lock_acquired = CacheClient().set(key, "locked", px=px, nx=True)

        if lock_acquired:
            return True

        # 如果无法获取锁,等待一段时间后重试,预期内无法获取到锁也直接退出
        logger.debug(f'acquire_gantry_info_lock failed, gantry_no: {gantry_no}, count: {count}, try again')
        time.sleep(0.01)
        count += 1
        if count > 5:
            print(f'acquire_gantry_info_lock failed, gantry_no: {gantry_no}, count: {count}, forced return')
            return False


def release_gantry_info_lock(gantry_no):
    """"释放轨道吊信息互斥锁"""
    key = f"{GANTRY_INFO_MUTEX_LOCK_PREFIX}:{gantry_no}"
    CacheClient().delete(key)
    logger.debug(f'release_gantry_info_locki, gantry_no: {gantry_no}')


# ---------------------gantry--------------------- #

def set_gantry(gantry_no: int, info: cache_pb2.GantryInfo, ex=None):
    name = f"{GANTRY_PREFIX}"
    key = f"{gantry_no}"
    CacheClient().hset_proto(name, key, info)
    if is_support_plc_share():
        PlcShareCacheClient().hset_proto(name, key, info)


def get_gantry(gantry_no: int, ex=60.0) -> Optional[cache_pb2.GantryInfo]:
    name = f"{GANTRY_PREFIX}"
    key = f"{gantry_no}"
    gantry = PlcCacheClient().hget_proto(name, key, cache_pb2.GantryInfo)
    if gantry is not None and not plc_time_valid(gantry.recv_timestamp, ex):
        gantry = None
    return gantry


def del_gantry(gantry_no: int):
    name = f"{GANTRY_PREFIX}"
    key = f"{gantry_no}"
    print(PlcCacheClient().hdel(name, key))
    print(PlcCacheClient().srem(GANTRY_NO_SET, key))
    return PlcCacheClient().hdel(name, key) and PlcCacheClient().srem(GANTRY_NO_SET, key)


def get_gantries(gantry_nos, ex=60.0) -> List[Optional[cache_pb2.GantryInfo]]:
    name = f"{GANTRY_PREFIX}"
    keys = [f"{gantry_no}" for gantry_no in gantry_nos]
    gantries = PlcCacheClient().hmget_proto(name, keys, cache_pb2.GantryInfo)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if
                gantry is not None and plc_time_valid(gantry.recv_timestamp, ex, cur_time)]
    return gantries


def get_ex_gantries(ex=60.0) -> List[cache_pb2.GantryInfo]:
    name = f"{GANTRY_PREFIX}"
    gantries = PlcCacheClient().hgetvals_proto(name, cache_pb2.GantryInfo)
    cur_time = time.time()
    gantries = [gantry for gantry in gantries if
                gantry is not None and plc_time_valid(gantry.recv_timestamp, ex, cur_time)]
    return gantries


def get_ex_share_gantries(ex=60.0) -> List[cache_pb2.GantryInfo]:
    gantries = []
    name = f"{GANTRY_PREFIX}"
    if is_support_plc_share():
        gantries = PlcShareCacheClient().hgetvals_proto(name, cache_pb2.GantryInfo)
        cur_time = time.time()
        gantries = [gantry for gantry in gantries if
                    gantry is not None and plc_time_valid(gantry.recv_timestamp, ex, cur_time)]
    return gantries


def refresh_online_gantries(gantry_nos: Iterable[int]):
    return CacheClient().update_set(GANTRY_NO_SET, gantry_nos, int)


def get_online_gantry_nos() -> List[int]:
    return CacheClient().get_set(GANTRY_NO_SET, int)


# ---------------------------------------------------------------------#
def get_config_mangetic_crane():
    key = f'{CONFIG_MANGETIC_CRANE_PREFIX}'
    return sorted(PlcCacheClient().get_set(key, int))


def add_config_mangetic_crane(trucks_no: list):
    key = f'{CONFIG_MANGETIC_CRANE_PREFIX}'
    PlcCacheClient().set_add(key, trucks_no)


def del_config_mangetic_crane(trucks_no: list):
    key = f'{CONFIG_MANGETIC_CRANE_PREFIX}'
    PlcCacheClient().set_rem(key, trucks_no)


def clear_config_mangetic_crane():
    key = f'{CONFIG_MANGETIC_CRANE_PREFIX}'
    PlcCacheClient().delete(key)

# ---------------------lock station for yongzhou--------------------- #

def set_lock_station(lock_station_name: str, info: cache_pb2.LockStationInfo, ex=2):
    key = f"{LOCK_STATION_PREFIX}:{lock_station_name}"
    CacheClient().set_proto(key, info, ex)


def get_lock_station(lock_station_name: str) -> Optional[cache_pb2.LockStationInfo]:
    key = f"{LOCK_STATION_PREFIX}:{lock_station_name}"
    gantry = CacheClient().get_proto(key, cache_pb2.LockStationInfo)
    return gantry


def get_all_device_manual_offset():
    key = DEVICE_OFFSET_SET
    offset_dict = CacheClient().get_dict(key)
    result = {}
    for key, value in offset_dict.items():
        try:
            key = key.decode()
            result[key] = float(value)
        except Exception as e:
            result[key] = 0
    return result


def get_device_manual_offset(device_name: str):
    key = DEVICE_OFFSET_SET
    offset = CacheClient().get_field(key, device_name)
    try:
        result = float(offset)
    except Exception as e:
        result = 0
    return result


def set_device_manual_offset(device_name: str, offset: float):
    key = DEVICE_OFFSET_SET
    CacheClient().set_field(key, device_name, str(offset))


if __name__ == '__main__':
    import sys
    usage = "\npython cache/plc_cache.py mask\n\n" + \
      "   1:get_store_gantry_infos [gantry_no]\n\n" + \
      "   2:get_store_gantry [gantry_no,vehicle_name]\n\n" + \
      "   3:get_online_store_gantry_nos\n\n" + \
      "   4:set_store_gantry [gantry_no,vehicle_name,ctrl_mode]\n\n" + \
      "   5:delete_store_gantry [gantry_no]\n\n" + \
      "   6:获取龙门吊干预服务状态(is_store_gantry_server_alive):python cache/plc_cahce.py 6\n\n" + \
      "   7:获取龙门干预结果告警信息(get_store_gantry_warning_info):python cache/plc_cahce.py 7 [gantry_no]\n\n" + \
       "       如获取170龙门吊干预告警:python cache/plc_cahce.py 7 170\n\n" + \
      "   8:设置龙门干预结果告警信息(set_store_gantry_warning_info):python cache/plc_cahce.py 8 [gantry_no] [warning_level]\n\n" + \
      "       如打开170龙门吊干预告警:python cache/plc_cahce.py 8 170 1\n\n" + \
      "       如关闭170龙门吊干预告警:python cache/plc_cahce.py 8 170 10\n\n"

    if len(sys.argv) < 2:
        logger.info(f'{usage}')
        exit(0)

    mask = int(sys.argv[1])
    if mask == 1:
        gantry_no = int(sys.argv[2])
        #ex = 2.0
        ex = None
        print(f"get_store_gantry_infos({gantry_no},{ex}):{get_store_gantry_infos(gantry_no,ex)}")

    if mask == 2:
        gantry_no = int(sys.argv[2])
        vehicle_name = sys.argv[3]
        #ex = 2.0
        ex = None
        print(f"get_store_gantry({gantry_no},{vehicle_name},{ex}):{get_store_gantry(gantry_no,vehicle_name,ex)}")

    if mask == 3:
        print(f"get_online_store_gantry_nos:{get_online_store_gantry_nos()}")

    if mask == 4:
        gantry_no = int(sys.argv[2])
        vehicle_name = sys.argv[3]
        ctrl_mode = int(sys.argv[4])
        info = cache_pb2.GantryStoreInfo()
        info.timestamp_ns = int(time.time() * 1000000000)
        info.timestamp_sec = time.time()
        info.gantry_no = gantry_no
        truck_no_num = re.search(r'\d+', str(vehicle_name))
        info.truck_no = int(truck_no_num.group()) if truck_no_num else 0
        info.ctrl_mode = ctrl_mode
        print(f"set_store_gantry:{MessageToString(info, as_one_line=True)}")
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        set_store_gantry(gantry_no,info,vehicle_name)

    if mask == 5:
        gantry_no = int(sys.argv[2])
        print(f"delete_store_gantry({gantry_no})")
        if not input("Input 'CONFIRM' to continue:") == 'CONFIRM':
            exit()
        print(f"{delete_store_gantry(gantry_no)}")

    if mask == 6:
        print(f"gantry store server alive status:{is_store_gantry_server_alive()}")

    if mask == 7:
        gantry_no = int(sys.argv[2])
        print(f"获取龙门干预结果告警信息:\n{get_store_gantry_warning_info(gantry_no)}")

    if mask == 8:
        gantry_no = int(sys.argv[2])
        warning_level = int(sys.argv[3])
        if not input(f"修改龙门吊{gantry_no}干预告警信息warning_level:{warning_level}.若确认无误,请按'Y'继续:") == 'Y':
            exit()
        print(f"修改龙门吊{gantry_no}干预告警信息warning_level:{warning_level}:{set_store_gantry_warning_info(gantry_no,warning_level)}")
