import time
from typing import List
from itertools import chain
from common.logger import logger
from common.singleton import Singleton
from common.constant import COMMAND_EXPIRE_TIME, COMMAND_LONG_EXPIRE_TIME
from cache.client import <PERSON>ache<PERSON>lient
from proto import cache_pb2, antenna_pb2
from proto.antenna_pb2 import Re<PERSON><PERSON>ommand, CommandStatus, ControlCommandMessage
from proto.cache_pb2 import WorkDeviceInfo,TosTable
from common.common_util import time_valid

COMMAND_WITH_STATUS_KEY_PREFIX = 'command-with-status'
COMMAND_UUIDS_KEY_PREFIX = 'command-uuids'
LAST_STATUS_CHANGED_COMMAND_UUID_KEY_PREFIX = 'last-status-changed-command-uuid'
LAST_COMMAND = "last_command"
CONTROL_COMMAND_MESSAGE_KEY_PREFIX = 'control-command-message-key'
COMMAND_RESET_MODE_PREFIX = "command-reset-mode-key"
COMMAND_WORK_DEVICE_INFO_KEY_PREFIX = 'command-work-device-info'
TEST_PARAM = 'test-param'
COMMAND_TOS_TABLE_PREFIX = 'command-tos-table'
WORKING_LANE_IN_BERTH_PREFIX = 'working_lane_in_berth'
LAST_COMMAND_INFO = "last-command-info"
LAST_FAILED_COMMAND_INFO = "last-failed-command-info"
MOVE_COMMAND_INFO = "move-command-info"


class CommandCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def query_command(self, uuid):
        b = self._redis.get('%s:%s' % (COMMAND_WITH_STATUS_KEY_PREFIX, uuid))
        if b is None:
            return None
        obj = cache_pb2.CommandWithStatus()
        obj.ParseFromString(b)
        return obj

    def _set_command(self, command):
        self._redis.set('%s:%s' % (COMMAND_WITH_STATUS_KEY_PREFIX, command.uuid), command.SerializeToString(), ex = COMMAND_EXPIRE_TIME)

    def query_uuids(self, prefix, vehicle_name):
        res = self._redis.get('%s:%s' % (prefix, vehicle_name))
        if res is None or len(res) == 0:
            return []
        else:
            return res.decode('utf-8').split(',')

    def query_vehicles_uuids(self, prefix, vehicle_names)-> dict:
        if len(vehicle_names) == 0:
            return {}
        keys = []
        for vehicle_name in vehicle_names:
            keys.append(f"{prefix}:{vehicle_name}")
        res = self._redis.mget_str(keys)
        res = list(zip(vehicle_names,res))
        results = {re[0]:re[1].split(',') if re[1] else [] for re in res}
        return results

    def query_uuids_commands(self, uuids):
        if len(uuids) == 0:
            return []
        keys = []
        for uuid in uuids:
            keys.append(f"{COMMAND_WITH_STATUS_KEY_PREFIX}:{uuid}")
        return self._redis.mget_proto(keys, cache_pb2.CommandWithStatus)

    def query_vehicles_commands(self, vehicle_names) -> dict:
        if len(vehicle_names) == 0:
            return {}
        commands = {}
        vehicles_uuids = self.query_vehicles_uuids(COMMAND_UUIDS_KEY_PREFIX, vehicle_names)
        all_uuids = list(chain(*vehicles_uuids.values()))
        commands_list = self.query_uuids_commands(all_uuids)
        commands_dict = {command.uuid:command for command in commands_list}
        for vehicle,uuids in vehicles_uuids.items():
            commands[vehicle] = [commands_dict[uuid] for uuid in uuids]
        return commands

    def query_commands(self, vehicle_name):
        command_uuids = self.query_uuids(COMMAND_UUIDS_KEY_PREFIX, vehicle_name)
        commands = []
        for id in command_uuids:
            if (command:= self.query_command(id)) is not None:
                commands = commands + [command]
        return commands

    def append_commands(self, vehicle_name, commands):
        for command in commands:
            self._set_command(command)
        command_ids = self.query_uuids(COMMAND_UUIDS_KEY_PREFIX, vehicle_name)
        command_ids.extend([command.uuid for command in commands])
        return self._redis.set('%s:%s' % (COMMAND_UUIDS_KEY_PREFIX, vehicle_name), ','.join(command_ids), ex = COMMAND_EXPIRE_TIME)

    def update_command_status(self, vehicle_name, uuid, execute_status):
        command = self.query_command(uuid)
        if command is not None:
            command.status = execute_status
            self._set_command(command)
            self._redis.set('%s:%s' % (LAST_STATUS_CHANGED_COMMAND_UUID_KEY_PREFIX, vehicle_name), uuid, ex = COMMAND_EXPIRE_TIME)
            logger.info(f"vehicle_name:{vehicle_name} update command status "
            f"for uuid:{command.uuid},command type:{RemoteCommand.CommandType.Name(command.command.type)}"
            f",command status:{CommandStatus.ExecuteStatus.Name(command.status)}")
        else:
            logger.warning(f"vehicle_name:{vehicle_name} fail to get command by uuid:{uuid}")

    def get_last_status_changed_command(self, vehicle_name):
        command_ids = self.query_uuids(LAST_STATUS_CHANGED_COMMAND_UUID_KEY_PREFIX, vehicle_name)
        if len(command_ids) < 1:
            return None
        return self.query_command(command_ids[0])

    def clear_last_status_changed_command(self, vehicle_name):
        command_ids = self._redis.delete('%s:%s' % (LAST_STATUS_CHANGED_COMMAND_UUID_KEY_PREFIX, vehicle_name))

    def get_last_command(self, vehicle_name):
        command_ids = self.query_uuids(LAST_COMMAND, vehicle_name)
        if len(command_ids) < 1:
            return None
        return self.query_command(command_ids[0])

    def clear_last_command(self, vehicle_name):
        command_ids = self._redis.delete('%s:%s' % (LAST_COMMAND, vehicle_name))

    def get_current_tos_id(self, vehicle_name):
        tos_id = -1
        commands = self.query_commands(vehicle_name)
        if len(commands) > 0:
            tos_id = commands[0].command.command_context.tos_command_id
        return tos_id

    def get_multi_tos_id(self, vehicle_name):
        multi_tos_id = -1
        commands = self.query_commands(vehicle_name)
        if len(commands) > 0 and len(commands[0].command.mul_command_context) > 1:
            multi_tos_id = commands[0].command.mul_command_context[1].tos_command_id
        return multi_tos_id

    def clear_all_command(self, vehicle_name):
        logger.info(f'vehicle_name:{vehicle_name} clear all command')
        command_ids = self._redis.delete('%s:%s' % (COMMAND_WITH_STATUS_KEY_PREFIX,vehicle_name))
        command_ids = self._redis.delete('%s:%s' % (LAST_STATUS_CHANGED_COMMAND_UUID_KEY_PREFIX,vehicle_name))
        command_ids = self._redis.delete('%s:%s' % (COMMAND_UUIDS_KEY_PREFIX,vehicle_name))
        #command_ids = self._redis.delete('%s:%s' % (LAST_COMMAND, vehicle_name))

    def delete_keys(self,key):
        keys = self.query_keys(key)
        logger.info(f'delete key:{key},num:{len(keys)}')
        for key in keys:
          self._redis.delete(key)
        return len(keys)

    def query_keys(self,key):
        key_pattern = f"{key}*"
        return self._redis.get_pattern_keys(key_pattern)

    def archive_command(self, vehicle_name, uuid):
        command_uuids = self.query_uuids(COMMAND_UUIDS_KEY_PREFIX, vehicle_name)
        if len(command_uuids) == 0 or command_uuids[0] != uuid:
            logger.warning(f'vehicle_name:{vehicle_name} Only the first command can be archived.len(command_uuids):{len(command_uuids)},uuid:{uuid},command_uuids:{command_uuids}')
            return False
        self._redis.set('%s:%s' % (LAST_COMMAND, vehicle_name), command_uuids[0], ex = COMMAND_EXPIRE_TIME)
        command_uuids = command_uuids[1:]
        return self._redis.set('%s:%s' % (COMMAND_UUIDS_KEY_PREFIX, vehicle_name), ','.join(command_uuids), ex = COMMAND_EXPIRE_TIME)

    def cancel_all(self, vehicle_name, plc_event_id = 0):
        commands = self.query_commands(vehicle_name)
        for command in commands:
            logger.info(f"vehicle_name:{vehicle_name} change command type:"
                        f"{RemoteCommand.CommandType.Name(command.command.type)} to cancle command for uuid:{command.uuid}")
            command.command.type = antenna_pb2.RemoteCommand.CANCEL
            command.ecs_event_active_name = ''
            for info in command.tos_command_info:
                #info.plc_event_id = plc_event_id
                pass
            self._set_command(command)

    def reset_all(self, vehicle_name):
        commands = self.query_commands(vehicle_name)
        for command in commands:
            logger.info(f"vehicle_name:{vehicle_name} change command type:"
                        f"{RemoteCommand.CommandType.Name(command.command.type)} to reset command for uuid:{command.uuid}")
            command.command.type = antenna_pb2.RemoteCommand.RESET
            command.ecs_event_active_name = ''
            self._set_command(command)

    def update_command(self, command):
        self._set_command(command)

    def get_command_plc_event_id(self, vehicle_name):
        plc_event_id = 0
        commands = self.query_commands(vehicle_name)
        if len(commands) > 0:
            tos_command_info = commands[0].tos_command_info
            if len(tos_command_info):
                plc_event_id = tos_command_info[0].plc_event_id
        return plc_event_id


    def get_command_work_device_info(self,vehicle_name):
        key = f"{COMMAND_WORK_DEVICE_INFO_KEY_PREFIX}:{vehicle_name}"
        return self._redis.get_proto(key, WorkDeviceInfo)

    def update_command_work_device_info(self,vehicle_name,info:WorkDeviceInfo):
        key = f"{COMMAND_WORK_DEVICE_INFO_KEY_PREFIX}:{vehicle_name}"
        return self._redis.set_proto(key, info)

    def update_command_work_crane_info(self, vehicle_name, new_info):
        info = self.get_command_work_device_info(vehicle_name)
        if info is not None:
            info.crane_info.g_pos = new_info.crane_info.g_pos
            info.crane_info.g_state = new_info.crane_info.g_state
            return self.update_command_work_device_info(vehicle_name, info)
        else:
            logger.warning(f"vehicle_name:{vehicle_name} fail to update work info")
            return False

    def update_command_work_gantry_info(self, vehicle_name, new_info):
        info = self.get_command_work_device_info(vehicle_name)
        if info is not None:
            info.gantry_info.valid= new_info.gantry_info.valid
            info.gantry_info.gantry_no = new_info.gantry_info.gantry_no
            info.gantry_info.g_pos = new_info.gantry_info.g_pos
            info.gantry_info.is_stable = new_info.gantry_info.is_stable
            return self.update_command_work_device_info(vehicle_name, info)
        else:
            logger.warning(f"vehicle_name:{vehicle_name} fail to update work info")
            return False

    def update_command_crane_info(self, vehicle_name, crane_command_info):
        commands = self.query_commands(vehicle_name)
        for command in commands:
            for info in command.tos_command_info:
                info.crane_command_info.g_pos = crane_command_info.g_pos
                info.crane_command_info.g_state = crane_command_info.g_state
            self._set_command(command)

    def update_command_gantry_info(self, vehicle_name, gantry_command_info):
        commands = self.query_commands(vehicle_name)
        for command in commands:
            for info in command.tos_command_info:
                info.gantry_command_info.valid= gantry_command_info.valid
                info.gantry_command_info.gantry_no = gantry_command_info.gantry_no
                info.gantry_command_info.g_pos = gantry_command_info.g_pos
                info.gantry_command_info.is_stable = gantry_command_info.is_stable
            self._set_command(command)

    def push_command_message(self, vehicle_name, events:List):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        return self._redis.lpush_protos(key, events)

    def pop_command_message(self, vehicle_name):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        return self._redis.rpop_proto(key, ControlCommandMessage)

    def get_command_message_length(self,vehicle_name):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        return self._redis.llen(key)

    def get_index_command_message(self,vehicle_name,index):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        return self._redis.lindex_proto(key, index, ControlCommandMessage)

    def pop_all_command_message(self,vehicle_name, auto_delete = False):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        messages = self._redis.lrange_proto(key, 0, -1, ControlCommandMessage)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def delete_all_command_message(self,vehicle_name, delete_length=None):
        key = f"{CONTROL_COMMAND_MESSAGE_KEY_PREFIX}" + f":{vehicle_name}"
        length=self._redis.llen(key)
        self._redis.ltrim(key, (delete_length if delete_length else length), -1)

    def set_command_reset_mode(self, vehicle_name):
        logger.debug(f"set_command_reset_mode, vehicle_name:{vehicle_name}")
        key = f"{COMMAND_RESET_MODE_PREFIX}:{vehicle_name}"
        return self._redis.set(key, 'True', ex=COMMAND_EXPIRE_TIME)

    def del_command_reset_mode(self, vehicle_name):
        logger.debug(f"del_command_reset_mode, vehicle_name:{vehicle_name}")
        key = f"{COMMAND_RESET_MODE_PREFIX}:{vehicle_name}"
        return self._redis.delete(key)

    def get_command_reset_mode(self, vehicle_name) -> bool:
        key = f"{COMMAND_RESET_MODE_PREFIX}:{vehicle_name}"
        value = self._redis.get(key)
        ret = False
        if value is not None:
            ret = (value.decode() == 'True')
        if ret:
            logger.debug(f"get_command_reset_mode, vehicle_name:{vehicle_name}, ret: {ret}")
        return ret

    def get_vehicles_command_reset_mode(self, vehicle_names) -> dict:
        if len(vehicle_names) == 0:
            return {}
        keys = [f"{COMMAND_RESET_MODE_PREFIX}:{vehicle_name}" for vehicle_name in vehicle_names]
        modes = self._redis.mget_str(keys)
        res = list(zip(vehicle_names,modes))
        results = {re[0]:(re[1]=='True') for re in res}
        return results

    def set_test_params(self, test_params):
        proto = cache_pb2.TestParamSet()
        proto.test_params.extend(test_params)
        return self._redis.set_proto(TEST_PARAM, proto)

    def get_test_params(self):
        proto = self._redis.set_proto(TEST_PARAM, cache_pb2.TestParamSet)
        if proto is None:
            return []
        return [m for m in proto.test_params]

    #vehicle_name,id,key,value,没有加锁有风险或分开
    def update_tos_command(self, vehicle_name,update_tos:list):
        logger.info(f"command cache update_tos_command:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        for tos in update_tos:
            vehicle_name = tos.get('vehicle_name')
            id = int(tos.get('id'))
            key_name = tos.get('key_name')
            key_value = tos.get('key_value')
            key = f"{COMMAND_TOS_TABLE_PREFIX}:{vehicle_name}:{id}"
            info = self._redis.get_proto(key, TosTable)
            if info is None:
                info = TosTable()
                info.C_TRUCK_NO = vehicle_name
                info.C_ID = id
                setattr(info, key_name, key_value)
                info.C_UPDATE_TIME = int(time.time() * 1000000000)
                info.C_INSERT_TIME = info.C_UPDATE_TIME
            else:
                setattr(info, key_name, key_value)
                info.C_UPDATE_TIME = int(time.time() * 1000000000)
            if not self._redis.set_proto(key, info, ex = COMMAND_LONG_EXPIRE_TIME):
                logger.warning(f"update_command_tos fail:vehicle_name:{vehicle_name},id:{id},key_name:{key_name},key_value:{key_value}")
                return False
        return True

    def get_tos_command(self, vehicle_name, id):
        key = f"{COMMAND_TOS_TABLE_PREFIX}:{vehicle_name}:{id}"
        return self._redis.get_proto(key, TosTable)

    def set_working_lane_in_berth(self, vehicle_name, work_lane):
        """
            @brief: 设置车辆的泊位作业车道
        """
        key = f"{WORKING_LANE_IN_BERTH_PREFIX}:{vehicle_name}"
        return self._redis.set(key, work_lane)

    def get_working_lane_in_berth(self, vehicle_name):
        """
            @brief: 泊位作业车道，当车辆处于箱区作业时该值表示上一次的泊位作业车道
            @ret: -1 表示异常值
        """
        key = f"{WORKING_LANE_IN_BERTH_PREFIX}:{vehicle_name}"
        value = self._redis.get(key)
        if value is not None:
            return int(value.decode())
        else:
            return -1

    def set_last_success_command_info(self, vehicle_name, info:cache_pb2.CommandExtInfo):
        key = f"{LAST_COMMAND_INFO}:{vehicle_name}"
        info.timestamp_ms = int(time.time() * 1000)
        return self._redis.set_proto(key, info, ex=COMMAND_LONG_EXPIRE_TIME)

    def get_last_success_command_info(self, vehicle_name,ex=20*60):
        key = f"{LAST_COMMAND_INFO}:{vehicle_name}"
        info = self._redis.get_proto(key, cache_pb2.CommandExtInfo)
        if info and ex is not None and not time_valid(info.timestamp_ms * 1000000, ex=ex, cur_time=time.time()):
            info = None
        return info
    
    def set_last_failed_command_info(self, vehicle_name, info:cache_pb2.CommandExtInfo):
        key = f"{LAST_FAILED_COMMAND_INFO}:{vehicle_name}"
        info.timestamp_ms = int(time.time() * 1000)
        return self._redis.set_proto(key, info, ex=COMMAND_LONG_EXPIRE_TIME)

    def get_last_failed_command_info(self, vehicle_name,ex=20*60):
        key = f"{LAST_FAILED_COMMAND_INFO}:{vehicle_name}"
        info = self._redis.get_proto(key, cache_pb2.CommandExtInfo)
        if info and ex is not None and not time_valid(info.timestamp_ms * 1000000, ex=ex, cur_time=time.time()):
            info = None
        return info

    def get_move_command_info(self, vehicle_name):
        key = f"{MOVE_COMMAND_INFO}:{vehicle_name}"
        info = self._redis.get_proto(key, cache_pb2.MoveCommandWithStatus)
        return info
    
    def set_move_command_info(self, vehicle_name, info:cache_pb2.MoveCommandWithStatus):
        key = f"{MOVE_COMMAND_INFO}:{vehicle_name}"
        info.timestamp_ms = int(time.time() * 1000)
        return self._redis.set_proto(key, info, ex = COMMAND_LONG_EXPIRE_TIME)


if __name__ == '__main__':
    # CommandCache().set_command_reset_mode('AT801')
    # print(CommandCache().get_command_reset_mode('AT801'))
    # CommandCache().del_command_reset_mode('AT801')
    # print(CommandCache().get_command_reset_mode('AT801'))
    vehicle_name = 'AT800'
    id = 1
    key = 'C_TRUCK_POS'
    #value = str(int(time.time()))
    value = 'F'
    update_tos = [{'vehicle_name':vehicle_name,'id':id,'key':key,'value':value}]
    CommandCache().update_tos_command(update_tos)
    info = CommandCache().get_tos_command(vehicle_name,id)
    print(f"{info}")

    from google.protobuf import json_format
    info = json_format.MessageToDict(info,preserving_proto_field_name=True)
    print(type(info['C_ID']))
    '''
    while True:
        CommandCache().get_command_reset_mode('AT801')
        logger.info(f"command size:{len(CommandCache().query_commands('AT801'))}")
        logger.info(f"--------------------")
        time.sleep(0.1)
    '''

