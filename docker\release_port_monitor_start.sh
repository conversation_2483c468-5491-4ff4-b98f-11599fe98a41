#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

#$DOCKER_PATH/db_start.sh
source $DOCKER_PATH/common.sh

function local_volumes() {
  volumes="
           -v $HOME/.ssh:${DOCKER_HOME}/.ssh \
           -v $HOME/.zsh_history:${DOCKER_HOME}/.zsh_history \
           -v /tmp/core:/tmp/core \
           -v /proc/sys/kernel/core_pattern:/tmp/core_pattern:rw \
           -v /media:/media \
           -v /private:/private \
           -v /onboard_data:/onboard_data \
	   -v /etc/localtime:/etc/localtime:ro \
           -v /tmp/.ssh-agent-$USER:/tmp/.ssh-agent-$USER"

  volumes="${volumes} -v ${LOCAL_DIR}:/antenna-server"

  echo "${volumes}"
}

#disable pull antenna server image from comany
#docker pull $IMG

docker ps -a --format "{{.Names}}" | grep "${DOCKER_NAME}" 1>/dev/null
if [ $? == 0 ]; then
docker stop ${DOCKER_NAME} 1>/dev/null
docker rm -f ${DOCKER_NAME} 1>/dev/null
fi

mkdir -p /tmp/.ssh-agent-$USER 2>&1 >/dev/null

USER_ID=$(id -u)
GRP=$(id -g -n)
GRP_ID=$(id -g)
LOCAL_HOST=$(hostname)
DOCKER_HOME="/home/<USER>"
if [ "$USER" == "root" ]; then
  DOCKER_HOME="/root"
fi

#delete ANTENNA_DB_IP and ANTENNA_DB_SID
eval docker create -it \
    --name ${DOCKER_NAME} \
    -e DOCKER_USER=$USER \
    -e USER=$USER \
    -e DOCKER_USER_ID=$USER_ID \
    -e DOCKER_GRP=$GRP \
    -e DOCKER_GRP_ID=$GRP_ID \
    -e DOCKER_HOME=$DOCKER_HOME \
    -e DOCKER_NAME=$DOCKER_NAME \
    -e DOCKER_HOST_PATH=$(pwd) \
    -e PYTHONPATH=. \
    -e SSH_AUTH_SOCK=/tmp/.ssh-agent-$USER/agent.sock \
    $(local_volumes) \
    --ulimit core=-1 \
    -w /antenna-server \
    --dns=*************** \
    --add-host in_antenna_dev_docker:127.0.0.1 \
    --add-host ${LOCAL_HOST}:127.0.0.1 \
    --hostname in_antenna_dev_docker \
    -v ${DOCKER_PATH}/entrypoint.sh:/tmp/entrypoint.sh \
    --entrypoint /tmp/entrypoint.sh \
    --cap-add=SYS_PTRACE \
    $IMG /bin/bash

docker cp -L ~/.gitconfig ${DOCKER_NAME}:${DOCKER_HOME}/.gitconfig
docker start ${DOCKER_NAME}
docker container update --restart=always ${DOCKER_NAME}
docker exec ${DOCKER_NAME} chown ${USER_ID}:${GRP_ID} /antenna-server
