from typing import Optional

from cache.client import <PERSON>ache<PERSON>lient
from proto import cache_pb2
from common.constant import NB_FABU_SERVER_01,NB_ANTENNA_SERVER_01,NB_BACK_WEB_SERVER_01,NB_BACK_WEB_SERVER_01_MAINTAINER_NUMBER,NB_FABU_SERVER_01_MAINTAINER_NUMBER
from common.logger import logger

MONITOR_ALARM_MAINTAINER_INFO = 'monitor-alarm-maintainer-info'
ENABLE_MONITOR_ALARM = 'enable-monitor-alarm'
AUTO_TIME_RESTART_CONFIG = 'auto-time-restart-config'


def enable_monitor_alarm():
    return CacheClient().client().set(ENABLE_MONITOR_ALARM, 1)


def disable_monitor_alarm():
    return CacheClient().client().delete(ENABLE_MONITOR_ALARM)


def if_enable_monitor_alarm():
    return CacheClient().client().get(ENABLE_MONITOR_ALARM) is not None


def set_monitor_alarm_maintainer_info(server_name, name, phone):
    proto = cache_pb2.MonitorAlarmMainterInfo()
    proto.name = name
    proto.phone = phone
    logger.info('set server_name:%s,maintainer info:%s' % (server_name, str(proto)))
    return CacheClient().set_proto(f'{MONITOR_ALARM_MAINTAINER_INFO}:{server_name}', proto)


def get_monitor_alarm_maintainer_info(server_name):
    return CacheClient().get_proto(f'{MONITOR_ALARM_MAINTAINER_INFO}:{server_name}', cache_pb2.MonitorAlarmMainterInfo)


def set_auto_time_restart_config(config:cache_pb2.AutoTimeRestartConfig):
    key = f"{AUTO_TIME_RESTART_CONFIG}"
    return CacheClient().set_proto(key, config)

def get_auto_time_restart_config():
    key = f"{AUTO_TIME_RESTART_CONFIG}"
    return CacheClient().get_proto(key, cache_pb2.AutoTimeRestartConfig)

if __name__ == '__main__':
    set_monitor_alarm_maintainer_info(NB_FABU_SERVER_01, 'lizhicheng', NB_FABU_SERVER_01_MAINTAINER_NUMBER)
    set_monitor_alarm_maintainer_info(NB_ANTENNA_SERVER_01, 'lizhicheng', NB_FABU_SERVER_01_MAINTAINER_NUMBER)
    set_monitor_alarm_maintainer_info(NB_BACK_WEB_SERVER_01, 'mengdejia',NB_BACK_WEB_SERVER_01_MAINTAINER_NUMBER)
    server_name = NB_FABU_SERVER_01
    get = get_monitor_alarm_maintainer_info(server_name)
    print(f'server name:{server_name},maintainer_info:{get}')
    server_name = NB_ANTENNA_SERVER_01
    get = get_monitor_alarm_maintainer_info(server_name)
    print(f'server name:{server_name},maintainer_info:{get}')
    server_name = NB_BACK_WEB_SERVER_01
    get = get_monitor_alarm_maintainer_info(server_name)
    print(f'server name:{server_name},maintainer_info:{get}')
