#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 开发模式仅限使用dev,线上环境可选fat/pre/pro
ENVIRONMENT='dev'
# antenna-db镜像
DB_IMG=docker.fabu.ai:5000/antenna-db:20230215_1542
#MYSQL_DB_IMG=mysql:5.7.37
MYSQL_DB_IMG=docker.fabu.ai:5000/mysql:8.0.21

HOST_NET_MODE_ENABLE='false'
export HOST_NET_MODE_ENABLE

# 仿真专用
# 仿真模式
SIM_MODE='false'
# 南通轨道吊对外端口映射配置, 对内映射固定2702
NT_PLC_GANTRY_PORT=2702
# ssh对外端口映射配置, 对内映射固定为2222
SSH_PORT=2222

# 公用配置
# 自定义容器名后缀
DOCKER_NAME_CUSTOM_SUFFIX=''
# antenna-server镜像
IMG=docker.fabu.ai:5000/antenna-server/antenna-server:20250422_1908
# rpc对外端口映射配置, 对内映射固定6789
DISPATCH_ADDRESS_PORT=6789
# 快速rpc对外端口映射配置, 对内映射固定6788
FAST_DISPATCH_ADDRESS_PORT=6788
# 测试端口, 对内映射固定6000
DEBUG_FAST_DISPATCH_ADDRESS_PORT=6000
# 桥吊信息对外端口映射配置, 对内映射固定2000
PLC_CRANE_PORT=2000
# 龙门吊信息对外端口映射配置, 对内映射固定1234
PLC_GANTRY_PORT=1234
# 换电站信息对外端口映射配置, 对内映射固定2090
CHANGE_PORT=2090
# 老云控对外端口映射配置, 对内映射固定6790
WEB_PORT=6790
# 内部redis端口,host-net时在同一个台主机上部署时要区分端口
LOCAL_REDIS_PORT=6379
# 是否启动ORACLE，梅山南通场景开发模式需要使用，非开发模式会默认重置为false
START_ORACLE_DB='true'
if [ $ENVIRONMENT != 'dev' ] ; then
  START_ORACLE_DB='false'
fi
# oracle端口对外端口映射配置, 对内映射固定1521
ANTENNA_DB_PORT=1521
# 是否启动MYSQL，万象\甬舟场景需要使用
START_MYSQL_DB='true'
# mysql端口对外端口映射配置, 对内映射固定13306
ANTENNA_MYSQL_DB_PORT=13306

export ENVIRONMENT
export SIM_MODE

DOCKER_NAME_PREFIX=${USER}'-'`echo ${HOSTNAME%%.*}`
if [ -z $DOCKER_NAME ]; then
  DOCKER_NAME="${DOCKER_NAME_PREFIX}_antenna_server_${ENVIRONMENT}${DOCKER_NAME_CUSTOM_SUFFIX}"
fi
export DOCKER_NAME

if [ -z $DB_DOCKER_NAME ]; then
  DB_DOCKER_NAME="${DOCKER_NAME_PREFIX}_antenna_db${DOCKER_NAME_CUSTOM_SUFFIX}"
fi
export DB_DOCKER_NAME

if [ -z $MYSQL_DB_DOCKER_NAME ]; then
  MYSQL_DB_DOCKER_NAME="${DOCKER_NAME_PREFIX}_antenna_mysql_db${DOCKER_NAME_CUSTOM_SUFFIX}"
fi
export MYSQL_DB_DOCKER_NAME
