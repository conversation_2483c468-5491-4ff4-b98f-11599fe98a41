from typing import List

import utm

from cache.client import CacheClient
from proto.fabupilot.obstacle_v2_pb2 import SensorObstacles
from proto.antenna_pb2 import RoadsidePerceptionBlocks
from common.logger import logger

ROADSIDE_PERCEPTION_UNITS = 'roadside-perception-units'
ROADSIDE_PERCEPTION_RESULT = 'roadside-perception-result'
ROADSIDE_PERCEPTION_BLOCKS = 'roadside-perception-blocks'

def utm_to_latlng(easting, northing, zone_number=51, zone_letter='R'):
    # meishan -> zone_number=51, zone_letter='R'
    return utm.to_latlon(easting, northing, zone_number, zone_letter)


def update_rspu_loc(rspu_name, utm_x, utm_y):
    lat, lon = utm_to_latlng(utm_x, utm_y)
    CacheClient().client().geoadd(ROADSIDE_PERCEPTION_UNITS, lon, lat, rspu_name)


def delete_rspu(repu_no):
    CacheClient().client().zrem(ROADSIDE_PERCEPTION_UNITS, repu_no)


def search_rspu_by_radius(utm_x, utm_y, radius) -> List:
    lat, lon = utm_to_latlng(utm_x, utm_y)
    rspus = []
    bs = CacheClient().client().georadius(ROADSIDE_PERCEPTION_UNITS, lon, lat, radius, "m")
    for b in bs:
        rspus.append(str(b.decode('utf-8')))
    return rspus


def update_roadside_perception_obstacles(rspu_name, obstacles: SensorObstacles):
    key = f"{ROADSIDE_PERCEPTION_RESULT}:{rspu_name}"
    CacheClient().set_proto(key, obstacles, ex=60)


def update_roadside_perception_blocks(rspu_name, blocks: RoadsidePerceptionBlocks):
    key = f"{ROADSIDE_PERCEPTION_BLOCKS}:{rspu_name}"
    CacheClient().set_proto(key, blocks, ex=60)

def get_roadside_perception_blocks()->List[RoadsidePerceptionBlocks]:
    key_pattern =  f"{ROADSIDE_PERCEPTION_BLOCKS}:*"
    keys = CacheClient().get_pattern_keys(key_pattern)
    logger.debug(f'get_roadside_perception_blocks:key_pattern:{key_pattern},keys:{keys}')
    if len(keys) > 0:
        blocks = CacheClient().mget_proto(keys, RoadsidePerceptionBlocks)
        return blocks
    return []


def mget_obstacles(rspus) -> List[SensorObstacles]:
    if not rspus:
        return []
    keys = [f"{ROADSIDE_PERCEPTION_RESULT}:{rspu_name}" for rspu_name in rspus]
    obstacles = CacheClient().mget_proto(keys, SensorObstacles)
    return obstacles


if __name__ == '__main__':
    import sys
    if len(sys.argv) < 3:
        print('0x1:show obstacles')
        print('   0x1 [name1] [name2] ...')
        print('0x2:test')
        print('0x2 0x1')
        exit(1)

    mask = 0
    if sys.argv[1].startswith('0x'):
        mask = int(sys.argv[1], 16)
    else:
        mask = int(sys.argv[1])

    if mask & 0x1:
        rspus = sys.argv[2:]
        print(f"input rspus name:{rspus}")
        obs = mget_obstacles(rspus)
        i = 0
        print(f'###################totoal obs:{len(obs)}#########################')
        for ob in obs:
            i = i + 1
            print(f"{len(obs)}-{i}:{ob}")

    if mask & 0x2:
        update_rspu_loc("p1", 404315, 3294083)
        update_rspu_loc("p2", 404325, 3294093)
        a = search_rspu_by_radius(404281, 3295188, 100)
        print(a)


