# 甬舟 vs 梅山车辆模式切换功能对比分析

## 概述

本文档详细对比了甬舟场景和梅山场景在车辆生产/测试模式切换功能上的实现差异，分析各自的特点和适用场景。

## 核心差异对比

### 1. 指令复制机制

#### 甬舟场景 - 完整实现
```python
def on_vehicle_operation_mode_changed(self, vehicle_name, old_mode, new_mode):
    if new_mode == VehicleOperationMode.TEST:
        commands = self._command_cache.query_commands(vehicle_name)
        self.insert_remote_command(vehicle_name, cancel_command)
        
        if len(commands) > 0:
            cur_cmd = commands[0]
            # 复杂的指令类型映射
            wi_type_map = {
                RemoteCommand.CommandContext.WiType.WI_LOAD: "LOAD",
                RemoteCommand.CommandContext.WiType.WI_DSCH: "DSCH",
                RemoteCommand.CommandContext.WiType.WI_YARD: "YARD",
            }
            # 构造新的TOS指令并重新下发
            tos_command = {...}  # 复杂的指令构造逻辑
            ret = insert_tos_command_dict(vehicle_name, tos_command)
```

**特点**:
- 完整的指令复制和重构逻辑
- 支持多种指令类型的转换
- 自动重新下发模拟指令

#### 梅山场景 - 简化实现
```python
def on_vehicle_operation_mode_changed(self, vehicle_name, old_mode, new_mode):
    if new_mode == VehicleOperationMode.TEST:
        commands = self._command_cache.query_commands(vehicle_name)
        self.insert_remote_command(vehicle_name, cancel_command)
        
        if len(commands) > 0:
            # 简化的指令复制逻辑
            logger.info(f"复制作业指令为模拟指令")
            # 主要记录日志，不进行复杂重构
```

**特点**:
- 简化的指令处理逻辑
- 主要用于日志记录和监控
- 适合梅山场景的业务需求

### 2. 数据库层指令过滤

#### 甬舟场景 - 数据库字段支持
```python
def query_execute(truck_no, operation_mode, time_delta=2):
    if operation_mode == "TEST":
        sql = sql.filter(TosWisInfo.TOS_SOURCE.in_(["TEST", "T"]))
    elif operation_mode == "PROD":
        sql = sql.filter(
            or_(
                TosWisInfo.TOS_SOURCE.is_(None),
                TosWisInfo.TOS_SOURCE.notin_(["TEST", "T"])
            )
        )
```

**特点**:
- 数据库有专门的TOS_SOURCE字段
- 在SQL查询层面直接过滤
- 性能高效，过滤准确

#### 梅山场景 - 多策略过滤
```python
def filter_test_mode_wis(self, truck_no, wis):
    filtered_wis = []
    for wi in wis:
        # 多种过滤策略
        if wi.get('TOS_SOURCE') in ['TEST', 'T', 'SIMULATION']:
            filtered_wis.append(wi)
        elif wi.get('REMARK') and 'TEST' in str(wi.get('REMARK')).upper():
            filtered_wis.append(wi)
        else:
            # 兼容策略
            filtered_wis.append(wi)
```

**特点**:
- 支持多种数据库字段配置
- 应用层过滤，灵活性高
- 向后兼容现有系统

### 3. 车辆状态管理

#### 甬舟场景 - 复杂状态流转
- 支持锁站交互
- 详细的状态流转管理
- 复杂的车辆信息更新逻辑

#### 梅山场景 - 基础状态管理
- 简化的状态管理
- 不涉及锁站交互
- 适合梅山港口的实际需求

## 技术架构对比

### 甬舟场景架构特点
1. **高复杂度**: 支持强控模式和单车模式切换
2. **完整功能**: 指令复制、状态管理、锁站交互一应俱全
3. **数据库支持**: 专门的字段支持模式区分
4. **适用场景**: 复杂的港口作业环境

### 梅山场景架构特点
1. **简化设计**: 统一单车模式，简化业务逻辑
2. **兼容性强**: 支持多种数据库配置，向后兼容
3. **易维护**: 代码简洁，易于理解和维护
4. **适用场景**: 相对简单的港口环境

## 实现策略选择

### 何时选择甬舟模式
- 港口作业复杂，需要强控和单车模式切换
- 有专门的数据库字段支持
- 需要完整的指令复制和重构功能
- 涉及锁站等复杂设备交互

### 何时选择梅山模式
- 港口作业相对简单，统一单车模式即可
- 数据库字段配置灵活，需要兼容多种情况
- 追求代码简洁和易维护性
- 不涉及复杂的设备交互

## 迁移建议

### 从梅山到甬舟
1. 添加数据库TOS_SOURCE字段
2. 实现完整的指令复制逻辑
3. 增加锁站交互功能
4. 完善车辆状态管理

### 从甬舟到梅山
1. 简化指令复制逻辑
2. 实现多策略数据库过滤
3. 移除锁站相关代码
4. 简化车辆状态管理

## 性能对比

| 指标 | 甬舟场景 | 梅山场景 |
|------|---------|---------|
| 数据库查询性能 | 高（SQL层过滤） | 中（应用层过滤） |
| 内存使用 | 高（复杂对象） | 低（简化对象） |
| 代码复杂度 | 高 | 低 |
| 维护成本 | 高 | 低 |
| 功能完整性 | 完整 | 基础 |

## 总结

甬舟和梅山两种实现方式各有优势：

- **甬舟场景**适合复杂的港口环境，功能完整但复杂度高
- **梅山场景**适合相对简单的港口环境，代码简洁易维护

选择哪种实现方式应该根据具体的业务需求、港口复杂度和维护能力来决定。对于大多数港口场景，梅山的简化实现已经能够满足基本需求，而对于特别复杂的港口环境，甬舟的完整实现提供了更强大的功能支持。
