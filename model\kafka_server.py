import os

import csv
import time
import json
import traceback
from kafka import KafkaProducer
from kafka import KafkaConsumer
from kafka import KafkaAdminClient
from kafka import TopicPartition
from kafka.admin import NewPartitions
import hashlib
from common.logger import logger
from common.constant import KAFKA_GROUP_ID


def hash_partitioner(key, all_partitions, available):
    """
    Customer Kafka partitioner to get the partition corresponding to key
    :param key: partitioning key
    :param all_partitions: list of all partitions sorted by partition ID
    :param available: list of available partitions in no particular order
    :return: one of the values from all_partitions or available
    """

    if key is None:
        if available:
            return random.choice(available)
        return random.choice(all_partitions)

    idx = int(hashlib.sha1(key).hexdigest(), 16) % (10 ** 8)
    idx &= 0x7fffffff
    idx %= len(all_partitions)
    logger.debug(f'has_partitioner:key:{key},idx:{idx}, get partition:{all_partitions[idx]},len(all_partitions):{len(all_partitions)}')
    return all_partitions[idx]


    
class KafkaMsgPartitioner(object):
    def __init__(self, servers:list, topic):
        self._servers = servers
        self._topic = topic

    def set_partitioner(self,partitioner_count):
        if partitioner_count <=0:
            #logger.info(f'no need set topic:{self._topic} partitioner count:{partitioner_count}')
            return True

        try:
            producer = KafkaProducer(bootstrap_servers=self._servers)
            result = producer.partitions_for(self._topic)
            #logger.info(f'get partitions result:{result}')
            producer.close()
            if len(result) == partitioner_count:
                #logger.info(f'topic:{self._topic} partitioner count is already {partitioner_count}')
                pass
            else:
                admin_client = KafkaAdminClient(bootstrap_servers=self._servers)
                topic_partitions = {}
                topic_partitions[self._topic] = NewPartitions(total_count=partitioner_count)
                admin_client.create_partitions(topic_partitions)
                admin_client.close()
            return True
        except Exception as e:
            logger.warning(f"set partitioner  err:{e}, trace:{traceback.format_exc()}")
            return False

def init_sasl_config(sasl_config:dict):
    if sasl_config.get('sasl_enable',False):
        sasl_plain_username = sasl_config.get('sasl_plain_username','')
        sasl_plain_password = sasl_config.get('sasl_plain_password','')
        security_protocol = sasl_config.get('security_protocol','')
        sasl_mechanism = sasl_config.get('sasl_mechanism','')
        if sasl_plain_username and sasl_plain_password \
              and security_protocol and sasl_mechanism:
            del sasl_config['sasl_enable']
            return sasl_config
        logger.warning(f"[__init_sasl_config] invalid sasl config,sasl_plain_username:{sasl_plain_username}"
        f",sasl_plain_password:{sasl_plain_password},security_protocol:{security_protocol},sasl_mechanism:{sasl_mechanism}")
    return {}

class KafkaMsgProducer(object):
    def __init__(self, servers:list, sasl_config={}):
        self._servers = servers
        self.producer = None
        self.is_connected = False
        self._sasl_config = init_sasl_config(sasl_config)
 
    def connect(self):
        if self.producer is None:
            try:
                #producer = KafkaProducer(value_serializer=lambda v: json.dumps(v).encode('utf-8'),bootstrap_servers=self._servers)
                #max_block_ms自动重连时间，默认为60s
                #retries如果发生错误或者连接断开，可以自动重连并重新发送数据。可以通过设置retries参数来控制重试次数，以确保数据被成功发送.默认为0
                #acks:默认为1，确认模式。确保是否成功生产.若为1生产者等待来自服务器的领导者（leader）节点的确认，仍有可能会丢失
                #acks:acks=all 或 acks=-1：生产者等待来自服务器的所有节点确认，但消息的可靠性最高，但生产者的吞吐量较低
                #api_version=(0, 10, 2) 接口版本信息
                self.producer = KafkaProducer(bootstrap_servers=self._servers, partitioner=hash_partitioner,**self._sasl_config)
                self.is_connected = True
                return True
            except Exception as e:
                logger.warning(f"KafkaMsgProducer connect err{e}, trace:{traceback.format_exc()}")
                return False
        return True
 
    def close(self):
        if self.producer is not None:
            self.producer.close()
            self.producer = None
            self.is_connected = False
 
    def send(self, topic, msg, key=None):
        if self.producer is not None and self.is_connected:
            if not isinstance(msg, bytes):
                logger.warning(f'kafka producer send topic:{topic},key:{key},msg format error')
                pass
            logger.debug(f'kafka producer send topic:{topic},key:{key},len(msg):{len(msg)},msg[:1024]:{msg[:1024]}')
            self.producer.send(topic=topic, value=msg, key=key)
        else:
            logger.warning(f'kafka producer topic:{self.topic} is None or not connected:{self.is_connected}')

    def asyn_send_flush(self, topic, msg, key=None):
        if self.producer is not None and self.is_connected:
            logger.debug(f'kafka producer send topic:{topic},key:{key},msg:{msg}')
            self.producer.send(topic=topic, value=msg, key=key)
            self.producer.flush() #异步,也可以批量提交
        else:
            logger.warning(f'kafka producer topic:{self.topic} is None or not connected:{self.is_connected}')

    def asyn_send_callback(self, topic, msg, key=None):
        if self.producer is not None and self.is_connected:
            logger.debug(f'kafka producer send topic:{topic},key:{key},msg:{msg}')
            self.producer.send(topic=topic, value=msg, key=key).add_callback(self.send_success).add_errback(self.send_error)
            self.producer.flush() #异步
        else:
            logger.warning(f'kafka producer topic:{self.topic} is None or not connected:{self.is_connected}')

    def syn_send(self, topic, msg, key=None):
        if self.producer is not None and self.is_connected:
            logger.debug(f'kafka producer send topic:{topic},key:{key},msg:{msg}')
            future = self.producer.send(topic=topic, value=msg, key=key)
            future.get(timeout=10)# 同步确认消费future
        else:
            logger.warning(f'kafka producer topic:{self.topic} is None or not connected:{self.is_connected}')

    def send_success(self, *args, **kwargs):
        """异步发送成功回调函数"""
        logger.debug(f"send success")
        return

    def send_error(self, *args, **kwargs):
        """异步发送错误回调函数"""
        logger.warning(f"send error")
        return

class KafkaMsgConsumer(object):
    def __init__(self, servers:list, topic = None, topics=[], group_id = KAFKA_GROUP_ID, sasl_config={}):
        self._servers = servers
        self._topic = topic
        self._topics = topics
        self.consumer = None
        self.is_connected = False
        self._group_id = group_id
        self._sasl_config = init_sasl_config(sasl_config)

    def connect(self):
        if self.consumer is None:
            try:
                if self._topic:
                    #session_timeout_ms:重连时间默认为10s
                    #fetch_max_wait_ms:消费者从Kafka服务器获取数据时的最大等待时间。默认500ms,如果数据量较大设的较小可能需要多次获取才能获取到完整的数据
                    #从而增加了网络流量和服务器负载,如果数据量较大可以设置大点
                    #api_version=(0, 10, 2)接口版本
                    #auto_offset_reset='earliest'
                    #consumer_timeout_ms: 消费者在 poll() 方法中等待消息的最大时间（毫秒）
                    self.consumer = KafkaConsumer(self._topic, group_id=self._group_id,auto_offset_reset='latest',bootstrap_servers=self._servers,**self._sasl_config)
                else:
                    self.consumer = KafkaConsumer(group_id=self._group_id,auto_offset_reset='latest',bootstrap_servers=self._servers,**self._sasl_config)
                    self.consumer.subscribe(self._topics)
                self.consumer.poll(1000)
                self.consumer.seek_to_end()
                self.is_connected = True
                return True
            except Exception as e:
                logger.warning(f"KafkaMsgConsumer connect err{e}, trace:{traceback.format_exc()}")
                return False
        return True

    def close(self):
        if self.consumer is not None:
            self.consumer.close()
            self.consumer = None
            self.is_connected = False
 
    def receive(self,timeout_ms=10*1000):
        if self.consumer is not None and self.is_connected:
            message = self.consumer.poll(timeout_ms=timeout_ms)
            return message
        else:
            logger.warning(f'kafka consumer topic:{self._topic} is None or not connected:{self.is_connected}')
        return None

    def seek_by_time(self,topics_offset_ms:dict = {}):
        ret = True
        if self.consumer is not None and self.is_connected:
            # 获取每个分区距离指定时间最近的偏移量
            for topic, target_time_ms in topics_offset_ms.items():
                offsets = {}
                for partition in self.consumer.partitions_for_topic(topic):
                    tp = TopicPartition(topic, partition)
                    if (offset:= self.consumer.offsets_for_times({tp: target_time_ms})[tp]) is not None:
                        offsets[tp] = offset
                        print(f"WARNING:set offset:topic:{topic},tp:{tp},offset:{offset},by target_time_ms:{target_time_ms}")
                # 将消费者的偏移量设置为计算出的偏移量
                for tp, offset in offsets.items():
                    self.consumer.seek(tp, offset.offset)
        else:
            logger.warning(f'kafka consumer topic:{self._topic} is None or not connected:{self.is_connected}')
            ret = False
        return ret

def producer_run():
    sasl_config = {
    'sasl_plain_username': 'zzt',
    'sasl_plain_password': 'zzt123',
    'security_protocol': 'SASL_PLAINTEXT',
    'sasl_mechanism': 'SCRAM-SHA-256'
    }
    producer = KafkaMsgProducer(["localhost:9092"])
    producer.connect()
    topic = "self-test"
 
    print("Start sending msg to kafka!")
 
    for msg in "Hello! This is YanChampion speaking!".split():
        producer.send(topic=topic, msg=msg)   # 向kafka 指定topic发送数据
        time.sleep(1)



def consumer_run():
    sasl_config = {
    'sasl_plain_username': 'zzt',
    'sasl_plain_password': 'zzt123',
    'security_protocol': 'SASL_PLAINTEXT',
    'sasl_mechanism': 'SCRAM-SHA-256'
    }
    topic = "self-test"
    consumer = KafkaMsgConsumer(["localhost:9092"], topic)
    consumer.connect() 
    while True:
      message = consumer.receive(1000)
      if message:
          #message.topic,message.partition,message.offset, message.key,message.value
          logger.info(f'kafka consumer receive topic:{topic},message:{message}')
      time.sleep(1)
 
 
if __name__ == '__main__':
    producer_run()    # 运行发布消息程序
