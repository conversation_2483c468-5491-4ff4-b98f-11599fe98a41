import os

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from common.logger import logger
from common.singleton import Singleton
from config.config_manage import ConfigManage

class TosSession(metaclass=Singleton):
    def __init__(self):
        mysql_dsn = "mysql://root:fabu124@172.30.1.10:3306/nb_port_prod?charset=utf8"
        if ConfigManage().is_local_oracle():
            if ConfigManage().is_dev_env():
                mysql_dsn = "mysql://read_write_user:OD3vd4v992GfppR9@192.168.3.110:3306/nb_port_test?charset=utf8"
                # mysql_dsn = "mysql://root:root@192.168.3.110:3306/nb_port_temp1?charset=utf8"  # for iecs test 2022.08.30
            else:#release_test
                mysql_dsn = "mysql://root:fabu124@172.30.1.10:3306/nb_port_test?charset=utf8"
        logger.info('mysql database dsn: {} '.format(mysql_dsn))
        mysql_engine = create_engine(mysql_dsn, pool_size=30, pool_recycle=10800, pool_pre_ping=True, max_identifier_length=30, echo=False)
        self.mysql_session = sessionmaker(bind=mysql_engine)

    def acquire(self):
        return self.mysql_session()

# def init_pool():
#     # close all the old connection in new processr
#     # https://docs.sqlalchemy.org/en/14/core/pooling.html#using-connection-pools-with-multiprocessing-or-os-fork
#     gis_engine.dispose()
#     tos_engine.dispose()
#     mysql_engine.dispose()
