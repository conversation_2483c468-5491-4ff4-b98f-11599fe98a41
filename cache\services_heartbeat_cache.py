import time

from cache.client import Cache<PERSON>lient
from common.logger import logger
from common.singleton import Singleton

ENABLE_SERVICES_HEARTBEAT_PREFIX = 'enable-services-heartbeat'
KEEP_HEARTBEAT_PREFIX = 'keep-heartbeat'
HEARTBEAT_SERVICE_STATUS_PREFIX = 'heartbeat-service-status'


class ServicesHeartbeatCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def set_service_heartbeat(self, sender, timestamp_ms):
        # logger.debug('set_service_heartbeat: start')
        key = f"{KEEP_HEARTBEAT_PREFIX}:{sender}"
        old_value = self._redis.get(key)
        if old_value is not None:
            old_timestamp_ms = int(old_value.decode())
            # 如果时间戳小于旧时间戳，并且时间戳与旧时间戳的差值小于10秒，则不去更新
            # 增加10秒判断是避免cache中有脏数据
            if timestamp_ms < old_timestamp_ms and timestamp_ms > old_timestamp_ms - 10000:
                logger.debug(f'sender:{sender} set_service_heartbeat: timestamp_ms is less than old_timestamp_ms, '
                               f'timestamp_ms: {timestamp_ms}, old_timestamp_ms: {old_timestamp_ms}')
                return
        self._redis.set(key, timestamp_ms)
        # logger.debug('set_service_heartbeat: end')
        return

    def is_service_alive(self, sender, acceptable_delay=500):
        key = f"{KEEP_HEARTBEAT_PREFIX}:{sender}"
        value = self._redis.get(key)
        if value is not None:
            last_timestamp_ms = int(value.decode())
            now_timestamp_ms = int(round(time.time() * 1000))
            if now_timestamp_ms - last_timestamp_ms < acceptable_delay:
                return True
            logger.warning(f'sender:{sender} is_service_alive: False , '
                           f'last_timestamp_ms: {last_timestamp_ms}, '
                           f'now_timestamp_ms: {now_timestamp_ms}, '
                           f'acceptable_delay: {acceptable_delay}')
        return False

    def set_keep_alived_service_status(self, status):
        """
            设置所有保活服务状态
        """
        return self._redis.set_proto(HEARTBEAT_SERVICE_STATUS_PREFIX, status)

    def get_keep_alived_service_status(self):
        """
            查询所有服务保活状态
        """
        return self._redis.get_proto(HEARTBEAT_SERVICE_STATUS_PREFIX, cache_pb2.HeartbeatServiceStatus)

if __name__ == '__main__':
    print(ServicesHeartbeatCache().get_keep_alived_service_status())