FROM docker.fabu.ai:5000/antenna-server/antenna-server:20230705_1150


RUN pip --no-cache-dir install -i  https://mirrors.aliyun.com/pypi/simple \
    nuitka \
    pyinstaller \
    opentelemetry-sdk==1.21.0 \
    opentelemetry-exporter-jaeger==1.12.0 \
    opentelemetry-exporter-otlp==1.12.0 \
    opentelemetry-instrumentation-grpc==0.42b0 \
    opentelemetry-instrumentation-kafka-python==0.42b0 \
    opentelemetry-instrumentation-flask==0.42b0 \
    opentelemetry-instrumentation-requests==0.42b0 \
    python-snappy \
    pyproj==3.5.0 \
    pycryptodome \
    paho-mqtt \
    cryptography



RUN rm -rf /etc/apt/sources.list.d/*.list
RUN apt-get update && apt-get install -y --no-install-recommends \
    patchelf && \
    apt-get clean autoclean && \
    rm -rf /var/lib/apt/lists/*