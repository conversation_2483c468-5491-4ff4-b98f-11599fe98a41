from asyncio.log import logger
import time
from typing import Optional, Iterable, List
from cache.client import CacheClient
from proto import cache_pb2, antenna_pb2
from common.logger import logger

REMOTE_DETECTION_PREFIX = 'remote_detection-info'


def get_remote_detection(vehicle_name)->List[antenna_pb2.RemoteDetection]:
    key = f"{REMOTE_DETECTION_PREFIX}:{vehicle_name}"
    proto = CacheClient().get_proto(key, cache_pb2.RemoteDetectionSet)
    if proto is None:
        return []
    return [m for m in proto.remote_detection]

def set_remote_detection(vehicle_name, infos:list, ex=60):
    key = f"{REMOTE_DETECTION_PREFIX}:{vehicle_name}"
    proto = cache_pb2.RemoteDetectionSet()
    proto.remote_detection.extend(infos)
    return CacheClient().set_proto(key, proto, ex)
