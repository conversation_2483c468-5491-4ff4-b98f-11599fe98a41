import os

DB_IP = '***********'
DB_PORT = 1521
DB_SID = 'wrjk'

# DB_IP = '*************'
# DB_PORT = 1521
# DB_SID = 'znlh'

TOS_DB_DSN = 'oracle://fabutech:fabu1#%@***********:1521/wrjk'

TABLE_CRANE_INFO = 'T_CRANE_INFO_4V'
TABLE_CRANE_INFO_DEBUG = 'T_CRANE_INFO_4V_DEBUG'
TABLE_TOS_CMD = 'T_WI_INFO_4V_TOS'
TABLE_CMD_INFO = 'T_WI_INFO_4V_DC'
TABLE_TRUCK_INFO = 'T_TRUCK_INFO_4V'
TABLE_CONTROL_CMD = 'T_CONTROL_4V'


VMS_CONTROL_TYPE = "VMS_CONTROL"
VMS_CRANE_TYPE = "VMS_CRANE"
VMS_TRUCK_TYPE = "VMS_TRUCK"
VMS_WI_TYPE = "VMS_WI"
VMS_TRUCK_REQUEST_TYPE = 'VMS_TRUCK_REQUEST'
VMS_AHT_TRUCK_INFO_TOPIC = "AHT_TRUCK_INFO"
VMS_AHT_CONTROL_INFO_TOPIC = "AHT_CONTROL_INFO"
KAFKA_NB_VMS_SERVERS_LIST = ['**************:9092','**************:9092','**************:9092']

#hour
TOS_EXPIRE_TIME_HOUR = 2
