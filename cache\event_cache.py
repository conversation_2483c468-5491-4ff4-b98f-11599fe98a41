import time
from typing import List
from common.logger import logger
from common.singleton import Singleton
from cache.client import CacheClient
from proto.antenna_pb2 import PushAbnormalEventRequest
from proto.cache_pb2 import StartWorkAlarm, StartWorkAlarmSet, AlarmEvent

ABNORMAL_EVENT_QUEUE_KEY = 'abnormal-event-queue-key'
START_WORK_ARLARMS_KEY_PREFIX = 'start-work-alarms'
RUN_EVENT_QUEUE_KEY = 'run-event-alarms'

class EventCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def lpush_abnormal_event(self, events:List):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        return self._redis.lpush_protos(key, events)

    def rpop_abnormal_event(self):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        return self._redis.rpop_proto(key, PushAbnormalEventRequest.AbnormalEvent)

    def get_abnormal_event_length(self):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        return self._redis.llen(key)

    def get_index_abnormal_event(self,index):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        return self._redis.lindex_proto(key, index, PushAbnormalEventRequest.AbnormalEvent)

    def get_all_abnormal_event(self):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        return self._redis.lrange_proto(key, 0, -1, PushAbnormalEventRequest.AbnormalEvent)

    def delete_all_abnormal_event(self,vehicle_name, delete_length=None):
        key = f"{ABNORMAL_EVENT_QUEUE_KEY}"
        length=self._redis.llen(key)
        self._redis.ltrim(key, (delete_length if delete_length else length), -1)

    def update_start_work_alarms(self, vehicle_name, alarms:List):
        key = f"{START_WORK_ARLARMS_KEY_PREFIX}:{vehicle_name}"
        alarm_set = StartWorkAlarmSet()
        alarm_set.timestamp_ms = int(time.time() * 1000)
        alarm_set.alarms.extend(alarms)
        return self._redis.set_proto(key, alarm_set, 5)

    def get_start_work_alarms(self, vehicle_name):
        key = f"{START_WORK_ARLARMS_KEY_PREFIX}:{vehicle_name}"
        return self._redis.get_proto(key, StartWorkAlarmSet)



class RunEventCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._max_length = 100

    def lpush_event(self, events:List):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        execeed_length = self.get_length() - self._max_length
        if execeed_length > 0:
            self._redis.ltrim(key, execeed_length, -1)
            logger.warning(f"key:{key} event execeed max length:{self._max_length},now ltrim:{execeed_length}")
        return self._redis.lpush_protos(key, events)

    def rpop_event(self):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        return self._redis.rpop_proto(key, AlarmEvent)

    def get_length(self):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        return self._redis.llen(key)

    def get_index_event(self,index):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        return self._redis.lindex_proto(key, index, AlarmEvent)

    def delete_all_event(self,delete_length=None):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        length=self._redis.llen(key)
        self._redis.ltrim(key, (delete_length if delete_length else length), -1)

    def get_all_event(self, auto_delete = True):
        key = f"{RUN_EVENT_QUEUE_KEY}"
        infos = self._redis.lrange_proto(key, 0, -1, AlarmEvent)
        if auto_delete and (event_len:=len(infos)) > 0:
            self._redis.ltrim(key, event_len, -1)
        return infos

if __name__ == '__main__':
    print(EventCache().get_abnormal_event_length())
    print(EventCache().delete_all_abnormal_event(''))
    print(EventCache().get_abnormal_event_length())