import traceback
import datetime
import json
from typing import Dict
import sqlalchemy
from sqlalchemy import Column, Integer, String, DateTime, or_, and_

from common.logger import logger
from meishanalia.common import common_util
from meishanalia.communication.db import TosSession
from meishanalia.model.table_info import ControlInfo,TosWisInfo,DcWiTnfo


def record_to_wis(record: ControlInfo,wis: TosWisInfo, need_init=False):
    logger.info(f'record.__dict__:{record.__dict__}')
    for key in record.__dict__:
        if key == '_sa_instance_state' or key == 'TRUCK_NO':
            continue
        setattr(wis, 'CTRL_' + key, getattr(record, key))
    if need_init:
        wis.DISPATCH_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
        wis.TRUCK_NO = record.TRUCK_NO
        wis.WI_ACT = record.ACTION
        if record.CUR_WI is not None and len(record.CUR_WI) > 0:
            wis.WI_NO = record.CUR_WI
        # 2022.03.09
        if record.CUR_ID is not None and len(record.CUR_ID) > 0:
            wis.WI_ID = record.CUR_ID
        if common_util.is_tos_command(str(record.ACTION)):
            wis.CMD_TYPE = 'TOS_CMD'
        else:
            wis.CMD_TYPE = 'ONLY_CMD'
            wis.WI_STATUS = 'DISPATCH'
    return wis

def insert(record: ControlInfo):
    session = TosSession().acquire()
    try:
        if common_util.is_tos_command(str(record.ACTION)):
            # cur = session.query(TosWisInfo).filter_by(TRUCK_NO = record.TRUCK_NO, WI_NO = record.CUR_WI, WI_ACT = record.ACTION).first()
            # 2022.03.09
            cur = session.query(TosWisInfo).filter_by(TRUCK_NO=record.TRUCK_NO, WI_ID=record.CUR_ID, WI_ACT=record.ACTION).first()
        else:
            cur = session.query(TosWisInfo).\
                outerjoin(DcWiTnfo,DcWiTnfo.ID == TosWisInfo.ID).\
                filter(TosWisInfo.TRUCK_NO == record.TRUCK_NO).\
                filter(TosWisInfo.CTRL_ACTION == record.ACTION).\
                filter(or_(TosWisInfo.WI_STATUS == 'DISPATCH'),or_(TosWisInfo.CTRL_STATUS == 'DISPATCH',TosWisInfo.CTRL_STATUS == 'WAIT_ROUTE')).\
                filter(or_(DcWiTnfo.TRUCK_STATUS.is_(None), and_(DcWiTnfo.TRUCK_STATUS != 'FINISH',DcWiTnfo.TRUCK_STATUS.notlike('ABANDON%'), DcWiTnfo.TRUCK_STATUS.notlike('CANCEL%')))).\
                order_by(sqlalchemy.asc(TosWisInfo.ID)).first()
        if cur is None:
            record.VERSION = 1
            record.INSERT_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            record.UPDATE_TIME = record.INSERT_TIME
            cur = TosWisInfo()
            wis = record_to_wis(record, cur, True)
            logger.info(f"Insert ControlInfo table to_dict:{wis.to_dict()}")
            session.add(wis)
        else:
            record.VERSION = cur.VERSION + 1
            record.UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
            if cur.CTRL_INSERT_TIME is None:
                record.INSERT_TIME = record.UPDATE_TIME
            wis = record_to_wis(record, cur)
            logger.info(f"Update ControlInfo table origin:{cur.__dict__}")
            logger.info(f"Update ControlInfo table to_dict():{wis.to_dict()}")
        session.commit()
    except Exception as e:
        logger.warning(f"Insert ControlInfo err:{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def wis_to_control(wis: TosWisInfo):
    record = ControlInfo()
    for key in dir(record):
        if not key.startswith('_') and key.isupper() and hasattr(wis, 'CTRL_' + key) and key != 'ID':
            setattr(record, key, getattr(wis, 'CTRL_' + key))
    record.ID = wis.ID
    record.TRUCK_NO = wis.TRUCK_NO
    return record

def query_all():
      session = TosSession().acquire()
      try:
          curs = session.query(TosWisInfo).\
                          order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
          results = list()
          for cur in curs:
              cur = wis_to_control(cur)
              results.append(cur.to_dict())
          return results
      except Exception as e:
          logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
          return []
      finally:
          session.close()

def query_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        curs = session.query(TosWisInfo).\
                        filter_by(TRUCK_NO = vehicle_name).\
                        order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
        results = list()
        for cur in curs:
            cur = wis_to_control(cur)
            results.append(cur.to_dict())
        return results
    except Exception as e:
        logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()