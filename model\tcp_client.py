import socket
import time
import traceback
from common.logger import logger


class PersistentTcpClient():
    def __init__(self, server_ip, server_port, timeout=0.2, message_header=''):
        self.server_ip = server_ip
        self.server_port = server_port
        self.meesage_info = f"{message_header}->{server_ip}:{server_port}"
        self.timeout = timeout
        self.client_socket = None
        self.is_connected = False
        self.reconnect_interval = 5
        self.disconnect_count = 0

    def connect(self):
        connect_count = 0
        while not self.is_connected:
            try:
                self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                # 设置超时时间
                self.client_socket.settimeout(self.timeout)
                # 设置 TCP 保活选项
                self.client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                self.client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 10)   # 连接空闲 10 秒后开始发送保活探测包
                self.client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)    # 保活探测包发送的间隔为 5 秒
                self.client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)      # 保活探测包发送的最大次数为 3 次
                self.client_socket.connect((self.server_ip, self.server_port))
                self.is_connected = True
                logger.debug(f"connect sucess to:{self.meesage_info},connect_count:{connect_count},disconnect_count:{self.disconnect_count}")
            except (socket.error, ConnectionRefusedError, ConnectionResetError) as e:
                connect_count = connect_count + 1
                logger.debug(f"connect socket error:{self.meesage_info},connect_count:{connect_count},disconnect_count:{self.disconnect_count},"
                               f"e:{e},trace:{traceback.format_exc()}")
                self.close()
                time.sleep(self.reconnect_interval)
            except Exception as e:
                connect_count = connect_count + 1
                logger.debug(f"fail to connect:{self.meesage_info},connect_count:{connect_count},disconnect_count:{self.disconnect_count},"
                               f"e:{e},trace:{traceback.format_exc()}")
                self.close()
                time.sleep(self.reconnect_interval)

    def send_data(self, data):
        ret = False
        if not self.is_connected:
            # 如果未连接到服务器，尝试重连
            self.reconnect(wait_time=0)
        try:
            self.client_socket.sendall(data)
            ret = True
        except (socket.timeout,ConnectionResetError) as e:
            logger.warning(f"send timeout:{self.meesage_info},e:{e},trace:{traceback.format_exc()}")
            self.handle_error()
        except socket.error as e:
            # 发送数据出错，可能需要重连
            logger.warning(f"send error:{self.meesage_info},e:{e},trace:{traceback.format_exc()}")
            self.handle_error()
        except Exception as e:
            logger.warning(f"fail to send:{self.meesage_info},e:{e},trace:{traceback.format_exc()}")
            self.handle_error()
        finally:
            return ret

    def handle_error(self):
        """
        处理发送数据时的错误，并尝试重连。
        """
        self.is_connected = False
        if not self.is_connected:
            self.reconnect()

    def reconnect(self,wait_time=5):
        """
        尝试重连到服务器。
        """
        self.close()  # 关闭现有连接
        if wait_time > 0:
            time.sleep(wait_time) #等待一段时间后重连
        self.connect()  # 重新连接
        self.disconnect_count = self.disconnect_count + 1

    def close(self):
        """
        关闭套接字连接并释放资源。
        """
        if self.client_socket:
            try:
                self.client_socket.close()
            except socket.error as e:
                logger.warning(f"close error:{self.meesage_info},e:{e},trace:{traceback.format_exc()}")
            except Exception as e:
                logger.warning(f"fail to close:{self.meesage_info},e:{e},trace:{traceback.format_exc()}")
            finally:
                # 清理状态
                self.client_socket = None
                self.is_connected = False

if __name__ == "__main__":
    # 定义服务器 IP 和端口
    SERVER_IP = '127.0.0.1'
    SERVER_PORT = 65432

    client = PersistentTcpClient(SERVER_IP, SERVER_PORT, heartbeat_interval=10, timeout=5)
    client.connect()
    
    try:
        # 发送一些数据
        client.send_data("你好，服务器！")
        time.sleep(2)
        
        # 发送更多数据
        client.send_data("这是另一条消息。")
        time.sleep(2)
    finally:
        # 确保在程序结束时关闭连接
        client.close()
