import datetime
import time

import sqlalchemy
from sqlalchemy import Column, BigInteger, JSON
from sqlalchemy.ext.declarative import declarative_base

from common import common_util
from common.logger import logger
from model.connection import PlcMysqlSession

Base = declarative_base()
Base.to_dict = common_util.to_dict


class PlcInfo(Base):
    __tablename__ = 'plc_info'

    timestamp = Column(BigInteger, primary_key=True)
    gantry_status = Column(JSON)
    crane_status = Column(JSON)

    def insert(self, timestamp, gantry_status, crane_status) -> bool:
        start = time.time()
        session = PlcMysqlSession().acquire()
        try:
            plc_info = PlcInfo(timestamp=timestamp,
                               gantry_status=gantry_status,
                               crane_status=crane_status)
            #logger.info(f"Insert PlcInfo :{plc_info.__dict__}")
            session.add(plc_info)
            session.commit()
            ret = True
        except Exception as e:
            logger.warning(f"Insert PlcInfo err{e}")
            ret = False
        finally:
            session.close()
        end = time.time()
        time_diff = end - start
        if time_diff > 1.0:
            logger.warning(
                f"PlcInfo insert over cost exceed round time:{time_diff}")
        return ret

    def query_latest_data(self):
        session = PlcMysqlSession().acquire()
        try:
            return session.query(PlcInfo).order_by(sqlalchemy.desc(PlcInfo.timestamp)).first().to_dict()
        except Exception as e:
            logger.warning(f"Query PlcInfo err: {e}")
        finally:
            session.close()

    def delete_records_before_days(self, num_days=39, num_records=100) -> bool:
        """
            删除1000条数据耗时大概4S，100条大概0.4S
        """
        start = time.time()
        session = PlcMysqlSession().acquire()
        try:
            logger.debug(f"Delete PlcInfo, num_days: {num_days}, num_records: {num_records}")
            num_days_ago = datetime.datetime.now() - datetime.timedelta(days=num_days)
            timestamp_num_days_ago_ago = int(num_days_ago.timestamp())
            records = session.query(PlcInfo).filter(PlcInfo.timestamp < timestamp_num_days_ago_ago).limit(num_records)
            for record in records:
                session.delete(record)
            session.commit()
            ret = True
        except Exception as e:
            logger.warning(f"Delete PlcInfo err: {e}")
            ret = False
        finally:
            session.close()
        end = time.time()
        time_diff = end - start
        if time_diff > 1.0:
            logger.warning(f"PlcInfo delete over cost exceed time：{time_diff}, num_days: {num_days}")
        return ret


if __name__ == '__main__':
    # t = PlcInfo().query_latest_data()
    # ms = json.loads(t['gantry_status'])
    # print(ms)
    # print()
    # print(PlcInfo().query_latest_data())
    print(PlcInfo().delete_records_before_days())
