import functools
import inspect
import os
import re
import time

# Skywalking兼容处理
try:
    from skywalking import Component  # type: ignore
    from skywalking.decorators import runnable, trace  # type: ignore
    from skywalking.trace.context import SpanContext, get_context  # type: ignore

    HAS_SKYWALKING = bool(
        os.getenv("USE_SKYWALKING") in ("true", "1") and os.getenv("SW_AGENT_NAME") and os.getenv("SW_AGENT_COLLECTOR_BACKEND_SERVICES")
    )
except ImportError:
    # 降级用的空实现
    HAS_SKYWALKING = False
if not HAS_SKYWALKING:
    Component = type("Component", (), {"General": 0, "Unknown": -1})

    class DummySpanContext:
        def new_entry_span(self, op=None):
            return DummyContextManager()

        def get_trace_id(self):
            return "N/A"

    class DummyContextManager:
        def __enter__(self):
            return DummySpan()

        def __exit__(self, *args):
            pass

    class DummySpan:
        component = 0

        def __setattr__(self, name, value):
            pass

    def trace(**kwargs):
        def decorator(func):
            return func

        return decorator

    def runnable():
        def decorator(func):
            return func

        return decorator

    get_context = lambda: DummySpanContext()


def init_trace():
    if HAS_SKYWALKING:
        # 如果不等待2秒，会有上报报错，导致子进程卡住
        time.sleep(2.0)


def format_callable_name(obj):
    """格式化可调用对象名称，支持普通函数、类方法和lambda"""
    try:
        if inspect.isfunction(obj):
            # 普通函数
            return f"{obj.__module__}.{obj.__qualname__}"
        if inspect.ismethod(obj):
            # 类方法
            return f"{obj.__self__.__class__.__name__}.{obj.__name__}"
        if isinstance(obj, functools.partial):
            # 处理偏函数
            return format_callable_name(obj.func)
        if hasattr(obj, "__call__"):
            # 可调用对象
            if isinstance(obj, type):
                return obj.__name__
            return f"{obj.__class__.__name__}.__call__"
    except Exception:
        pass
    # 最后回退到字符串处理
    obj_str = str(obj)
    obj_str = re.sub(r" at 0x[0-9a-fA-F]+>", ">", obj_str)  # 移除内存地址
    obj_str = re.sub(r"<(|lambda|function|method) (.*?)>", r"\2", obj_str)  # 简化标识
    return obj_str.replace(" ", "_")
