import itertools
import json
import math
import os
import time
from typing import Dict, List

import shapely
from google.protobuf import text_format

from cache import command_cache, config_cache, map_cache, plc_cache, vehicle_cache
from common.common_util import distance_from_point_to_polygon
from common.logger import init_logger, logger
from common.periodical_task import PeriodicalTask
from common.singleton import Singleton
from plc import gantry_receiver
from proto.antenna_pb2 import Position, RemoteCommand, YardInfo, YardInfoSet
from proto.cache_pb2 import GantryCrossInfo, GantryCrossInfoSet, GantryInfo
from shapely.geometry import Point, Polygon
from proto.cache_pb2 import GantryStoreWarningInfo

class GantryCrossChecker:

    GEOMETRY_DATA_PATH = "data/meishan/gantry_cross.json"
    GANTRY_EXTEND_DISTANCE = 6.4 + 3.0

    def __init__(self):
        self._gantry_cross_map: Dict[int, GantryCrossInfo] = {}
        self._gantry_cross_geom_tree = None
        self._gantry_cross_geom_indices: List[str] = []
        self._gantry_info_cache_map: Dict[int, GantryInfo] = {}

    def init(self):
        self._gantry_cross_map.clear()
        self._gantry_cross_geom_indices.clear()
        self._gantry_info_cache_map.clear()
        try:
            with open(self.GEOMETRY_DATA_PATH) as f:
                cross_data = json.load(f)
                cross_polys = []
                for cross_name, cross_data in cross_data.items():
                    cross_poly = shapely.Polygon(
                        [(v["pointX"], v["pointY"]) for v in cross_data["boundary"]]
                    )
                    cross_polys.append(cross_poly)
                    self._gantry_cross_map[cross_name] = cross_data
                    self._gantry_cross_geom_indices.append(cross_name)
                self._gantry_cross_geom_tree = shapely.STRtree(cross_polys)
        except Exception as ex:
            logger.error(f"parse gantry cross data failed: {ex}")

    def check(self):
        container = gantry_receiver.GantryInfoContainer()
        gantry_nos = set(plc_cache.get_online_gantry_nos())
        gantry_infos = plc_cache.get_gantries(gantry_nos)
        gantry_cross_config = config_cache.ConfigCache().get_gantry_cross_config()
        extend_distance = (
            gantry_cross_config.get("extend_distance", self.GANTRY_EXTEND_DISTANCE)
            if gantry_cross_config
            else self.GANTRY_EXTEND_DISTANCE
        )
        last_gantry_cross_set_proto = map_cache.MapCache().get_gantry_cross_info()
        gantry_cross_map = {}
        forbid_gantries = gantry_cross_config.get("forbid_gantries", [])
        for gantry_info in gantry_infos:
            if gantry_info is None:
                continue
            self._gantry_info_cache_map[gantry_info.gantry_no] = gantry_info
            if not gantry_cross_config.get("enabled"):
                continue
            if gantry_info.gantry_no in forbid_gantries:
                continue
            if gantry_info.rtg_o_tag == 0:
                continue
            gantry_center = container.get_center_position(gantry_info)
            gantry_direction = container.get_gantry_direction(gantry_info.block_nul)
            if gantry_center is None or gantry_direction is None:
                continue
            gantry_line = shapely.LineString(
                [
                    shapely.Point(
                        gantry_center[0] + math.cos(gantry_direction) * extend_distance,
                        gantry_center[1] + math.sin(gantry_direction) * extend_distance,
                    ),
                    shapely.Point(
                        gantry_center[0] - math.cos(gantry_direction) * extend_distance,
                        gantry_center[1] - math.sin(gantry_direction) * extend_distance,
                    ),
                ]
            )
            ret = self._gantry_cross_geom_tree.query(
                gantry_line, predicate="intersects"
            ).tolist()
            if ret:
                gantry_cross_proto = GantryCrossInfo()
                gantry_cross_proto.gantry_no = gantry_info.gantry_no
                gantry_cross_proto.junction_name = self._gantry_cross_geom_indices[
                    ret[0]
                ]
                gantry_cross_proto.junction_points.extend(
                    [
                        Position(utm_x=point["pointX"], utm_y=point["pointY"])
                        for point in self._gantry_cross_map[
                            gantry_cross_proto.junction_name
                        ]["boundary"]
                    ]
                )
                gantry_cross_proto.update_timestamp_ms = int(time.time() * 1000)
                gantry_cross_map[gantry_info.gantry_no] = gantry_cross_proto
        if last_gantry_cross_set_proto:
            for gantry_cross in last_gantry_cross_set_proto.gantry_crosses:
                if gantry_cross.gantry_no not in gantry_cross_map:
                    gantry_cross_proto = GantryCrossInfo()
                    gantry_cross_proto.CopyFrom(gantry_cross)
                    # 如果是plc丢失的gantry，且不在屏蔽名单里，且未切换限位标记的，保留上次的信息
                    if (
                        gantry_cross.gantry_no in gantry_nos
                        or gantry_cross.gantry_no in forbid_gantries
                        or (
                            gantry_cross.gantry_no in self._gantry_info_cache_map
                            and self._gantry_info_cache_map[
                                gantry_cross.gantry_no
                            ].rtg_o_tag
                            == 0
                        )
                    ) and gantry_cross.junction_name:
                        gantry_cross_proto.junction_name = ""
                        gantry_cross_proto.ClearField("junction_points")
                        gantry_cross_proto.update_timestamp_ms = int(time.time() * 1000)
                    gantry_cross_map[gantry_cross.gantry_no] = gantry_cross_proto
                else:
                    gantry_cross_proto = gantry_cross_map[gantry_cross.gantry_no]
                    if gantry_cross.junction_name != gantry_cross_proto.junction_name:
                        gantry_cross_proto.update_timestamp_ms = int(time.time() * 1000)
                    else:
                        gantry_cross_proto.update_timestamp_ms = (
                            gantry_cross.update_timestamp_ms
                        )
                # 清理更新时间超过一天的数据
                if (
                    gantry_cross_proto.update_timestamp_ms > 0
                    and int(time.time() * 1000) - gantry_cross_proto.update_timestamp_ms
                    > 24 * 3600 * 1000
                ):
                    logger.debug(
                        f"clean old message: {gantry_cross_proto.gantry_no}, {gantry_cross_proto.update_timestamp_ms}"
                    )
                    del gantry_cross_map[gantry_cross_proto.gantry_no]

        gantry_cross_set_proto = GantryCrossInfoSet()
        for gantry_no in sorted(gantry_cross_map.keys()):
            gantry_cross = gantry_cross_set_proto.gantry_crosses.add()
            gantry_cross.CopyFrom(gantry_cross_map[gantry_no])
        if (
            not last_gantry_cross_set_proto
            or last_gantry_cross_set_proto.gantry_crosses
            != gantry_cross_set_proto.gantry_crosses
        ):
            gantry_cross_set_proto.timestamp_ms = int(time.time() * 1000)
            map_cache.MapCache().set_gantry_cross_info(gantry_cross_set_proto)
            logger.debug(
                "gantry_cross_set_proto: {}".format(
                    text_format.MessageToString(
                        gantry_cross_set_proto, as_one_line=True
                    )
                )
            )


class YardInfoChecker:
    AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER = {
        "9C": "L9",
        "9D": "D9",
        "9E": "E9",
        "XA": "AX",
        "XB": "BX",
        "XC": "CX",
        "XD": "DX",
        "XE": "EX",
        "Y6": "6Y",
        "Y7": "7Y",
        "Y8": "8Y",
        "Y9": "9Y",
        "YA": "AY",
        "YB": "BY",
        "YC": "CY",
        "YD": "DY",
        "YE": "EY",
        "X6": "6X",
        "X7": "7X",
        "X8": "8X",
        "X9": "9X",
        "8E": "E8",
    }
    AUTO_DYNAMIC_YARD_ADJACENT_MAP = {
        "9C": "9D",
        "9D": "9C",
        "XA": "XB",
        "XB": "XA",
        "XC": "XD",
        "XD": "XC",
        "Y6": "Y7",
        "Y7": "Y6",
        "Y8": "Y9",
        "Y9": "Y8",
        "YA": "YB",
        "YB": "YA",
        "YC": "YD",
        "YD": "YC",
        "X6": "X7",
        "X7": "X6",
        "X8": "X9",
        "X9": "X8",
    }
    AUTO_DYNAMIC_YARD_STACKER_TO_GANTRY = {
        v: k for k, v in AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER.items()
    }
    MANUAL_STACKER_YARD = {"7F", "7G", "7H", "7K", "9F", "9G", "XF", "XG"}
    # 新增：特殊龙门吊场地配置
    SPECIAL_GANTRY_YARDS = {"X6"}  # 始终保持龙门吊模式的场地
    SPECIAL_GANTRY_YARD_DEPENDENCIES = {
        "X6": "X7"  # X6依赖X7的状态
    }
    SPECIAL_VERTICAL_STACKER_YARDS = {}  # 特殊堆高机场地，如果双垛，强制改单垛，目前没有特殊未适配的箱区了
    YARD_INFO_SET_KEY = "yard-info-set"

    def check(self):
        vehicles = vehicle_cache.get_online_trucks_()
        commands = command_cache.CommandCache().query_vehicles_commands(vehicles)
        commands = list(itertools.chain(*commands.values()))
        commands = list(
            filter(
                lambda command: command.command.type
                in (RemoteCommand.MOVE_TO_STACKER, RemoteCommand.MOVE_TO_GANTRY),
                commands,
            )
        )
        destinations = {
            command.command.command_context.destination_id for command in commands
        }
        yard_info_update: Dict[str, YardInfo] = {}
        for destination in destinations:
            dest_yard_name = destination[:2]
            dest_seg_id = destination[2:4]
            if (
                dest_yard_name not in self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER
                and dest_yard_name not in self.AUTO_DYNAMIC_YARD_STACKER_TO_GANTRY
            ):
                continue
            yard_name = (
                dest_yard_name
                if dest_yard_name in self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER
                else self.AUTO_DYNAMIC_YARD_STACKER_TO_GANTRY[dest_yard_name]
            )
            if yard_name not in yard_info_update:
                yard_info_update[yard_name] = YardInfo(
                    name=yard_name,
                    alias_name=self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER[yard_name],
                )
            if dest_yard_name in self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER:
                yard_info_update[yard_name].work_mode = YardInfo.WorkMode.GANTRY
            elif dest_seg_id in ("06", "10"):
                yard_info_update[yard_name].work_mode = (
                    YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
                )
            else:
                if (
                    yard_info_update[yard_name].work_mode
                    != YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
                ):
                    yard_info_update[yard_name].work_mode = (
                        YardInfo.WorkMode.VERTICAL_STACKER
                    )

        # MANUAL STACKER的堆高机模式alias_name是箱区名称反转
        for yard_name in self.MANUAL_STACKER_YARD:
            yard_info_update[yard_name] = YardInfo(
                name=yard_name,
                alias_name=yard_name[::-1],
                work_mode=YardInfo.WorkMode.HORIZONTAL_STACKER,
            )
        logger.info(f"[YARD_DEBUG] Getting last_yard_info_set from cache...")
        last_yard_info_set: YardInfoSet = map_cache.MapCache().get_map_yard_info()
        logger.info(f"[YARD_DEBUG] Retrieved last_yard_info_set: {last_yard_info_set is not None}")
        if last_yard_info_set:
            logger.info(f"[YARD_DEBUG] Last yard_info_set has {len(last_yard_info_set.yards)} yards, timestamp: {last_yard_info_set.timestamp_ms}")

        new_yard_info_set = YardInfoSet()
        if last_yard_info_set:
            new_yard_info_set.timestamp_ms = last_yard_info_set.timestamp_ms
            for yard_info in last_yard_info_set.yards:
                if yard_info.name not in yard_info_update:
                    yard_info_update[yard_info.name] = YardInfo(
                        name=yard_info.name,
                        alias_name=yard_info.alias_name,
                        work_mode=yard_info.work_mode,
                    )
                    logger.info(f"[YARD_DEBUG] Inherited yard from cache: name={yard_info.name}, alias_name={yard_info.alias_name}, work_mode={yard_info.work_mode}")
                else:
                    # 如果last work_mode是VERTICAL_STACKER_WITH_SECOND_STACK，且当前work_mode是VERTICAL_STACKER，则将work_mode设置为VERTICAL_STACKER_WITH_SECOND_STACK
                    if (
                        yard_info.work_mode
                        == YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
                        and yard_info_update[yard_info.name].work_mode
                        == YardInfo.WorkMode.VERTICAL_STACKER
                    ):
                        logger.info(f"[YARD_DEBUG] Upgrading {yard_info.name} from VERTICAL_STACKER to VERTICAL_STACKER_WITH_SECOND_STACK (inherited from cache)")
                        yard_info_update[yard_info.name].work_mode = (
                            YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
                        )
        # 如果当前work_mode是VERTICAL_STACKER，且相邻箱区是VERTICAL_STACKER_WITH_SECOND_STACK，则将work_mode设置为VERTICAL_STACKER_ADJACENT_SECOND_STACK
        # 如果当前work_mode是VERTICAL_STACKER_WITH_SECOND_STACK，且相邻箱区也是VERTICAL_STACKER_WITH_SECOND_STACK，则报错，且已时间更新的箱区的work_mode为准
        for yard_name in list(yard_info_update.keys()):
            yard_info = yard_info_update[yard_name]
            if (
                yard_info.work_mode
                != YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
            ):
                continue
            adjacent_yard_name = self.AUTO_DYNAMIC_YARD_ADJACENT_MAP.get(yard_name)
            if not adjacent_yard_name:
                continue
            if adjacent_yard_name in yard_info_update:
                yard_info_update[adjacent_yard_name].work_mode = (
                    YardInfo.WorkMode.VERTICAL_STACKER_ADJACENT_SECOND_STACK
                )
                if (
                    yard_info_update[adjacent_yard_name].work_mode
                    == YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
                ):
                    logger.error(
                        f"adjacent yard {adjacent_yard_name} is VERTICAL_STACKER_WITH_SECOND_STACK, but current yard {yard_name} is VERTICAL_STACKER_WITH_SECOND_STACK"
                    )
                elif (
                    yard_info_update[adjacent_yard_name].work_mode
                    == YardInfo.WorkMode.GANTRY
                ):
                    logger.error(
                        f"adjacent yard {adjacent_yard_name} is Gantry, but current yard {yard_name} is VERTICAL_STACKER_WITH_SECOND_STACK"
                    )
            else:
                yard_info_update[adjacent_yard_name] = YardInfo(
                    name=adjacent_yard_name,
                    alias_name=self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER[
                        adjacent_yard_name
                    ],
                    work_mode=YardInfo.WorkMode.VERTICAL_STACKER_ADJACENT_SECOND_STACK,
                )
        # 如果当前work_mode是VERTICAL_STACKER_ADJACENT_SECOND_STACK，且相邻箱区不是VERTICAL_STACKER_WITH_SECOND_STACK，则将work_mode设置为VERTICAL_STACKER
        for yard_name in self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER.keys():
            yard_info = yard_info_update.get(yard_name)
            if not yard_info:
                continue
            if (
                yard_info.work_mode
                != YardInfo.WorkMode.VERTICAL_STACKER_ADJACENT_SECOND_STACK
            ):
                continue
            adjacent_yard_name = self.AUTO_DYNAMIC_YARD_ADJACENT_MAP.get(yard_name)
            if not adjacent_yard_name:
                continue
            if (
                adjacent_yard_name in yard_info_update
                and yard_info_update[adjacent_yard_name].work_mode
                == YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK
            ):
                continue
            yard_info.work_mode = YardInfo.WorkMode.VERTICAL_STACKER
        # 没有配置过的箱区，设置为GANTRY
        for yard_name in self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER.keys():
            if yard_name not in yard_info_update:
                yard_info_update[yard_name] = YardInfo(
                    name=yard_name,
                    alias_name=self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER[yard_name],
                    work_mode=YardInfo.WorkMode.GANTRY,
                )
        logger.info(f"[YARD_DEBUG] Before special yard handling - yard_info_update keys: {list(yard_info_update.keys())}")

        self._handle_special_gantry_yards(yard_info_update)

        logger.info(f"[YARD_DEBUG] After special yard handling - yard_info_update keys: {list(yard_info_update.keys())}")

        for yard_name in sorted(yard_info_update.keys()):
            yard_info = yard_info_update[yard_name]
            if yard_info.name in self.AUTO_DYNAMIC_YARD_ADJACENT_MAP:
                yard_info.adjacent_yard_name = self.AUTO_DYNAMIC_YARD_ADJACENT_MAP[
                    yard_info.name
                ]
            new_yard_info_set.yards.append(yard_info)

        logger.info(f"[YARD_DEBUG] Final yard_info_set will have {len(new_yard_info_set.yards)} yards")

        if last_yard_info_set != new_yard_info_set:
            new_yard_info_set.timestamp_ms = int(time.time() * 1000)
            logger.info(f"[YARD_DEBUG] Yard info changed, updating cache with timestamp: {new_yard_info_set.timestamp_ms}")
            logger.info(f"update yard info:{new_yard_info_set}")
            map_cache.MapCache().set_map_yard_info(new_yard_info_set)
        else:
            logger.info(f"[YARD_DEBUG] Yard info unchanged, not updating cache")

    def _handle_special_gantry_yards(self, yard_info_update: Dict[str, YardInfo]):
        """处理特殊龙门吊场地（如X6）的逻辑"""
        for special_yard in self.SPECIAL_GANTRY_YARDS:
            # 确保X6始终存在且为龙门吊模式
            if special_yard not in yard_info_update:
                # 从映射表中获取：X6 -> 6X
                alias_name = self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER.get(special_yard, special_yard)
                yard_info_update[special_yard] = YardInfo(
                    name=special_yard,
                    alias_name=alias_name,  # X6的alias_name是6X
                    work_mode=YardInfo.WorkMode.GANTRY,
                )
                logger.debug(f"Created special yard {special_yard} with GANTRY mode")
            else:
                # 强制设置为龙门吊模式
                original_mode = yard_info_update[special_yard].work_mode
                yard_info_update[special_yard].work_mode = YardInfo.WorkMode.GANTRY
                if original_mode != YardInfo.WorkMode.GANTRY:
                    logger.info(f"Special yard {special_yard} forced from {original_mode} to GANTRY mode")

            # 处理依赖关系（X6依赖X7）
            dependency_yard = self.SPECIAL_GANTRY_YARD_DEPENDENCIES.get(special_yard)
            if dependency_yard and dependency_yard in yard_info_update:
                # X6继承X7的adjacent_name
                dependency_yard_info = yard_info_update[dependency_yard]
                if hasattr(dependency_yard_info, 'adjacent_yard_name') and dependency_yard_info.adjacent_yard_name:
                    yard_info_update[special_yard].adjacent_yard_name = dependency_yard_info.adjacent_yard_name
                    logger.info(
                        f"Special yard {special_yard} inherits adjacent_name from {dependency_yard}: "
                        f"{dependency_yard_info.adjacent_yard_name}"
                    )
                else:
                    logger.debug(f"Dependency yard {dependency_yard} has no adjacent_name to inherit")
            else:
                if dependency_yard:
                    logger.debug(f"Dependency yard {dependency_yard} not found in yard_info_update for {special_yard}")

            logger.debug(f"Special yard {special_yard} processing completed")
        # YE/EY, XE/EX, 9E/E9处理, 如果是双垛，强制改单垛
        for special_yard in self.SPECIAL_VERTICAL_STACKER_YARDS:
            if special_yard in yard_info_update:
                alias_name = self.AUTO_DYNAMIC_YARD_GANTRY_TO_STACKER.get(special_yard)
                if alias_name:
                    if yard_info_update[special_yard].work_mode == YardInfo.WorkMode.VERTICAL_STACKER_WITH_SECOND_STACK:
                        yard_info_update[special_yard].work_mode = YardInfo.WorkMode.VERTICAL_STACKER
                        logger.info(f"Special yard {special_yard} forced from {yard_info_update[special_yard].work_mode} to VERTICAL_STACKER mode")



class GantryStoreChecker:
    def check(self):
        container = gantry_receiver.GantryInfoContainer()
        gantry_nos = set(plc_cache.get_online_gantry_nos())
        gantry_infos = filter(None, plc_cache.get_gantries(gantry_nos))

        map_adjustments, block_id = map_cache.MapCache().get_all_map_adjustment()
        yard_offline_maps = [
            info for info in map_adjustments
            if info.source_key.endswith('YARD-OFFLINE')
        ]

        for gantry_info in gantry_infos:
            gantry_no = gantry_info.gantry_no
            gantry_center = container.get_center_position(gantry_info)
            if gantry_center is None:
                continue
            gantry_point = Point(gantry_center[0], gantry_center[1])
            for info in yard_offline_maps:
                distance = distance_from_point_to_polygon(gantry_point, info.block_rect)
                if distance == 0:
                    plc_cache.set_store_gantry_proximity_warning_info(int(gantry_no),GantryStoreWarningInfo.WARNING_LEVEL1)
                    logger.debug(f"{gantry_no} in YARD-OFFLINE map_adjustments")

class YardMonitor:
    YARD_INFO_CHECK_INTERVAL = 2.0
    GANTRY_CROSS_CHECK_INTERVAL = 2.0
    GANTRY_STORE_CHECK_INTERVAL = 2.0

    def __init__(self):
        self._running = False
        self._yard_info_thread = None
        self._yard_info_checker = None
        self._gantry_cross_thread = None
        self._gantry_cross_checker = None
        self._gantry_store_thread = None
        self._gantry_store_checker = None

    def start_monitor_yard_info(self):
        master_only = True
        self._yard_info_checker = YardInfoChecker()
        self._yard_info_thread = PeriodicalTask(
            target=self.monitor_yard_info_loop,
            interval=self.YARD_INFO_CHECK_INTERVAL,
            master_only=master_only,
        )
        self._yard_info_thread.start()

    def start_monitor_gantry_cross(self):
        master_only = True
        self._gantry_cross_checker = GantryCrossChecker()
        self._gantry_cross_checker.init()
        self._gantry_cross_thread = PeriodicalTask(
            target=self.monitor_gantry_cross_loop,
            interval=self.GANTRY_CROSS_CHECK_INTERVAL,
            master_only=master_only,
        )
        self._gantry_cross_thread.start()

    def start_monitor_gantry_store(self):
        master_only = True
        self._gantry_store_checker = GantryStoreChecker()
        self._gantry_store_thread = PeriodicalTask(
            target=self.monitor_gantry_store_loop,
            interval=self.GANTRY_STORE_CHECK_INTERVAL,
            master_only=master_only,
        )
        self._gantry_store_thread.start()

    def start(self):
        if not self._running:
            self.start_monitor_yard_info()
            self.start_monitor_gantry_cross()
            self.start_monitor_gantry_store()
            self._running = True

    def monitor_yard_info_loop(self):
        self._yard_info_checker.check()

    def monitor_gantry_cross_loop(self):
        self._gantry_cross_checker.check()

    def monitor_gantry_store_loop(self):
        self._gantry_store_checker.check()


if __name__ == "__main__":
    init_logger("yard_monitor")
    print(YardMonitor().start())
