from typing import Optional

from cache.client import Cache<PERSON>lient
from proto import cache_pb2

ENABLE_AUTO_DEBUG_CONTROL = 'enable-auto-debug-control'
#default use
#default not use
def disable_auto_debug_control():
    #return CacheClient().client().set(ENABLE_AUTO_DEBUG_CONTROL, 1)
    return Cache<PERSON>lient().client().delete(ENABLE_AUTO_DEBUG_CONTROL)

def enable_auto_debug_control():
    #return CacheClient().client().delete(ENABLE_AUTO_DEBUG_CONTROL)
    return CacheClient().client().set(ENABLE_AUTO_DEBUG_CONTROL, 1)

def if_auto_debug_control():
    #return (CacheClient().client().get(ENABLE_AUTO_DEBUG_CONTROL) is None)
    return (CacheClient().client().get(ENABLE_AUTO_DEBUG_CONTROL) is not None)

if __name__ == '__main__':
    enable_auto_debug_control()
    get = get_crane_calib_offset()
    print(get)
    get = if_auto_debug_control_offset()
    print(get)
