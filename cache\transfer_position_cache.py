import traceback
import time

from common.logger import logger
from common.singleton import Singleton
from cache.client import CacheClient

TRANSFER_POSITION_PREFIX = 'transfer-position'
TRANSFER_POSITION_SET_PREFIX = 'transfer-position-set'
MANUAL_TRANSFER_POSITION_PREFIX = 'manual-transfer-position'
MANUAL_TRANSFER_POSITION_SET_PREFIX = 'manual-transfer-position-set'
TRANSFER_POSITION_POINTS = 'transfer-position-points'

class TransferPositionCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def get_all_tp_keys(self, is_auto=True):
        tp_set =  self._redis.smembers(TRANSFER_POSITION_SET_PREFIX  if is_auto else MANUAL_TRANSFER_POSITION_SET_PREFIX)
        res = []
        for tp in tp_set:
            res.append(tp.decode())
        return res

    def clear_all_tp(self, is_auto=True):
        tp_key_set = self._redis.smembers(TRANSFER_POSITION_SET_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_SET_PREFIX)
        for tp_key in tp_key_set:
            tp_key = tp_key.decode()
            self.clear_tp(tp_key)

    def clear_tp(self, transfer_position_key, is_auto=True):
        self._redis.srem(TRANSFER_POSITION_SET_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_SET_PREFIX , transfer_position_key)
        key = f"{TRANSFER_POSITION_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_PREFIX}:{transfer_position_key}"
        self._redis.delete(key)

    def set_tp(self, transfer_position_key, transfer_square_value, ex=None, is_auto=True):
        # add in set
        self._redis.sadd(TRANSFER_POSITION_SET_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_SET_PREFIX, transfer_position_key)
        # add in string
        key = f"{TRANSFER_POSITION_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_PREFIX}:{transfer_position_key}"
        self._redis.set(key, transfer_square_value, ex)

    def get_tp(self, transfer_position_key, is_auto=True):
        key = f"{TRANSFER_POSITION_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_PREFIX }:{transfer_position_key}"
        tp =  self._redis.get(key)
        if tp is not None:
            return tp.decode()
        else:
            # 如果过期时间已过没查询到则从set中删掉
            self._redis.srem(TRANSFER_POSITION_SET_PREFIX if is_auto else MANUAL_TRANSFER_POSITION_SET_PREFIX, transfer_position_key)
            logger.debug(f"KEY({key}) NOT FOUND.")
            return None

    def set_tp_name_point(self, key, point):
        self._redis.hset(TRANSFER_POSITION_POINTS, key, point)

    def get_tp_name_point(self, key):
        return self._redis.hget(TRANSFER_POSITION_POINTS, key).decode()

    def get_all_tp_point_keys(self):
        keys=self._redis.hkeys(TRANSFER_POSITION_POINTS)
        res=[]
        for key in keys:
            res.append(key.decode())
        return res

    def get_all_tp_point(self):
        return self._redis.hgetall(TRANSFER_POSITION_POINTS).decode()

    def clear_all_tp_point(self):
        return self._redis.delete(TRANSFER_POSITION_POINTS)


if __name__ == '__main__':
    t = TransferPositionCache()
    print(t.get_all_tp_keys())

    # test case 1, set ex = 5
    # print(t.get_tp('PB911'))
    # time.sleep(5)
    # print('show after sleep')
    # print(t.get_tp('PB911'))
    # print(t.get_all_tp_keys())

    # test case 2
    print('show after delete')
    print(t.clear_all_tp())
    print(t.get_all_tp_keys())
    print(t.get_tp('PB911'))
