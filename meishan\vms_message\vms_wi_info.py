import json
from proto.cache_pb2 import PreWiInfo
from common.logger import logger
from cache.wi_cache import WiCache
from config.config_manage import ConfigManage

_last_pre_wi_enabled = True

def vms_wi_info_handler(msg):
    global _last_pre_wi_enabled
    info = json.loads(msg.decode())
    if info is None:
        return
    info_dict = {k.lower(): v for k, v in info.items()}
    current_enabled = ConfigManage().get_config_pre_wi_start()
    if current_enabled:
        _last_pre_wi_enabled = True
        pre_wi_info = PreWiInfo()
        pre_wi_info.truck_no = info_dict.get("cheid")
        pre_wi_info.crane_no = info_dict.get("crid")
        pre_wi_info.container_id = info_dict.get("containerid")
        pre_wi_info.cycle_mode = info_dict.get("cyclemode")
        pre_wi_info.recv_timestamp = info_dict.get("sendtime")
        pre_wi_info.wi_ref = info_dict.get("wiref")
        pre_wi_info.wi_status = "DISPATCH"
        WiCache().hset_pre_wi_info(pre_wi_info)
        logger.debug(f"set_pre_wi_info: {pre_wi_info}")
        return
    elif _last_pre_wi_enabled:
        WiCache().clear_all_pre_wi_info()
        _last_pre_wi_enabled = False
        return