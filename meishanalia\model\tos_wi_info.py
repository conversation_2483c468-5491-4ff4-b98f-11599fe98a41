import traceback
from typing import Dict
import datetime
import sqlalchemy
from sqlalchemy import Column, Integer, String, DateTime,and_,or_
from common.logger import logger
from meishanalia.communication.db import TosSession
from meishanalia.model.table_info import TosWisInfo, TosWiInfo


def record_to_wis(record: TosWiInfo, wis: TosWisInfo):
    for key in record.__dict__:
        if key == '_sa_instance_state':
            continue
        setattr(wis, key, getattr(record, key))
    return wis

def insert(record: TosWiInfo):
    session = TosSession().acquire()
    try:
        # 2022.03.09
        cur = session.query(TosWisInfo).filter_by(TRUCK_NO=record.TRUCK_NO, WI_ID=record.WI_ID, WI_ACT=record.WI_ACT).first()
        # 2022.02.07 add wi_id replace wi_no
        # cur = session.query(TosWisInfo).filter_by(TRUCK_NO = record.TRUCK_NO, WI_NO = record.WI_NO, WI_ACT = record.WI_ACT).first()
        if cur is None:
            cur = TosWisInfo()
            wis = record_to_wis(record, cur)
            logger.info(f"Insert TosWiInfo table to_dict:{wis.to_dict()}")
            session.add(wis)
        else:
            '''
            record.VERSION = cur.VERSION + 1
            '''
            #session.query(TosWiInfo).filter(TosWiInfo.TRUCK_NO == record.TRUCK_NO,TosWiInfo.WI_NO == record.WI_NO,TosWiInfo.WI_ACT == record.WI_ACT).update(update_record)
            wis = record_to_wis(record, cur)
            logger.info(f"Update TosWiInfo table origin:{cur.__dict__}")
            logger.info(f"Update TosWiInfo table to_dict():{wis.to_dict()}")
        session.commit()
    except Exception as e:
        logger.warning(f"Insert TosWiInfo err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()

def wis_to_wi(wis: TosWisInfo):
    record = TosWiInfo()
    for key in dir(record):
        if not key.startswith('_') and key.isupper():
            setattr(record, key, getattr(wis, key))
    record.ID = wis.ID
    return record
  

def query_all():
      session = TosSession().acquire()
      try:
          curs = session.query(TosWisInfo).\
                          order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
          results = list()
          for cur in curs:
              cur = wis_to_wi(cur)
              results.append(cur.to_dict())
          return results
      except Exception as e:
          logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
          return []
      finally:
          session.close()

def query_by_name(vehicle_name):
    session = TosSession().acquire()
    try:
        curs = session.query(TosWisInfo).\
                        filter_by(TRUCK_NO = vehicle_name).\
                        order_by(sqlalchemy.asc(TosWisInfo.ID)).all()
        results = list()
        for cur in curs:
            cur = wis_to_wi(cur)
            results.append(cur.to_dict())
        return results
    except Exception as e:
        logger.warning(f"query all TosWisInfo Err: {e},trace:{traceback.format_exc()}")
        return []
    finally:
        session.close()

if __name__ == '__main__':
    wi_record = TosWiInfo()
    update_by_id(7)
    info = get_by_id(7)
    if info:
        print(f'''type(info):{type(info)}''')
        test = info.to_dict() #if not Base.to_dict = to_dict will error
        print(f'''info get test:{test}''')
        #print(f'''info get test.dict:{test.__dict__}''')
    else:
        print('no info id')
