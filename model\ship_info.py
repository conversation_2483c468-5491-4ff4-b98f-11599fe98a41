import sqlalchemy
from sqlalchemy import Column, Integer, String
from sqlalchemy.ext.declarative import declarative_base

from common import common_util
from common.logger import logger
from model.connection import TosMysqlSession

Base = declarative_base()
Base.to_dict = common_util.to_dict


class ShipInfo(Base):
    __tablename__ = 'ship_info'

    id = Column(Integer, primary_key=True, autoincrement=True)
    ship_name = Column(String(255), nullable=False)
    ship_length = Column(String(255))
    berthing_direction = Column(String(50))
    work_face = Column(String(255))
    status = Column(String(100))

    def insert(self, ship_name, ship_length, berthing_direction, work_face, status) -> bool:
        session = TosMysqlSession().acquire()
        try:
            ship_info = ShipInfo(ship_name=ship_name,
                                 ship_length=ship_length,
                                 berthing_direction=berthing_direction,
                                 work_face=work_face,
                                 status=status)
            logger.info(f"Insert ShipInfo :{ship_info.__dict__}")
            session.add(ship_info)
            session.commit()
            ret = True
        except Exception as e:
            logger.warning(f"Insert ShipInfo err{e}")
            ret = False
        finally:
            session.close()
        return ret

    def query_latest_data(self):
        session = TosMysqlSession().acquire()
        try:
            return session.query(ShipInfo).order_by(sqlalchemy.desc(ShipInfo.timestamp)).first().to_dict()
        except Exception as e:
            logger.warning(f"Query ShipInfo err: {e}")
        finally:
            session.close()


if __name__ == '__main__':
    # t = ShipInfo().query_latest_data()
    # ms = json.loads(t['gantry_status'])
    # print(ms)
    # print()
    # print(ShipInfo().query_latest_data())
    print(ShipInfo().insert('测试船只', '456', '789', '123', '0'))
