import datetime
from typing import Dict
import math
import time
from common import common_util

from cache.rspu import utm_to_latlng
from cache.vehicle_cache import set_truck_info_path2_to_redis
from meishanalia.common.constant import TOS_EXPIRE_TIME_HOUR, TABLE_TOS_CMD, TABLE_CMD_INFO, TABLE_TRUCK_INFO, TABLE_CRANE_INFO
from common.logger import logger
from common.common_util import from_utm_to_latlon
from proto.antenna_pb2 import VehicleStatus
from meishanalia.model import wis_info,dc_wi_info,crane_info,truck_info
from meishanalia.model.table_info import CraneInfo, TosWisInfo


class TosDb(object):
    def get_recent_wis(self, truck_no):
        return wis_info.query_recent(truck_no, TOS_EXPIRE_TIME_HOUR)

    def get_trucks_recent_wis(self, truck_nos):
        return wis_info.query_trucks_recent(truck_nos, TOS_EXPIRE_TIME_HOUR)

    #返回tos_wi,control两个信息T_WI_INFO_4V_TOS.WI_STATUS包含DISPATCH,CANCEL
    #control信息中的CUR_WIS可能带两条WI_NO，如果带两条默认取第一个WI_NO对应的指令信息
    def get_wis(self, truck_no, only_dispatch=False):
        return wis_info.query_execute(truck_no, TOS_EXPIRE_TIME_HOUR)

    def get_dispatch_wis(self, truck_no):
        #ToDo
        return []

    def set_route_wi_status(self, truck_no, id):
        update_items = dict()
        update_items['CTRL_STATUS'] = 'WAIT_ROUTE'
        wis_info.update(id, update_items)

    def get_wi_info_by_id(self, truck_no, id):
        return wis_info.query_info_by_ID(truck_no, id)

    def is_wi_done(self, truck_no, wi_id, ctn_no, wi_act):
        wi = dc_wi_info.query_by_wi_ctn(truck_no, wi_id, wi_act, ctn_no)
        if wi is not None and wi.get('TRUCK_STATUS') == 'FINISH':
            return True
        wi = wis_info.query_by_wi_ctn(truck_no, wi_id, wi_act, ctn_no)
        if wi is not None and wi.get('WI_STATUS') == 'FINISH':
            return True
        return False

    def is_twins_valid(self, one, other):
        if not ((one['TO_POS'].startswith('CR') and other['TO_POS'].startswith('CR')) or \
                ((one['TO_POS'][0].isdigit()) and ( (other['TO_POS'][0].isdigit() )))):
            logger.warning('twins error(TO_POS):one:' + str(one) + ",other:" + str(other))
            return False
        if not ((one['TRUCK_POS'] == '0' and other['TRUCK_POS'] == '0') or \
                (one['TRUCK_POS'] == 'F' and other['TRUCK_POS'] == 'A') or (one['TRUCK_POS'] == 'A' and other['TRUCK_POS'] == 'F')):
            logger.warning('twins error(TRUCK_POS):one:' + str(one) + ",other:" + str(other))
            return False
        return True

    def determine_twins(self, truck_no, wis, is_debug=True):
        for wi in wis:
            cur_wis = wi.get('CTRL_CUR_WIS')
            cur_wi = wi.get('CTRL_CUR_WI')
            twin_flag = wi.get('TWIN_FLAG')
            wi['TWIN_TYPE'] = 'ALONE'
            #make sure T,or by wether done
            if cur_wis is not None and len(cur_wis) > 0 and (twin_flag == 'Y' or twin_flag == 'T'):
                if cur_wis != cur_wi:
                    wi['TWIN_TYPE'] = 'FIRST'
                else: 
                    wi['TWIN_TYPE'] = 'SECOND'
        return wis

    # 取下DC中的指令信息是否有，如果有，状态是否是ABANDON,且比较当前最新的更新时间是否满足条件
    def is_wis_support_execute(self, truck_no, wis: list):
        if len(wis) < 1:
            return False
        wi = wis[0]
        if (wi.get('WI_STATUS') == 'DISPATCH' and wi.get('CTRL_STATUS') == 'DISPATCH' and (not 'ABANDON' in str(wi.get('TRUCK_STATUS')))):
            if wi.get('CMD_TYPE') == 'TOS_CMD' or wi.get('CMD_TYPE') == 'ONLY_CMD':
                return True
        logger.warning(f"vehicle:{truck_no} get can not execute wi id:{wi['ID']},cmd_type:{wi.get('CMD_TYPE')},to_pos:{wi.get('TO_POS')},status(wi,ctrl,truck):{wi.get('WI_STATUS')},{wi.get('CTRL_STATUS'),{wi.get('TRUCK_STATUS')}}")
        return False

    def fiter_invalid_wis(self, truck_no, wis: list) -> list:
        if len(wis) == 0:
            return []
        wi = wis[0]
        # 2022.02.09 temp for test
        if wi.get('CTRL_STATUS') != 'DISPATCH' or 'ABANDON' in str(wi.get('TRUCK_STATUS')):
            logger.warning(f"vehicle_name:{truck_no} get wi id:{wi.get('ID')} invalid ctrl_status:{wi.get('CTRL_STATUS')},truck status:{wi.get('TRUCK_STATUS')}")
            return []
        return wis


    def fiter_execute_wis(self, truck_no, wis: list) -> list:
        if len(wis) == 0:
            return []
        return wis[:1]

    def pick_up_wis(self, truck_no, wis, load_status):
        idx = 0
        num = len(wis)
        new_wis = []
        if load_status:
            # 若车上有箱子去掉收工或离场的指令
            for wi in wis:
                idx = idx + 1
                # 如果是收工或离场取消指令也取出执行
                if not common_util.is_off_work_wi(wi) or wi.get('WI_STATUS') == 'CANCEL':
                    new_wis.append(wi)
                else:
                    info = f"pick_up_wis:truck_no:{truck_no},load_status:{load_status},num:{num}," + \
                           f"idx:{idx},discard off work wi:{wi.get('ID')}"
                    if num > 1:
                        logger.debug(f"{info}")
                    else:
                        logger.info(f"{info}")
        else:
            # 若车上没有箱子且当前给了可执行的收工或离场指令则执行
            for wi in wis:
                idx = idx + 1
                if common_util.is_off_work_wi(wi) and wi.get('WI_STATUS') == 'DISPATCH':
                    new_wis.append(wi)
                else:
                    logger.debug(f"pick_up_wis:truck_no:{truck_no},load_status:{load_status},num:{num},"
                                 f"idx:{idx},discard work wi:{wi.get('ID')},act:{wi.get('WI_ACT')},wi status:{wi.get('WI_STATUS')}")
            new_wis = new_wis[:1] if len(new_wis) > 0 else wis
        return new_wis

    #get_wis return abandon
    def get_new_wi(self, truck_no, load_status, is_debug=True, only_dispatch=False):
        wis = self.get_wis(truck_no, only_dispatch)
        wis_num = len(wis)
        if is_debug:
            logger.debug("{} get_new_wi, size: {}, wis: {}".format(truck_no, wis_num, wis))
        #support stop_work or leave space
        wis = self.pick_up_wis(truck_no, wis, load_status)
        wis_num = len(wis)
        if is_debug:
            logger.debug("{}  get_new_wi after pick up, size: {}, wis: {}".format(truck_no, wis_num, wis))

        if wis_num > 0:
            if wis[0].get('WI_STATUS') == 'DISPATCH' and (wis[0].get('TWIN_FLAG') == 'Y' or wis[0].get('TWIN_FLAG') == 'T'):
                wis = self.determine_twins(truck_no, wis, is_debug)
            else:
                wis = wis[:1]
                wis[0]['TWIN_TYPE'] = 'ALONE'
        return wis, wis_num

    def query_new_wi(self, truck_no, load_status = True):
        logger.debug(f"[query_new_wi] vehicle_name:{truck_no}, load_status:{load_status}")
        wis, wis_num = self.get_new_wi(truck_no, load_status)
        wis = self.fiter_invalid_wis(truck_no, wis)
        execute_wis = self.fiter_execute_wis(truck_no, wis)
        # execute_wis = self.select_execute_new_wis(truck_no, wis)
        if len(execute_wis) > 0:
            for wi in execute_wis:
                self.insert_wi_status(wi['ID'])
        return wis, wis_num
  
    def get_abandon_wi(self,truck_no, wis):
        if len(wis) == 0:
            return None
        wi = wis[0]
        # if wi.get('WI_STATUS') == 'DISPATCH' and wi.get('CTRL_STATUS') == 'DISPATCH' and 'ABANDON' in str(wi.get('TRUCK_STATUS')):
        if wi.get('CTRL_STATUS') == 'DISPATCH' and 'ABANDON' in str(wi.get('TRUCK_STATUS')):  # 如果abandon的指令被cancel，则取消该指令
            return wi
        return None
        
    def query_abandon_wi(self, truck_no, load_status):
        wis, _ = self.get_new_wi(truck_no, load_status, False)
        wi = self.get_abandon_wi(truck_no,wis)
        return wi

    def query_crane_info(self, crane_id, is_debug):
        return crane_info.get_by_crane_no(crane_id, is_debug)
        # return crane_info.get_by_crane_no(crane_id, True)  # 临时使用 仿真调试专用

    def update_crane_info(self, crane_item, is_debug=True):
        crane = CraneInfo()
        crane.from_dict(crane_item)
        crane_info.insert(crane, update=True, debug=is_debug)

    def batch_query_lane_no(self, crane_nos, is_debug) -> Dict[int, int]:
        cranes = crane_info.batch_get_by_crane_no(crane_nos, is_debug)
        lanes = dict()
        for crane in cranes:
            if crane.get('LANE_NO') is not None and crane.get('LANE_NO').isdigit():
                crane_no = int(crane.get('CRANE_ID')[2:])
                lanes[crane_no] = int(crane.get('LANE_NO'))
        return lanes

    def batch_query_cranes(self, crane_nos, is_debug) -> Dict:
        cranes = crane_info.batch_get_by_crane_no(crane_nos, is_debug)
        result = {}
        for crane in cranes:
            crane_no = int((crane.get('CRANE_ID','CR0')[2:]))
            lane_no = int(crane.get('LANE_NO')) if (crane.get('LANE_NO') is not None and crane.get('LANE_NO').isdigit()) else None
            #default R
            direction = 'L' if crane.get('VESSEL_DIRECTION', 'R') == 'L' else 'R'
            result[crane_no] = {'LANE_NO':lane_no, 'VESSEL_DIRECTION':direction}
        return result

    def query_wi_update(self, id, version):
        return wis_info.query_by_id_version(id, version)

    def update_wi_version(self, id, version):
        dc_wi_info.update_vesion(id, version)

    def set_vehicle_offline(self, vechicle_name):
        truck_info.update_truck_offline(vechicle_name)

    def update_vehicle_status(self, vehicle_status, cur_ids, cur_pos, truck_mode, truck_status, action):
        # record = truck_info.TruckInfo()
        args_table = vehicle_status_to_dict(vehicle_status)
        # 2022.03.09
        # args_table['CUR_WIS'] = cur_wis
        # args_table['CUR_IDS'] = cur_ids
        args_table['CUR_WIS'] = cur_ids  # fix kafka send truck status data cur_wis bug
        args_table['CUR_POS'] = cur_pos
        args_table['TRUCK_MODE'] = truck_mode
        args_table['TRUCK_STATUS'] = truck_status
        args_table['ACTION'] = action
        # # logger.info('update_vehicle_status: ' + str(args_table))
        # record.from_dict(args_table)
        # # logger.info('999-record type_of_record: ' + str(record) + str(type(record)))
        # truck_info.insert(record)
        return args_table
    

    def insert_wi_status(self, id):
        logger.debug('insert_wi_status' + str(id))
        dc_wi_info.insert_by_wi_id(id, truck_status='START')

    def insert_cancel_wi_status(self, id):
        logger.debug('insert_cancel_wi_status: ' + str(id))
        return dc_wi_info.insert_by_wi_id(id, truck_status='CANCEL_YARD')

    def update_wi_status(self, vehicle_name, id, truck_status):
        dc_wi_info.update_wi_status(id, truck_status)
        dc_wi = self.query_dc_wi_by_id(vehicle_name, id)
        if dc_wi is not None:
            logger.debug(f"update_wi_status result:vehicle_name:{vehicle_name},id:{id}"
                        f",truck_status:{dc_wi.get('TRUCK_STATUS')},version:{dc_wi.get('VERSION')}")
        else:
            logger.warning(f"update_wi_status fail:vehicle_name:{vehicle_name},id:{id}")


    def reset_command(self, vehicle_name, id, info = "", need_route=True):
        logger.info(f'reset command: id:{id},info:{info},need_route:{need_route}')
        # dc_wi_info.delete_by_id(id)
        # delete dc_wi_info by set TRUCK_STATUS and VERSION to null
        dc_wi_info.update(id, {"VERSION": None, "TRUCK_STATUS": None})
        if need_route:
            wis_info.update_by_reset(id)  # reset指令 设置reset标志
            logger.info(f'update_by_reset reset_tos_commands: id:{id}')

    def query_wi_by_id(self, vehicle_name, id):
        ret = wis_info.query_by_id(id)
        if ret:
            return ret
        else:
            return None

    def query_dc_wi_by_id(self, vehicle_name, id):
        ret = dc_wi_info.query_by_id(id)
        if ret:
            return ret
        else:
            return None

    def query_wi_by_info(self, vehicle_name, ref_wi):
        id = ref_wi.get('ID')
        truck_no = ref_wi.get('TRUCK_NO')
        wi_type = ref_wi.get('WI_TYPE')
        wi_act = ref_wi.get('WI_ACT')
        # to_pos = ref_wi.get('TO_POS')
        cancel_time = ref_wi.get('CANCEL_TIME')
        if cancel_time is None:
            logger.warning(f"[query_wi_by_info] vehicle_name:{vehicle_name},invalid cancel_time,wi id:{id}")
            return []
        ret = wis_info.query_by_info(id, truck_no, cancel_time, wi_act, wi_type)
        #and TO_POS like: to_pos-->'to_pos': '%' + to_pos + '%'
        if ret:
            return [ret]
        else:
            return []

    def finish_tos_command(self, id):
        update_column = {}
        update_column['WI_STATUS'] = 'FINISH'
        update_column['VERSION'] = int(time.time())
        # update_column['UPDATE_TIME'] = datetime.datetime.strptime(
        #         datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "%Y-%m-%d %H:%M:%S")
        logger.info(f'[finish_tos_command] update_column:{update_column}')
        wis_info.update(id, update_column)

    def update_tos_wi(self, vehicle_name, wi, id = None, wi_type = None, wi_act = None):
        if id is None:
            try:
                wi['WI_ID'] = int(time.time())
                wi['TRUCK_NO'] = vehicle_name
                wi['WI_NO'] = -2
                wi['WI_ACT'] = wi_act
                wi['CTRL_ACTION'] = wi_act
                wi['WI_TYPE'] = str(wi_type)
                wi['WI_STATUS'] = 'DISPATCH'
                wi['EQUIT_TYPE'] = ''
                wi['TRUCK_POS'] = '0'
                wi['TWIN_FLAG'] = 'N'
                wi['TWIN_WI_NO'] = -2
                wi['REMARK3'] = 'FABU'
                wi['TRUCK_SEQ'] = 0
                wi['VERSION'] = 1
                wi['LOCK_FLAG'] = 'N'
                wi['CTRL_STATUS'] = 'DISPATCH'
                wi['DISPATCH_TIME'] = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                                         "%Y-%m-%d %H:%M:%S")
                wi_record = TosWisInfo()
                for attr in dir(wi_record):
                    if not attr.startswith('_') and wi.get(attr, ""):
                        setattr(wi_record, attr, wi.get(attr))
                wis_info.insert(wi_record)
                id = wi_result.get("ID") if (wi_result := wis_info.query_by_WI_ID(vehicle_name, wi['WI_ID'])) else -1
            except Exception as e:
                logger.warning(f"[update_tos_wi] vehicle_name:{vehicle_name} fail insert wi:{wi},id:{id} error:{e}")
                id = -1
        else:
            ori_wi = self.query_wi_by_id(vehicle_name, id)
            if ori_wi is None or ori_wi.get('TRUCK_NO') != vehicle_name:
                logger.warning(f"[update_tos_wi] vehicle_name:{vehicle_name} fail query wi by id:{id},ori_wi:{ori_wi}")
                return -1
            if (wi_type is not None and wi_type != ori_wi.get('WI_TYPE')) or \
                (wi_act is not None and wi_act != ori_wi.get('WI_ACT')):
                logger.warning(f"[update_tos_wi] vehicle_name:{vehicle_name} fail to check id:{id},wi_type:{wi_type},wi_act:{wi_act},ori_wi:{ori_wi}")
                return -1
            try:
                # wi['ID'] = id
                wi['VERSION'] = ori_wi.get('VERSION') + 1
                UPDATE_TIME = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                                         "%Y-%m-%d %H:%M:%S")
                if wi.get('WI_STATUS') == 'CANCEL':
                    wi['CANCEL_TIME'] = UPDATE_TIME
                elif wi.get('WI_STATUS') == 'FINISH':
                    wi['CONFIRMED_TIME'] = UPDATE_TIME
                wis_info.update(id, wi)
            except Exception as e:
                logger.warning(f"[update_tos_wi] vehicle_name:{vehicle_name} fail update wi:{wi},id:{id} error:{e}")
                id = -1
        return id

    def update_wi(self, vehicle_name, wi, id = None, wi_type = None, wi_act = None):
        id = self.update_tos_wi(vehicle_name, wi, id, wi_type, wi_act)
        if id > 0 and wi.get("WI_STATUS") == "CANCEL":
            if self.query_dc_wi_by_id(vehicle_name, id) is None:
                logger.info(f'vehicle_name:{vehicle_name} has no dc wi:{id},now direct update truck_status cancel')
                if not self.insert_cancel_wi_status(id):
                    logger.warning(f"vehicle_name:{vehicle_name} fail to insert cancel wi status for id:{id}")
                    id = -1
        return id


    def update_tos_command(self, id, key, value, value_type = ''):
        update_column = {}
        if key == "VERSION":
            update_column["VERSION"] = int(value)
        else:
            update_column["VERSION"] = int(time.time())
            update_column[str(key)] = value
        logger.info(f'[update_tos_command] update_column:{update_column}')
        wis_info.update(id, update_column)

    def arrive_command(self, vehicle_name, id, arrive_area=''):
        logger.info(f'arrive command:vehicle_name:{vehicle_name}, id:{id}, arrive_area:{arrive_area}')
        # ToDo
        update_result = dc_wi_info.update_arrive_area_by_id(id, arrive_area)
        # update_result = False
        return update_result


    def count_tos(self,limit_num = 10):
        return dc_wi_info.count_id(limit_num)

def vehicle_status_to_dict(vehicle_status):
    vehicle_status_dict = dict()
    vehicle_status_dict['TRUCK_NO'] = vehicle_status.vehicle_name
    vehicle_status_dict['DRIVERLESS'] = 'True' if vehicle_status.driverless else 'False'
    vehicle_status_dict['POS_Y'],vehicle_status_dict['POS_X'] = from_utm_to_latlon(vehicle_status.position.utm_x,vehicle_status.position.utm_y,vehicle_status.vehicle_name)
    vehicle_status_dict['SPEED'] = vehicle_status.speed
    vehicle_status_dict['ALTITUDE'] = vehicle_status.position.latitude
    vehicle_status_dict['REST_OIL'] = 0
    vehicle_status_dict['RESET_ELECTRIC'] = vehicle_status.soc
    vehicle_status_dict['POWER_TYPE'] = 'ELECTRIC'
    vehicle_status_dict[
        'SENSOR_STATUS'] = ','.join(vehicle_status.error_sensors)
    vehicle_status_dict['ANGEL'] = ((-vehicle_status.heading * 180/math.pi) % 360 + 90) % 360

    node_path = []
    for path in vehicle_status.node_path:
        node_path.append(path)
    vehicle_status_dict['NODE_PATH'] = str(node_path)

    path1 = []
    for path in vehicle_status.path1:
        lat, lon = utm_to_latlng(path.utm_x, path.utm_y)
        node = (lat,lon)
        path1.append(node)
    vehicle_status_dict['PATH1'] = str(path1)

    path2 = []
    for path in vehicle_status.fit_route_points:
        lat, lon = utm_to_latlng(path.x, path.y)
        node = (lat,lon)
        path2.append(node)
    # 2022.02.21 save PATH2 to redis
    set_truck_info_path2_to_redis(vehicle_status.vehicle_name, str(path2))
    vehicle_status_dict['PATH2'] = str(len(path2))
    # vehicle_status_dict['PATH2'] = str(path2)
    return vehicle_status_dict


if __name__ == '__main__':
    '''
    a = TosDb().batch_query_lane_no(['CR44', 'CR45'], True)
    print(a)
    info = TosDb().query_crane_info('CR44',False)
    print(info)
    '''
    # info = TosDb().query_new_wi('AT501')
    # print(info)

    # info_id = TosDb().update_tos_wi('AT501', {'WI_STATUS': 'DISPATCH', 'TO_POS': 'leave_1', 'WI_ACT': 'LEAVE_SPACE'}, None, None, 'LEAVE_SPACE')
    # print("info_id :", info_id)
    # info_id = TosDb().update_tos_wi('AT501', {'WI_STATUS':'FINISH'}, 1040, None, 'LEAVE_SPACE')
    # print("info_id :", info_id)

    TosDb().update_crane_info({"CRANE_ID":"CR42", "LANE_NO":"5", "VESSEL_DIRECTION":"R"}, True)
    print("update_crane")
