import datetime
import json
from dataclasses import dataclass
#from dataclasses_json import dataclass_json
from common.logger import logger

def get_object_hook(cls):
    def hook(dct):
        obj = cls.__new__(cls)
        for key, value in dct.items():
            if key not in cls.__annotations__:
                continue
            typ = cls.__annotations__[key]
            if typ is int or typ is str or typ is float:
                setattr(obj, key, value)
            elif typ is datetime.datetime:
                #d = datetime.datetime.strptime(value, 'datetime.datetime(%Y, %m, %d, %H, %M, %S)')
                d = datetime.datetime.fromtimestamp(value)
                setattr(obj, key, d)
            else:
                continue
        return obj

    return hook


def from_json(cls, json_str):
    return json.loads(json_str, object_hook=get_object_hook(cls))

def make_msg(msg_type:str,msg_body:str):
    msg = VmsMsg(MSG_ID = datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
          MSG_TYPE = "PUSH",
          MSG_JOB_TYPE = msg_type,
          MSG_TIME = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3],
          MSG_SOURCE = "BLCTMS-VMS-V1",
          MSG_STATUS = "0",
          MSG_BODY = msg_body)
    return msg


@dataclass
class MsgHead:
    MSG_ID: str
    MSG_TYPE: str
    MSG_JOB_TYPE: str
    MSG_TIME: str
    MSG_SOURCE: str
    MSG_STATUS: str
    MSG_BODY: str


@dataclass
class CraneMsg:
    CRANE_ID: str
    LANE_NO: str
    VESSEL_DIRECTION: str
    BERTH: str
    DRIVE_DIRECTION: str
    BRIDGE_UP: str
    BRIDGE_DOWN: str

#@dataclass_json
@dataclass
class WIMsg:
    TRUCK_NO: str
    TRUCK_SEQ: str
    WI_NO: int
    WI_ID: int
    CTN_NO: str
    EQUIT_TYPE: str
    TEU: int
    FROM_POS: str
    TO_POS: str
    WI_TYPE: str
    WI_ACT: str
    WI_STATUS: str
    TWIN_FLAG: str
    TWIN_WI_NO: int
    TWIN_CTN_NO: str
    TRUCK_POS: str
    DISPATCH_TIME: datetime.datetime
    CANCEL_TIME: datetime.datetime
    CONFIRMED_TIME: datetime.datetime
    REMARK1: str
    REMARK2: str
    REMARK3: str
    CTN_WEIGHT: str
    POW_NAME: str
    LOCK_FLAG: str
    LOCK_PAVILION: str
    SPEED: int
    WAIT_NODE: str
    LOCK_NODE: str
    QUEUE_NODE: str
    ACTION: str
    CUR_WIS: str
    CUR_IDS: str
    VESSEL_POS: str


#@dataclass_json
@dataclass
class TruckMsg:
    TRUCK_NO: str  = ""
    POS_X: float = 0.0
    POS_Y: float  = 0.0
    ALTITUDE: float  = 0.0
    ANGEL: float  = 0.0
    CUR_POS: str = "0"
    POWER_TYPE: str  = "ELECTRIC"
    REST_OIL: float  = 0.0
    REST_ELECTRIC: float  = 0.0
    SPEED: float  = 0.0
    SENSOR_STATUS: str  = "null"
    TRUCK_MODE: int  = 2
    ACTION: str  = "IDEL"
    CUR_WIS: str  = "0"
    CUR_IDS: str = "0"
    TRUCK_STATUS: str  = "0"

@dataclass
class TruckRequestMsg:
    TRUCK_NO: str = "0"
    REQUEST_TYPE: str = "0"
    GEN_TIME: str = "0"

#@dataclass_json
@dataclass
class ControlMsg:
    TRUCK_NO: str
    SPEED: int
    STOP_NODE: str
    WAIT_NODE: str
    LOCK_NODE: str
    QUEUE_NODE: str
    TURN_NODE: str
    CHECK_NODE: str
    ACTION: str
    CUR_WIS: str
    CUR_IDS: str


@dataclass
class VmsMsg:
    MSG_ID: str
    MSG_TYPE: str
    MSG_JOB_TYPE: str
    MSG_TIME: str
    MSG_SOURCE: str
    MSG_STATUS: str
    MSG_BODY: str


if __name__ == '__main__':

    msg = b'{"MSG_ID": "20210508124034", "MSG_TYPE": "PUSH", "MSG_JOB_TYPE": "VMS_CRANE",\
    "MSG_TIME": "20210508124034817", "MSG_SOURCE": "BLCTMS-VMS-V1", "MSG_STATUS": "0",\
    "MSG_BODY": {"ID": 1, "CRANE_ID": "CR38", "LANE_NO": null, "BAY1": "70", "BAY2": "77",\
    "BAY3": "79", "VESSEL_DIRECTION": "R","INSERT_TIME":"datetime.datetime(2021, 5, 7, 14, 59, 16)"}}'

    value_list = json.loads(msg)
    logger.info(f'value_list:{value_list}')
    logger.info(f'''value_list['MSG_BODY']:{value_list.get('MSG_BODY')},type(value_list['MSG_BODY']):{type(value_list['MSG_BODY'])}''')
    #msg_value = from_json(VmsMsg, msg)
    #logger.info(f'MSG_BODY:{type(msg_value.MSG_BODY)},MSG_JOB_TYPE:{type(msg_value.MSG_JOB_TYPE)}')
