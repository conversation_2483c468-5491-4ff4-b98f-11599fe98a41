db:
  mysql:
    tos_mysql:
      mode: remote
      dsn: mysql://root:fabu124@************:3306/antenna_tos_cloud_prodprev?charset=utf8
    data_mysql:
      dsn: mysql://root:fabu124@************:3306/yz_port_prodprev?charset=utf8
    chassis_mysql:
      dsn: mysql://root:fabu124@************:3306/yz_port_prodprev_chassis?charset=utf8

  redis:
    mode: cluster
    sentinel:
      master: antenna_server
      nodes:
        - ************:16982
        - ************:16983
        - ************:16984
    # 0(pro)、8(pre),15(fat),iecs(6)
    database: 10
    # share_database生产(pro)、预发(pre)用0,其他模式(DEV、FAT)下与database保持一致
    share_database: 10
    # plc_share_database 在portman及DEV模式下与database保持一致,在生产(pro)、预发(pre)、测试(FAT)模式下用8
    plc_share_database: 10
    password: fabu123!@#

  # 强控redis实例配置，当前只支持单节点
  pscp_redis:
    ip: ************
    port: 6379
    database: 5
    password:

  kafka:
      producer_enable: True
      consumer_enable: True
      group_id: FABU_FMS_CS_PRE
      sasl_config:
        sasl_enable: True
        sasl_plain_username: FabuAi
        sasl_plain_password: yzct#Fabu
        security_protocol: SASL_PLAINTEXT
        sasl_mechanism: SCRAM-SHA-256
      nodes:
        - *************:9092
        - *************:9092
        - *************:9092

  inner_kafka:
    producer_enable: True
    consumer_enable: True
    group_id: FABU_FMS_CS_PRE_INNER
    sasl_config:
      sasl_enable: True
      sasl_plain_username: FabuAi
      sasl_plain_password: yzct#Fabu
      security_protocol: SASL_PLAINTEXT
      sasl_mechanism: SCRAM-SHA-256
    nodes:
      - *************:9092
      - *************:9092
      - *************:9092

service:
  scheduler_server:
    ip: ***********
    port: 6170
  qcms_server:
    ip: ************
    port: 8090
  hcms_server:
    ip: ************
    port: 8090
  hdmap_server:
    ip: ************
    port: 22022

server_attribute:
  #如果是非分布式为默认为Node1-1;分布式主节点为'Node1-1';其他节点格式为'Node[major_num]-[minor_num]',如Node1-2
  #1.cluster_[]:集群,可以与其他多节点同时运行tos_bridge
  #  (1):cluster_master:集群主节点,除了启动tos_bridge,同时启动其他服务
  #  (2):cluster_slave:集群子节点,只启动tos_bridge服务
  #  (3):cluster_back:备节点,当cluster_master所在服务主机挂掉时，所有服务会被cluster_back接管
  #2.single:只单机运行tos_bridge(注意redis的database其他服务没有在用),同时运行其他服务
  nodes:
    -
      node_name: Node1-1
      mode: single

modules:
  timing_record:
    start: false
  vehicle_info:
    start: true
    publisher: true
