from cache import command_cache
from cache.command_cache import CommandCache
from cache.lock_cache import LockCache
from common.logger import logger
from proto.antenna_pb2 import RemoteCommand
from tos_bridge import TosBridge


def auto_lock(vehicle_name):
    commands = command_cache.CommandCache().query_commands(vehicle_name)
    description = ''
    if len(commands) == 0:
        description = '车辆 {} 当前无指令，无法模拟装卸锁状态'.format(vehicle_name)
        logger.info(f'auto_lock: {description}')
        return False, description

    # 检查指令是否有效
    command = commands[0].command
    command_context = command.command_context
    lock_container_id = command_context.lock_container_id
    lock_flag = command_context.lock_flag
    if str(lock_container_id[0]) == '-1':
        description = '车辆 {} 指令号为-1，暂时无法模拟卸锁状态，请等待下条指令重新执行'.format(vehicle_name)
        logger.info(f'auto_lock: {description}')
        return False, description

    # 检查指令内容
    except_lock_flag = ''
    lock_type = ''
    if LockCache().need_lock(lock_flag):
        except_lock_flag = 'True'
        lock_type = '装锁'
    elif LockCache().need_unlock(lock_flag):
        except_lock_flag = 'False'
        lock_type = '卸锁'
    else:
        description = '车辆 {} 当前指令不需要装卸锁，请检查指令'.format(vehicle_name)
        logger.info(f'auto_lock: {description}')
        return False, description

    # 检查指令阶段
    if command.type == RemoteCommand.WAIT_LOCK_OFF or command.type == RemoteCommand.WAIT_UNLOCK_OFF:
        description = '车辆 {} 已到达锁亭，正在等待拍按纽确认，无法执行，请人工确认'.format(vehicle_name)
        logger.info(f'auto_lock: {description}')
        return False, description

    # 模拟装卸锁
    LockCache().update_container_lock_is_exist(str(lock_container_id[0]), except_lock_flag)
    if len(lock_container_id) == 2:
        LockCache().update_container_lock_is_exist(str(lock_container_id[1]), except_lock_flag)

    # reset指令
    tos_command_ids = []
    if command.type != RemoteCommand.CANCEL:
        CommandCache().cancel_all(vehicle_name)
        for info in commands[0].tos_command_info:
            logger.info(f"auto_lock: reset command id:{info.db_id}")
            tos_command_ids.append(info.db_id)
            TosBridge().reset_command_with_mode(vehicle_name, info.db_id, True)
    else:
        logger.warning(f"auto_lock: now cancel,no need reset")
        for info in commands[0].tos_command_info:
            logger.warning(f"auto_lock: now cancel,no need reset command id:{info.db_id}")
            tos_command_ids.append(info.db_id)
    # 回馈云控
    description = '车辆 {} 模拟 {}成功，指令 {} 已重新执行，请确认是否生效'.format(vehicle_name, lock_type, tos_command_ids)
    logger.info(f'auto_lock: {description}')
    return True, description


if __name__ == '__main__':
    auto_lock('AT800')
