import datetime
from enum import Enum
from typing import Optional
import sqlalchemy
from sqlalchemy import Column, String, TIMESTAMP, Integer
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession
from config.config_manage import ConfigManage

Base = declarative_base()

class AntiFatigueResult(Base):
    __tablename__ = 'anti_fatigue_result'

    id = Column('id', Integer, primary_key=True, autoincrement=True)
    vehicle_name = Column('vehicle_name', String)
    driver_id = Column('driver_id', Integer)
    driver_name = Column('driver_name', String)
    driver_license = Column('driver_license', String)
    time_sec = Column('time_sec', Integer)
    anti_fatigue_type = Column('anti_fatigue_type', String)
    anti_fatigue_type_ret = Column('anti_fatigue_type_ret', String)
    create_time = Column('create_time', TIMESTAMP)

def is_support():
    return True

def insert(vehicle_name, driver_id, driver_name, driver_license, time_sec, anti_fatigue_type, anti_fatigue_type_ret) -> bool:
    if not is_support():
        return True
    session = MysqlSession().acquire()
    try:
        anti_fatigue_result = AntiFatigueResult(vehicle_name=vehicle_name,
                             driver_id=driver_id,
                             driver_name=driver_name,
                             driver_license=driver_license,
                             time_sec=time_sec,
                             anti_fatigue_type=anti_fatigue_type,
                             anti_fatigue_type_ret=anti_fatigue_type_ret,
                             create_time=datetime.datetime.now())
        logger.info(f"Insert AntiFatigueResult :{anti_fatigue_result.__dict__}")
        session.add(anti_fatigue_result)
        session.commit()
        return True
    except Exception as e:
        logger.warning(f"Insert AntiFatigueResult err{e}")
        return False
    finally:
        session.close()