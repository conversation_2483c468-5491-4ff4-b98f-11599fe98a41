#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

source $DOCKER_PATH/common.sh
echo "DOCKER_NAME：$DOCKER_NAME"
echo "SIM_MODE: $SIM_MODE"

# check docker image
if [[  -z $(docker images -q ${IMG}) ]]; then
  echo "----------pull image: ${IMG}"
  docker pull $IMG
fi

# stop antenna-server docker
docker ps -a --format "{{.Names}}" | grep "${DOCKER_NAME}" 1>/dev/null
if [ $? == 0 ]; then
docker stop ${DOCKER_NAME} 1>/dev/null
docker rm -f ${DOCKER_NAME} 1>/dev/null
fi

mkdir -p /tmp/.ssh-agent-$USER 2>&1 >/dev/null

if [ $START_ORACLE_DB == "true" ]; then
  $DOCKER_PATH/db_start.sh
fi

if [ $START_MYSQL_DB == "true" ]; then
  $DOCKER_PATH/db_mysql_start.sh
fi

# functions
function local_volumes() {
  volumes="
           -v $HOME/.ssh:${DOCKER_HOME}/.ssh \
           -v $HOME/.zsh_history:${DOCKER_HOME}/.zsh_history \
           -v $HOME/.cache:${DOCKER_HOME}/.cache \
           -v /tmp/core:/tmp/core \
           -v /proc/sys/kernel/core_pattern:/tmp/core_pattern:rw \
           -v /media:/media \
           -v /private:/private \
           -v /onboard_data:/onboard_data \
	         -v /etc/localtime:/etc/localtime:ro \
           -v /tmp/.ssh-agent-$USER:/tmp/.ssh-agent-$USER \
           -v ${DOCKER_PATH}/master.txt:/tmp/check_master.info
          "

  volumes="${volumes} -v ${LOCAL_DIR}:/antenna-server"

  echo "${volumes}"
}

function check_port() {
  docker port ${DOCKER_NAME} | sed "s/6789[^:]*/grpc/" | sed "s/6788[^:]*/fast-grpc/" | sed "s/6790[^:]*/web/" | \
                               sed "s/2000[^:]*/gantry/" | sed "s/1234[^:]*/crane/" | sed "s/2090[^:]*/change/"
}


# docker create
USER_ID=$(id -u)
GRP=$(id -g -n)
GRP_ID=$(id -g)
LOCAL_HOST=$(hostname)
DOCKER_HOME="/home/<USER>"
if [ "$USER" == "root" ]; then
  DOCKER_HOME="/root"
fi

DOCKER_CMD="nvidia-docker"
if ! [ -x "$(command -v ${DOCKER_CMD})" ]; then
  DOCKER_CMD="docker"
fi

PORTS_CONFIG="
              -p ${DISPATCH_ADDRESS_PORT}:6789
              -p ${FAST_DISPATCH_ADDRESS_PORT}:6788
              -p ${DEBUG_FAST_DISPATCH_ADDRESS_PORT}:6000
              -p ${PLC_CRANE_PORT}:2000
              -p ${PLC_GANTRY_PORT}:1234
              -p ${CHANGE_PORT}:2090
              -p ${WEB_PORT}:6790
             "

SIM_CONFIG=""
if [ "$SIM_MODE" == "true" ]; then
  SIM_CONFIG="
              -e SIM_MODE=$SIM_MODE \
             "
  GPU_CONFIG=""
  PORTS_CONFIG="${PORTS_CONFIG}
                -p ${NT_PLC_GANTRY_PORT}:2702
                -p ${SSH_PORT}:2222
               "
fi

HOST_NET_MODE=""
if [ "$HOST_NET_MODE_ENABLE" == "true" ]; then
  PORTS_CONFIG=""
  HOST_NET_MODE=" --net=host"
fi

GPU_CONFIG=""
if [ "$DOCKER_CMD" == "docker" ]; then
  # 仅线上环境需要gpu支持
  if [ "$ENVIRONMENT" ==  "FAT" -o "$ENVIRONMENT" ==  "PRE" -o "$ENVIRONMENT" ==  "PRO" ]; then
    new_docker=$(echo "$(docker -v | cut -c 16-20) >= 19.03" | bc)
    if [ "$new_docker" == "1" ]; then
      GPU_CONFIG="--gpus all"
    fi
  fi
fi

#--privileged
eval ${DOCKER_CMD} create -it \
    --name ${DOCKER_NAME} \
    -e DOCKER_USER=$USER \
    -e USER=$USER \
    -e DOCKER_USER_ID=$USER_ID \
    -e DOCKER_GRP=$GRP \
    -e DOCKER_GRP_ID=$GRP_ID \
    -e DOCKER_HOME=$DOCKER_HOME \
    -e DOCKER_NAME=$DOCKER_NAME \
    -e DOCKER_HOST_PATH=$(pwd) \
    -e PYTHONPATH=. \
    -e ANTENNA_DB_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DB_DOCKER_NAME}) \
    -e ANTENNA_MYSQL_DB_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${MYSQL_DB_DOCKER_NAME}) \
    -e ANTENNA_DB_SID=XE \
    -e ANTENNA_MYSQL_DB_SID=antenna_tos \
    -e ANTENNA_MYSQL_DB_PORT=3306 \
    -e DEV_ID=$(date | md5sum | cut -f 1 -d ' ') \
    -e SSH_AUTH_SOCK=/tmp/.ssh-agent-$USER/agent.sock \
    -e ENVIRONMENT=$ENVIRONMENT \
    $(local_volumes) \
    --ulimit core=-1 \
    -w /antenna-server \
    --dns=*************** \
    --add-host in_antenna_dev_docker:127.0.0.1 \
    --add-host ${LOCAL_HOST}:127.0.0.1 \
    --hostname in_antenna_dev_docker \
    $GPU_CONFIG \
    $SIM_CONFIG \
    $PORTS_CONFIG \
    $HOST_NET_MODE \
    -v ${DOCKER_PATH}/entrypoint.sh:/tmp/entrypoint.sh \
    --entrypoint /tmp/entrypoint.sh \
    --cap-add=SYS_PTRACE \
    $IMG /bin/bash

docker cp -L ~/.gitconfig ${DOCKER_NAME}:${DOCKER_HOME}/.gitconfig
docker start ${DOCKER_NAME}
check_port
echo "ip:$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DOCKER_NAME})"
docker exec ${DOCKER_NAME} chown ${USER_ID}:${GRP_ID} /antenna-server
