from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from common.logger import logger
from common.singleton import Singleton
from config.config_manage import ConfigManage


class MysqlSessionBase(object):
    def __init__(self, mysql_dsn, pool_size, max_overflow=5, pool_recycle=10800, pool_pre_ping=True,
                 max_identifier_length=30, echo=False):
        self.__mysql_engine = create_engine(mysql_dsn, pool_size=pool_size, max_overflow=max_overflow,
                                            pool_recycle=pool_recycle,
                                            pool_pre_ping=pool_pre_ping, max_identifier_length=max_identifier_length,
                                            echo=echo)
        self.__mysql_session = sessionmaker(bind=self.__mysql_engine)

    def acquire(self):
        return self.__mysql_session()

    def engine(self):
        return self.__mysql_engine


class MysqlSession(MysqlSessionBase, metaclass=Singleton):
    def __init__(self):
        mysql_dsn = ConfigManage().get_config_data_mysql_dsn()
        pool_size = ConfigManage().get_config_data_mysql_pool_size()
        max_overflow = ConfigManage().get_config_data_mysql_max_overflow()
        logger.info(f'mysql dsn: {mysql_dsn},pool_size:{pool_size},max_overflow:{max_overflow}')
        MysqlSessionBase.__init__(self, mysql_dsn=mysql_dsn, pool_size=pool_size, max_overflow=max_overflow)


class MysqlRemoteSession(MysqlSessionBase, metaclass=Singleton):
    def __init__(self):
        mysql_dsn = ConfigManage().get_config_monitor_mysql_dsn()
        pool_size = ConfigManage().get_config_monitor_mysql_pool_size()
        max_overflow = ConfigManage().get_config_monitor_mysql_max_overflow()
        logger.info(f'remote mysql dsn: {mysql_dsn},pool_size:{pool_size},max_overflow:{max_overflow}')
        MysqlSessionBase.__init__(self, mysql_dsn=mysql_dsn, pool_size=pool_size, max_overflow=max_overflow)


class ChassisMysqlSession(MysqlSessionBase, metaclass=Singleton):
    def __init__(self):
        mysql_dsn = ConfigManage().get_config_chassis_mysql_dsn()
        pool_size = ConfigManage().get_config_chassis_mysql_pool_size()
        max_overflow = ConfigManage().get_config_chassis_mysql_max_overflow()
        logger.info(f'chassis mysql dsn: {mysql_dsn},pool_size:{pool_size},max_overflow:{max_overflow}')
        MysqlSessionBase.__init__(self, mysql_dsn=mysql_dsn, pool_size=pool_size, max_overflow=max_overflow)


class TosMysqlSession(MysqlSessionBase, metaclass=Singleton):
    def __init__(self):
        mysql_dsn = ConfigManage().get_config_tos_mysql_dsn()
        pool_size = ConfigManage().get_config_tos_mysql_pool_size()
        max_overflow = ConfigManage().get_config_tos_mysql_max_overflow()
        logger.info(f'tos mysql dsn: {mysql_dsn},pool_size:{pool_size},max_overflow:{max_overflow}')
        MysqlSessionBase.__init__(self, mysql_dsn=mysql_dsn, pool_size=pool_size, max_overflow=max_overflow)


class PlcMysqlSession(MysqlSessionBase, metaclass=Singleton):
    def __init__(self):
        mysql_dsn = ConfigManage().get_config_plc_mysql_dsn()
        pool_size = ConfigManage().get_config_plc_mysql_pool_size()
        max_overflow = ConfigManage().get_config_plc_mysql_max_overflow()
        logger.info(f'plc mysql dsn: {mysql_dsn},pool_size:{pool_size},max_overflow:{max_overflow}')
        MysqlSessionBase.__init__(self, mysql_dsn=mysql_dsn, pool_size=pool_size, max_overflow=max_overflow)

"""
# 不安全的使用, 有crash的风险
class OracleSessionBase(object):
    def __init__(self, oracle_dsn, max_identifier_length):
        oracle_engine = create_engine(oracle_dsn, max_identifier_length=max_identifier_length, echo=False)
        self.tos_session = sessionmaker(bind=oracle_engine)

    def acquire(self):
        return self.tos_session()

class TosOracleSession(OracleSessionBase, metaclass=Singleton):
    def __init__(self):
        oracle_dsn = ConfigManage().get_config_oracle_dsn()
        max_identifier_length = ConfigManage().get_config_oracle_max_connections()
        OracleSessionBase.__init__(self, oracle_dsn = oracle_dsn, max_identifier_length = max_identifier_length)
"""

# def init_pool():
#     # close all the old connection in new processr
#     # https://docs.sqlalchemy.org/en/14/core/pooling.html#using-connection-pools-with-multiprocessing-or-os-fork
#     gis_engine.dispose()
#     tos_engine.dispose()
#     mysql_engine.dispose()


if __name__ == '__main__':
    data_sql = MysqlSession()
    monitor_sql = MysqlRemoteSession()
    chassis_sql = ChassisMysqlSession()
    tos_sql = TosMysqlSession()
