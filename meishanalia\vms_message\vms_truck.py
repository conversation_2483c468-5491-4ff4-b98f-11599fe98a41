import json
from dataclasses import dataclass
#from dataclasses_json import dataclass_json
import datetime
import time
import json
from enum import Enum
from meishanalia.common.constant import VMS_TRUCK_TYPE
from common.logger import logger
from meishanalia.vms_message.msgs import from_json, TruckMsg, MsgHead, make_msg
from meishanalia.model import truck_info
from meishanalia.model import table_info


class TruckEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj,datetime.datetime):
            return obj.strftime("datetime.datetime(%Y, %m, %d, %H, %M, %S)")
        else:
            return json.JSONEncoder.default(self,obj)

def is_need_update(truck):
    #return True

    if truck.UPDATE_TIME >= (datetime.datetime.now() - datetime.timedelta(minutes=1,seconds=10)):
        return True
    else:
        return False


def vms_truck_handler():
    truck_msgs = list()
    all_truck = truck_info.query_all()
    for truck in all_truck:
        if truck is not None and is_need_update(truck):
            truck_msg = TruckMsg()
            for attr in truck_msg.__dict__:
                if hasattr(truck, attr.lower()):
                    setattr(truck_msg, attr, getattr(truck, attr.lower()))
            msg = make_msg(VMS_TRUCK_TYPE,truck_msg.__dict__)
            msg_json = json.dumps(msg.__dict__,cls=TruckEncoder).encode('utf-8')
            key_json = truck.TRUCK_NO.encode('utf-8')
            logger.debug(f'key_json:{key_json},msg_json:{msg_json},truck:{truck.__dict__}')
            truck_msgs.append((key_json,msg_json))
    return truck_msgs



if __name__ == '__main__':
    record = table_info.TruckInfo()
    args_table = {'truck_no': 'AT800', 'pos_x': 122.007, 'pos_y': 29.7812, 'altitude': 0.0, 'angel': 46.4552, 'cur_pos': '0', 'power_type':'ELECTRIC', 'rest_oil': 0.0, 'rest_electric': 0.0,\
      'speed': 0.0, 'sensor_status': '1', 'truck_mode': 1, 'action': 'UNLOAD', 'CUR_WIS':'538892', 'truck_status': '2'}
    logger.info('update_vehicle_status: ' + str(args_table))
    record.from_dict(args_table)
    truck_info.insert(record)
    while True:
        msgs = vms_truck_handler()
        for msg in msgs:
            msg_key = msg[0]
            msg_context = msg[1]
            logger.info(f'msg_key:{msg_key},msg_context:{msg_context}')
        time.sleep(1)
        break
