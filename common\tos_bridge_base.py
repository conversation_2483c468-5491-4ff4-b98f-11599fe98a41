import datetime
import math
import time
import traceback
import uuid
from typing import List, Optional

import google.protobuf.text_format as text_format
from google.protobuf import json_format

from cache import vehicle_cache
from common.common_util import is_off_work_wi
from common.logger import logger
from common.name_converter import NameConverter
from proto.antenna_pb2 import (
    CommandStatus,
    RemoteCommand,
    StopCommand,
    VehicleOperationMode,
)
from proto.cache_pb2 import AlarmEvent, CommandWithStatus
from config.config_manage import ConfigManage
from yongzhou.model import crane_info


class TosBridgeBase:
    def __init__(self):
        self._db = None
        self._command_cache = None
        self._run_event_cache = None
        self._run_event = dict()
        self._run_event_interval = 5 * 60.0 * 1000  # 5m

    def _make_run_event(self, vehicle_name, error_code, error_info):
        if self._run_event_cache is None:
            logger.warning(f"invalid run_event_cache")
            return
        try:
            error_time = time.time() * 1000
            if error_time - self._run_event.get(vehicle_name, 0) > self._run_event_interval:
                run_event = AlarmEvent()
                run_event.vehicle_name = vehicle_name
                run_event.timestamp_ms = error_time
                run_event.timestamp = datetime.datetime.fromtimestamp(int(error_time / 1000)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                run_event.event_code = "tos_bridge"
                run_event.content = error_info
                run_event.event_content = error_code
                self._run_event[vehicle_name] = error_time
                self._run_event_cache.lpush_event([run_event])
                logger.debug(
                    f"vehicle_name:{vehicle_name} run_event:{text_format.MessageToString(run_event, as_one_line=True)}"
                )
            else:
                logger.debug(
                    f"vehicle_name:{vehicle_name} not in time,error_time:{error_time},"
                    f"last error time:{self._run_event.get(vehicle_name, 0)}"
                )
        except Exception as e:
            logger.error(f"truck_no:{vehicle_name} fail to make run event,e:{e}, trace:{traceback.format_exc()}")

    def _wrap_command_(self, remote_command):
        command_with_status = CommandWithStatus()
        command_with_status.uuid = remote_command.uuid
        command_with_status.command.CopyFrom(remote_command)
        command_with_status.status = CommandStatus.WAIT
        return command_with_status

    def insert_remote_command(self, vehicle_name, remote_command: RemoteCommand):
        remote_command.uuid = str(uuid.uuid1())
        remote_command.command_source_type = RemoteCommand.CommandSourceType.REMOTE
        command = self._wrap_command_(remote_command)

        stop_command = RemoteCommand()
        stop_command.command_source_type = RemoteCommand.CommandSourceType.REMOTE
        stop_command.uuid = str(uuid.uuid1())
        stop_command.type = RemoteCommand.STOP
        stop_command = self._wrap_command_(stop_command)

        if command.command.type == RemoteCommand.CANCEL:
            self._command_cache.cancel_all(vehicle_name)
            self._command_cache.append_commands(vehicle_name, [stop_command])
        elif command.command.type == RemoteCommand.STOP:
            self._command_cache.cancel_all(vehicle_name)
            self._command_cache.append_commands(vehicle_name, [command])
        else:
            stop_command.command.stop_command.stop_position = StopCommand.ROUTE_POSITION
            if command.command.type == RemoteCommand.MOVE_TO_POSITION:
                command.command.command_context.dest_type = RemoteCommand.CommandContext.DEST_TEST
                command.command.command_context.remote_command_source = RemoteCommand.CommandContext.TEST
                pow_name = command.command.move_to_position_command.pow_name
                if pow_name and (ConfigManage().is_yz_scene() or ConfigManage().is_yz_cs_scene()):
                    info = crane_info.get_by_crane_no(pow_name)
                    command.command.command_context.bridge_up = str(info.get('BRIDGE_UP'))
                    command.command.command_context.bridge_down = str(info.get('BRIDGE_DOWN'))
            self._command_cache.append_commands(vehicle_name, [stop_command, command])
        return True

    def reset_command_with_mode(self, vehicle_name: str, id: int, reset_mode: bool, need_route=False):
        if self._db:
            self._db.reset_command(vehicle_name, id)
        else:
            logger.warning(f"invalid db")
        return

    def reset_command(self, vehicle_name: str, id: int):
        if self._command_cache is None or self._db is None:
            logger.warning(f"invalid db or command cache")
            return False
        ret = False
        can_reset = False
        wi = self._db.query_wi_by_id(vehicle_name, id)
        if wi is not None:
            if is_off_work_wi(wi):
                tos_id = self._command_cache.get_current_tos_id(vehicle_name)
                if tos_id == id:
                    can_reset = True
                else:
                    logger.warning(
                        f"[ResetTosCommand] vehicle_name:{vehicle_name} command_id:{id}"
                        f",refused to reset for off work wi and not executing"
                    )
            else:
                can_reset = True
        else:
            logger.warning(f"[ResetTosCommand] vehicle_name:{vehicle_name} fail to get wi by command_id:{id}")
        if can_reset:
            tos_command_ids = []
            commands = self._command_cache.query_commands(vehicle_name)
            if len(commands) > 0:
                for info in commands[0].tos_command_info:
                    tos_command_ids.append(info.db_id)
            tos_command_ids = tos_command_ids if id in tos_command_ids else [id]
            logger.info(
                f"[ResetTosCommand] vehicle_name:{vehicle_name} ready to reset command_id:{id}"
                f",now reset command_ids:{tos_command_ids}"
            )
            self._command_cache.cancel_all(vehicle_name)
            for tos_command_id in tos_command_ids:
                self.reset_command_with_mode(vehicle_name, tos_command_id, True)
            ret = True
        return ret

    def check_update_tos_commands(self, vehicle_name, update_tos: list, mode=0):
        if mode == 0 and len(vehicle_name) > 0 and not NameConverter().is_debug_truck_no(vehicle_name):
            logger.warning(f"not support vehicle_name:{vehicle_name}")
            return False
        for tos in update_tos:
            key = tos.get("key_name")
            if mode == 1 and key not in [
                "C_TO_POS",
                "C_TRUCK_POS",
                "C_LOCK_FLAG",
                "C_TRUCK_SEQ",
                "C_TWIN_TYPE",
                "C_LOCK_PAVILION",
                "C_CANCLE_BUFFER",
                "C_MANUAL_LANE_NO",
            ]:
                logger.warning(f"not support key:{key},tos:{tos}")
                return False
        return True

    def update_work_tos_commands(self, vehicle_name, update_tos: list, source):
        ret_dict = {"ret": False, "error_code": 1}
        key = update_tos[0].get("key_name")
        if key == "C_TRUCK_POS":
            ret_dict = self.update_truck_pos(vehicle_name, update_tos)
        elif key == "C_LOCK_FLAG" or key == "C_LOCK_PAVILION":
            ret_dict = self.update_truck_lock(vehicle_name, update_tos)
        elif key == "C_TRUCK_SEQ":
            ret_dict = self.update_truck_seq(vehicle_name, update_tos)
        elif key == "C_TO_POS":
            ret_dict = self.update_truck_to_pos(vehicle_name, update_tos)
        elif key == "C_CANCLE_BUFFER":
            ret_dict = self.update_schedule_buffer(vehicle_name, update_tos)
        elif key == "C_TWIN_TYPE":
            pass
        elif key == "C_MANUAL_LANE_NO":
            ret_dict = self.update_lane_no(vehicle_name, update_tos)
        return ret_dict

    def update_analog_tos_commands(self, vehicle_name, update_tos: list, source):
        update_list = list()
        for tos in update_tos:
            id = int(tos.get("id"))
            key_name = tos.get("key_name")
            key_value = tos.get("key_value")
            update_list.append((id, key_name, key_value, ""))
        ret = self._db.update_tos_commands(update_list)
        ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
        return ret_dict

    def update_tos_commands(self, vehicle_name, update_tos: list, mode=0, source=0):
        ret_dict = {"ret": False, "error_code": 1}
        if len(update_tos) == 0:
            logger.warning(f"[update_tos_commands]:vehicle_name:{vehicle_name} no update_tos")
            return ret_dict
        if not self.check_update_tos_commands(vehicle_name, update_tos, mode):
            logger.warning(f"fail to check update tos command")
            return ret_dict
        if mode == 0:
            ret_dict = self.update_analog_tos_commands(vehicle_name, update_tos, source)
        elif mode == 1:
            ret_dict = self.update_work_tos_commands(vehicle_name, update_tos, source)
        else:
            pass
        return ret_dict

    def update_truck_seq(self, vehicle_name, update_tos):
        logger.info(f"update_truck_seq:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        ret_dict = {"ret": False, "error_code": 1}
        for tos in update_tos:
            truck_seq = tos.get("key_value")
            if truck_seq not in ["0", "100", ""]:
                logger.warning(f"update_truck_seq:vehicle_name:{vehicle_name} invalid truck_seq:{truck_seq}")
                return ret_dict
        commands = self._command_cache.query_commands(vehicle_name)
        if len(commands) > 0:
            command = commands[0].command
            command_context = command.command_context
            logger.info(
                f"vehicle_name:{vehicle_name},action_type:{command_context.action_type},wi_type:{command_context.wi_type}"
                f",tos_command_id:{command_context.tos_command_id}"
            )
            if (
                command_context.action_type == RemoteCommand.CommandContext.UNLOAD
                and command_context.wi_type == RemoteCommand.CommandContext.WI_LOAD
            ):
                ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
                ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
            else:
                logger.warning(f"invalid condition:vehicle_name:{vehicle_name}")
                ret_dict = {"ret": False, "error_code": 2}
        else:
            logger.warning(f"vehicle_name:{vehicle_name} has no commands")
        return ret_dict

    def update_truck_pos(self, vehicle_name, update_tos):
        id = int(update_tos[0].get("id"))
        truck_pos = update_tos[0].get("key_value")
        logger.info(f"update_truck_pos:vehicle_name:{vehicle_name},id:{id},truck_pos_mode:{truck_pos}")
        ret_dict = {"ret": False, "error_code": 1}
        value = str(truck_pos)
        if value not in ["M", ""]:
            logger.warning(f"invalid truck_pos_mode:vehicle_name:{vehicle_name},id:{id},truck_pos_mode:{value}")
            return ret_dict
        commands = self._command_cache.query_commands(vehicle_name)
        if len(commands) > 0:
            command = commands[0].command
            command_context = command.command_context
            logger.info(
                f"vehicle_name:{vehicle_name},tos_command_id:{command_context.tos_command_id},id:{id},"
                f"wi_type:{RemoteCommand.CommandContext.WiType.Name(command_context.wi_type)},"
                f"action_type:{RemoteCommand.CommandContext.ActionType.Name(command_context.action_type)},"
                f"command type:{RemoteCommand.CommandType.Name(command.type)}"
            )
            if (
                command_context.action_type == RemoteCommand.CommandContext.LOAD
                and command_context.wi_type == RemoteCommand.CommandContext.WI_DSCH
                and command_context.tos_command_id == id
            ):
                if command.type == RemoteCommand.MOVE_TO_CRANE or command.type == RemoteCommand.STOP:
                    ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
                    ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
                else:
                    logger.warning(f"invalid condition:vehicle_name:{vehicle_name},id:{id}")
                    ret_dict = {"ret": False, "error_code": 2}
            else:
                logger.warning(f"invalid condition:vehicle_name:{vehicle_name},id:{id}")
                ret_dict = {"ret": False, "error_code": 2}
        else:
            logger.warning(f"vehicle_name:{vehicle_name} has no commands")
        return ret_dict

    def update_truck_lock(self, vehicle_name, update_tos):
        logger.info(f"update_truck_lock:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        ret_dict = {"ret": False, "error_code": 1}
        for tos in update_tos:
            if tos.get("key_name") == "C_LOCK_FLAG" and tos.get("key_value") not in [
                "N",
                "A",
                "B",
                "O",
                "I",
                "X",
                "U",
                "",
            ]:
                logger.warning(f"vehicle_name:{vehicle_name} invalid key_value:{tos}")
                return ret_dict
        ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
        ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
        return ret_dict

    def update_truck_to_pos(self, vehicle_name, update_tos):
        logger.info(f"update_truck_to_pos:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        ret_dict = {"ret": False, "error_code": 1}
        ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
        ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
        return ret_dict

    def update_schedule_buffer(self, vehicle_name, update_tos):
        logger.info(f"update_schedule_buffer:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        ret_dict = {"ret": False, "error_code": 1}
        for tos in update_tos:
            buffer_grade = tos.get("key_value")
            if buffer_grade not in ["NO_BUFFER", ""]:
                logger.warning(
                    f"update_schedule_buffer:vehicle_name:{vehicle_name} invalid buffer_grade:{buffer_grade}"
                )
                return ret_dict
        commands = self._command_cache.query_commands(vehicle_name)
        if len(commands) > 0:
            command = commands[0].command
            command_context = command.command_context
            logger.info(
                f"vehicle_name:{vehicle_name},action_type:{command_context.action_type},wi_type:{command_context.wi_type}"
                f",tos_command_id:{command_context.tos_command_id}"
            )
            if (
                command_context.action_type == RemoteCommand.CommandContext.UNLOAD
                and command_context.wi_type == RemoteCommand.CommandContext.WI_LOAD
            ) or (
                command_context.action_type == RemoteCommand.CommandContext.LOAD
                and command_context.wi_type == RemoteCommand.CommandContext.WI_DSCH
            ):
                ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
                ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
            else:
                logger.warning(f"invalid condition:vehicle_name:{vehicle_name}")
                ret_dict = {"ret": False, "error_code": 2}
        else:
            logger.warning(f"vehicle_name:{vehicle_name} has no commands")
        return ret_dict

    def update_lane_no(self, vehicle_name, update_tos):
        logger.info(f"update_lane_no:vehicle_name:{vehicle_name},update_tos:{update_tos}")
        ret_dict = {"ret": False, "error_code": 1}
        commands = self._command_cache.query_commands(vehicle_name)
        if len(commands) > 0:
            command = commands[0].command
            if command.type in (
                RemoteCommand.MOVE_TO_CRANE,
                RemoteCommand.MOVE_TO_LOCK,
            ):
                ret = self._command_cache.update_tos_command(vehicle_name, update_tos)
                ret_dict = {"ret": ret, "error_code": (0 if ret else 1)}
            else:
                logger.warning(f"invalid command type: {vehicle_name}, {command.type}")
        else:
            logger.warning(f"vehicle_name:{vehicle_name} has no commands")
        return ret_dict

    def insert_move_command(self, vehicle_name, move_command):
        logger.info(f"insert_move_command ignored: vehicle_name:{vehicle_name},move_command:{move_command}")
        return False

    def cancel_move_command(self, vehicle_name):
        logger.info(f"cancel_move_command ignored: vehicle_name:{vehicle_name}")
        return False

    def update_vehicle_mode(self, vehicle_name: str, operation_mode: VehicleOperationMode):
        old_operation_mode = vehicle_cache.get_vehicle_operation_mode(vehicle_name)
        if operation_mode == VehicleOperationMode.PROD:
            vehicle_cache.set_vehicle_operation_mode(vehicle_name, VehicleOperationMode.PROD)
        elif operation_mode == VehicleOperationMode.TEST:
            vehicle_cache.set_vehicle_operation_mode(vehicle_name, VehicleOperationMode.TEST)
        else:
            logger.warning(f"invalid operation_mode:{operation_mode}")
            return False
        if old_operation_mode != operation_mode:
            self.on_vehicle_operation_mode_changed(vehicle_name, old_operation_mode, operation_mode)
        return True

    def on_vehicle_operation_mode_changed(
        self, vehicle_name: str, old_operation_mode: VehicleOperationMode, new_operation_mode: VehicleOperationMode
    ):
        logger.info(
            f"on_vehicle_operation_mode_changed: vehicle_name:{vehicle_name}, old_operation_mode:{old_operation_mode}, new_operation_mode:{new_operation_mode}"
        )
        return

    def get_vehicle_modes(self, vehicle_names: List[str]) -> List[Optional[int]]:
        vehicle_operation_mode_dict = vehicle_cache.get_all_vehicle_operation_mode()
        vehicle_modes = []
        for vehicle_name in vehicle_names:
            operation_mode = vehicle_operation_mode_dict.get(vehicle_name)
            if operation_mode is not None:
                vehicle_modes.append(operation_mode)
            else:
                vehicle_modes.append(None)
        return vehicle_modes
