import time
import traceback

from sqlalchemy import <PERSON>umn, Integer, String, BIGINT
from sqlalchemy.ext.declarative import declarative_base

from common.logger import logger
from model.connection import MysqlSession,MysqlRemoteSession

Base = declarative_base()

class ServiceRunStatus(Base):
    __tablename__ = 'service_run_status'
    id = Column('id', Integer, primary_key=True, autoincrement=True)
    name  = Column('name', String(255))
    heart_beat_count = Column('heart_beat_count', BIGINT)
    start_time = Column('start_time', BIGINT)
    update_time = Column('update_time', BIGINT)
    running_time = Column('running_time', BIGINT)

def get_one():
    session = MysqlRemoteSession().acquire()
    try:
        info = session.query(ServiceRunStatus).first()
        return info
    finally:
        session.close()

def query_by_name(name):
    session = MysqlRemoteSession().acquire()
    try:
        info = session.query(ServiceRunStatus).filter(ServiceRunStatus.name == name).first()
        return info
    except Exception as e:
        logger.warning(f"Insert query_by_name err{e}, trace:{traceback.format_exc()}")
        return None
    finally:
        session.close()

def update_by_name(name, heart_beat_count, running_time, start_time = None):
    res = 0
    start = time.time()
    session = MysqlRemoteSession().acquire()
    try:
        if start_time is None:
            update_column = {"heart_beat_count":heart_beat_count, "update_time":int(time.time() * 1000), "running_time":running_time}
        else:
            update_column = {"heart_beat_count":heart_beat_count, "update_time":int(time.time() * 1000), "start_time":int(time.time() * 1000), "running_time":running_time}
        res = session.query(ServiceRunStatus).filter(ServiceRunStatus.name == name).update(update_column)
        if not res:
            status = ServiceRunStatus(name=name,
                                      heart_beat_count=heart_beat_count,
                                      start_time=int(time.time() * 1000),
                                      update_time=int(time.time() * 1000),
                                      running_time=running_time)
            logger.info(f"Insert ServiceRunStatus :{status.__dict__}")
            session.add(status)
        session.commit()
        return res
    except Exception as e:
        logger.warning(f"Insert update_by_name err{e}, trace:{traceback.format_exc()}")
        res = 0
    finally:
        session.close()
    end = time.time()
    if (end - start) > 1.0:
        logger.warning(f"ServiceRunStatus update_by_name over cost execeed round time:{round(end - start)}, execeed time:{end - start}")
    return res

def insert(name, heart_beat_count, running_time):
    session = MysqlRemoteSession().acquire()
    try:
        status = ServiceRunStatus(name=name,
                              heart_beat_count=heart_beat_count,
                              start_time=int(time.time() * 1000),
                              update_time=int(time.time() * 1000),
                              running_time=running_time)
        logger.info(f"Insert ServiceRunStatus :{status.__dict__}")
        session.add(status)
        session.commit()
    except Exception as e:
        logger.warning(f"Insert ServiceRunStatus err{e}, trace:{traceback.format_exc()}")
    finally:
        session.close()


def create_talbe():
    import pymysql
    pymysql.install_as_MySQLdb()
    from sqlalchemy_utils import database_exists, create_database, drop_database
    print(f"now start create_talbe ServiceRunStatus....")
    engine=MysqlSession().engine()
    # 1, 删除数据库
    # if  database_exists(engine.url):
    #     drop_database(engine.url)

    # 2, 创建新数据库
    if  not database_exists(engine.url):
        create_database(engine.url)

    ServiceRunStatus.metadata.drop_all(engine) # Create a table with multiple items
    ServiceRunStatus.metadata.create_all(engine)
    print(f"now end create_talbe ServiceRunStatus....")


if __name__ == '__main__':
    #create_talbe()
    heart_beat_count = 0
    running_time = 0
    while True:
        heart_beat_count += 1
        running_time += 1000
        ret = update_by_name("NB_WEB", heart_beat_count, running_time)
        print('ret:' + str(ret))
        info = query_by_name("NB_WEB")
        if info is not None:
            print(info.__dict__)
        else:
            print('get one query_by_name')
        time.sleep(1)

