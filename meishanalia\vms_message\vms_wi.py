import json
from common.logger import logger
from meishanalia.vms_message.msgs import from_json, WIMsg
from meishanalia.model import tos_wi_info
from meishanalia.model import table_info


def vms_wi_handler(msg):
    wi = from_json(WIMsg, msg)
    if hasattr(wi,'TRUCK_NO') and wi.TRUCK_NO.startswith('AT'):
        wi_record = table_info.TosWiInfo()
        for attr in dir(wi_record):
            if not attr.startswith('_') and hasattr(wi, attr):
                # if attr.upper() == "TRUCK_SEQ" and getattr(wi, attr) == "X":
                #     setattr(wi_record, attr, 9)  # TRUCK_SEQ=='X' 表示需要去排队，存为9
                # else:
                #     setattr(wi_record, attr, getattr(wi, attr))
                setattr(wi_record, attr, getattr(wi, attr))
        tos_wi_info.insert(wi_record)


if __name__ == '__main__':
    wi_j = '{"TRUCK_NO": "AT501", "WI_NO": 538898, "CTN_NO": "OOCU8103979", "TO_POS": "6640", \
      "WI_TYPE": "DSCH", "WI_ACT": "UNLOAD", "WI_STATUS": "DISPATCH", "TWIN_FLAG": "N", "DISPATCH_TIME": 1620817260, \
        "CANCEL_TIME": 0, "CONFIRMED_TIME": 1620817280,\
          "REMARK1": "None", "REMARK2": "None", "REMARK3": "None",\
            "TWIN_WI_NO": 0, "TWIN_CTN_NO": "None", "TRUCK_POS": "M", "EQUIT_TYPE": "45GP", "TEU": 2, "TRUCK_SEQ": 0, "VERSION": 3, "RESET_VERSION": 0,"LOCK_FLAG": "U", "LOCK_PAVILION": "None", "POW_NAME": "6YARD"}'
    vms_wi_handler(wi_j)

