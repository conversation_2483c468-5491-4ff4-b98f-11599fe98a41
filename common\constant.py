import os

# for tos test
'''
DB_IP = '*************'
DB_PORT = 1521
DB_SID = 'znlh'
TOS_DB_DSN = 'oracle://testrtg:testrtg@*************:1521/znlh'
DB_USER = 'testrtg'
DB_PASSWORD = 'testrtg'
'''

DB_IP = '***********'
DB_PORT = 1521
DB_SID = 'wrjk'
DB_USER = 'fabutech'
DB_PASSWORD = 'fabu1#%'
TOS_DB_DSN = 'oracle://fabutech:fabu1#%@***********:1521/wrjk'

GIS_DB_DSN = 'oracle://MSGIS:MSGIS@************:1521/msgis'

CRANE_MSG_HEAD = b'\xFE\xEF'
CRANE_MSG_END = b'\x0D\x0A'
GANTRY_MSG_HEAD = b'\xFE\xEF'
GANTRY_MSG_END = b'\x0D\xDA'
LISTEN_CRANE_PORT = 1234
LISTEN_GANTRY_PORT = 2000
LISTEN_CHANGE_PORT = 2090
DISPATCH_GRPC_PORT = 6789
FAST_DISPATCH_GRPC_PORT = 6788
WEB_HTTP_PORT = 6790
LOCAL_REDIS_PORT=os.environ.get("LOCAL_REDIS_PORT",6379)

#his_wi_info_4v_tos 归档
TABLE_CRANE_INFO = 'T_CRANE_INFO_4V'
TABLE_TOS_CMD = 'T_WI_INFO_4V_TOS'
TABLE_CMD_INFO = 'T_WI_INFO_4V_DC'
TABLE_TRUCK_INFO = 'T_TRUCK_INFO_4V'
TABLE_NTOS_CMD = 'ntos.NTOS_TRUCK_WI'
# TABLE_CRANE_INFO = 'T_TEST_CRANE_INFO_4V'
# TABLE_TOS_CMD = 'T_TEST_WI_INFO_4V_TOS'
# TABLE_CMD_INFO = 'T_TEST_WI_INFO_4V_DC'
# TABLE_TRUCK_INFO = 'T_TEST_TRUCK_INFO_4V'

ENVIRONMENT = os.environ.get("ENVIRONMENT")
SIM_MODE = os.environ.get("SIM_MODE") == 'true' or os.environ.get("SIM_MODE") == '1'

DEV_ID = os.environ.get("DEV_ID", 0)
MONITOR_ENV_DEV = os.environ.get("MONITOR_ENV") == 'MONITOR_DEV'
DEV_USER = os.environ.get("USER", "NONE")

YARD_BOX_LANE_WIDTH = 2.9  # m

YARD_BOX_LANE_THETA = 0.7637393850799011
CRANE_LANE_THETA = 0.7457379033763737
#桥吊大车位置离桥吊中心的距离
DIST_FROM_CENTER_TO_COUPLING_POINT = 5.593

# crane id and ip
CRANE_CAMERA_IP = {
    '31': 131,
    '32': 132,
    '33': 139,
    '34': 140,
    '35': 141,
    '36': 142,
    '37': 133,
    '38': 134,
    '39': 135,
    '40': 136,
    '41': 143,
    '42': 144,
    '43': 145,
    '44': 146,
    '45': 137,
    '46': 138,
    '47': 147,
    '48': 148,
    '49': 149,
    '50': 150,
    '51': 151,
    '52': 152,
    '53': 153,
    '54': 154,
    '55': 155,
    '56': 156,
}
CRANE_STREAM_TEMPLATE = "rtsp://root:pass@10.168.{}.{}:554/axis-media/media.amp?videocodec=h264"
NEW_PASSWORD_RULE_CRANES = [31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56]
NEW_CRANE_STREAM_TEMPLATE = "rtsp://root:Msqc%23{}_{}@10.168.{}.{}:554/axis-media/media.amp?videocodec=h264" # #转义为%23


# t500 before t501
IMU_OFFSET = 3.544

# test
MSGSM_WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bb5d53bd-e3c5-46ae-802f-74342e353c76"
#MSGSM_WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7f1dd26a-c860-42de-9466-74f2dfaaaae6"
#MSGSM_WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=13b450ab-d6cd-4881-96b5-2faeb1c8b61c"

NT_CRANE_GANTRY_WECHAT_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=47a1f4a7-aa7a-4c38-b1a0-0eab3fd8153d"
MS_CRANE_GANTRY_WECHAT_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d40677dd-1b3a-43f4-9dfb-a2f85d69e6a8"
V2V_WECHAT_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f5dbf3cd-acca-4ea2-9392-2a690abc73fd"


# test
#SHIP_MSGSM_WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7f1dd26a-c860-42de-9466-74f2dfaaaae6"
SHIP_MSGSM_WECHAT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2c0e9ffe-a781-4783-9ce4-08a4ee9d3413"

CRANE_CALIBRATOR_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c294117f-012e-429d-8385-056d67e403c1"

SIMULATION_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f0e2dbd5-97a9-4cf8-bc68-f94076f565a9"

NB_BACK_WEB_SERVER_01 = "NB_WEB"
NB_ANTENNA_SERVER_01 = "ANTENNA_SERVER_01"
NB_FABU_SERVER_01 ="FABU_SERVER_01"


KAFKA_PORT_VEHICLE_GPS_TOPIC = "gps_blctms_vehicleinfo"
#KAFKA_PORT_VEHICLE_GPS_TOPIC = "gps_blctms_fabu_vehicleinfo"
KAFKA_FABU_VEHICLE_GPS_TOPIC = "gps_blctms_fabu_vehicleinfo"
KAFKA_SERVERS_LIST = ['*************:9092',
                      '*************:9092', '*************:9092']
KAFKA_GROUP_ID = 'my_group_new'
INNER_KAFKA_GROUP_ID = 'inner_my_group_new'

ALIBABA_CLOUD_PHONE_ACCESS_KEY_ID = "LTAI4GKoMEBYj4HFmh8tpZbk"
ALIBABA_CLOUD_PHONE_ACCESS_KEY_SECRET = "******************************"
# alarm voice
ALIBABA_CLOUD_PHONE_ALARM_VOICE_CODE = '18d7a6df-e0c5-4765-b30d-406aa0ade081.wav'
# mengdejia
NB_BACK_WEB_SERVER_01_MAINTAINER_NUMBER = '17606591404'
#lizhicheng
NB_FABU_SERVER_01_MAINTAINER_NUMBER = '13567120091'
#7*24*60*60=604800,2*24*60*60=172800
COMMAND_EXPIRE_TIME = 172800
#1 day
COMMON_REDIS_EXPIRE_TIME = 86400
#15 day
COMMAND_LONG_EXPIRE_TIME = 1296000

#
CHASSIS_CACHE_INTERVAL_TIME = 59
CHECK_DELETE_CHASSIS_INTERVAL_TIME = 60 * 5
DEFAULT_LIMIT_MIN = 10

#  chassis pagination
DEFAULT_PAGE = 0
DEFAULT_PAGE_NUMBER = 20

#hour
TOS_EXPIRE_TIME_HOUR = 2

DOCKER_NAME = os.environ.get("DOCKER_NAME", "UNKNOW")

CHASSIS_SUPPOR_BY_FILE = True
CHASSIS_SAVE_TIME_DAYS = 3
CHASSIS_DATA_DIR = 'chassis_data'
NODE_NAME_PREFIX = 'Node'

KAFKA_DEBUG = False
NO_ECS_WEB_DEBUG = False

#纵向堆高机场地
# 9C,9D的堆高机场是L9和D9
STACKER_VERTICAL_YARDS = ['AY', 'BY', 'CY', 'DY', 'EY', 'AX', 'BX', '6Y', '7Y', '8Y', '9Y', 'CX', 'DX', 'EX', 'L9', 'D9','7X', '8X', '9X', 'E9','E8']
STACKER_VERTICAL_BAYS_E2W = ['14', '10']
STACKER_VERTICAL_BAYS_W2E = ['02', '06']
STACKER_VERTICAL_YARDS_BAYS = {yard:STACKER_VERTICAL_BAYS_E2W+STACKER_VERTICAL_BAYS_W2E for yard in STACKER_VERTICAL_YARDS}

#横向堆高机或正面吊场地
STACKER_HORIZONTAL_YARDS = ['XF','XG','7F','7G','7H','7K','9F','9G','8L','8M']



#是否支持以平板中心点为目标点
SUPPORT_TRAILER_CENTER_TERMINAL = False

#是否支持用新的接口.目前横向堆高机启用port box map新接口
SUPPORT_NEW_TRANSPOINT_API = True


TWO_YARD_BAY_LENGTH = 6.55 #两个贝的长度
STACKER_TAIL_CLOSE_ROAD_DISTANCE = 3 #堆高机车尾离作业贝位关路距离,与封禁后向一致
STACKER_HEAD_FORBID_AREAR_DISTANCE = 3 #堆高机前往封禁长度

VMS_LOCK_STATION_SETTING_SEND_TOPIC = "LOCK_STATION_SETTING_SEND"
VMS_LOCK_STATION_SETTING_TYPE = "LOCK_STATION_SETTING"
VMS_GPS_INFO_TOPIC = "ods_blctms_qx_gps_vehicleinfo"
VMS_GPS_INFO_TYPE = "FMS_GPS"
#ntos发送给fabu
NTOS_TO_FMS_TOPIC = "ods_blctms_ntos2fabu_info"
#预指令
FMS_PRE_WI_TYPE = "PRE_WI_SEND"

TOPIC_JOB_TYPE_MAPPING = {
    VMS_GPS_INFO_TOPIC: VMS_GPS_INFO_TYPE,
    VMS_LOCK_STATION_SETTING_SEND_TOPIC: VMS_LOCK_STATION_SETTING_TYPE,
    NTOS_TO_FMS_TOPIC: FMS_PRE_WI_TYPE
}
CHANGE_STATION_ID_MSBSS02 = "MSBSS02"
CHANGE_STATION_ID_MSBSS02_NUM = "155047"
