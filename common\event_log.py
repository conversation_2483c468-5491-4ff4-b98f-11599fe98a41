import datetime
import json
import time

from google.protobuf import json_format
from google.protobuf.message import Message

from cache.event_log import pop_event_log, push_event_log
from common.singleton import Singleton
from common.periodical_task import PeriodicalTask
from common.constant import NO_ECS_WEB_DEBUG
from cache.client import CacheClient
from config.config_manage import ConfigManage

EVENT_LOG_QUEUE_KEY = 'event-log-queue-key'


class MyEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Message):
            return json.loads(json_format.MessageToJson(o))
        if isinstance(o, datetime.datetime):
            return (o.timestamp())
        else:
            return super().default(o)


class EventLogger(metaclass=Singleton):
    def __init__(self):
        self._file = open('log/event_log.txt', 'a')
        self._loop_thread = PeriodicalTask(target=self.flush, interval=1)

    def start(self):
        self._loop_thread.start()

    def flush(self):
        while True:
            log_value = pop_event_log()
            if log_value is not None:
                log_value = log_value.decode('utf-8')
                self._file.write(log_value + '\n')
            else:
                break
        self._file.flush()


def log_event(log_type, content):
    line = {}
    line['type'] = log_type
    line['timestamp'] = time.time()
    line['content'] = content
    log_value = json.dumps(line, cls=MyEncoder)
    # for file
    # push_event_log(log_value)
    # 开发环境不需要，没有ecs-display配套的antenna-server单独部署线上环境使用仍有风险
    if not ConfigManage().is_dev_env() and not NO_ECS_WEB_DEBUG:
        CacheClient().client().lpush(EVENT_LOG_QUEUE_KEY, log_value)

def get_eventlog_length():
    return CacheClient().client().llen(EVENT_LOG_QUEUE_KEY) or 0


if __name__ == '__main__':
    log_event("test", json.dumps({"aaa": "bbb"}, cls=MyEncoder))
