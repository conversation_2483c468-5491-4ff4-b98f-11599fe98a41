#!/bin/bash
DOCKER_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source $DOCKER_PATH/common.sh
echo "${DOCKER_NAME} IP: $(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DOCKER_NAME})"
echo "${DB_DOCKER_NAME} IP: $(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DB_DOCKER_NAME})"
echo "${MYSQL_DB_DOCKER_NAME} IP: $(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${MYSQL_DB_DOCKER_NAME})"
echo "----------"

echo "PORT: "
function check_port() {
  docker port ${DOCKER_NAME} | sed "s/6789[^:]*/grpc/" | sed "s/6788[^:]*/fast-grpc/" | sed "s/6790[^:]*/web/" | \
                               sed "s/2000[^:]*/gantry/" | sed "s/1234[^:]*/crane/"
}
check_port
