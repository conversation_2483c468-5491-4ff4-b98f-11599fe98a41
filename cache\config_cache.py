import base64
import json
import sys

from google.protobuf.json_format import MessageToDict
from google.protobuf.text_format import MessageToString

from cache.client import Cache<PERSON>lient, ShareCacheClient
from common.logger import logger
from common.singleton import Singleton
from proto.antenna_pb2 import VesselDirection, WeatherConfiguration
from proto.cache_pb2 import (
    FunctionEnableConfig,
    GantryCtrlEnableConfig,
    OddConfig,
    StartWorkAlarmConfig,
    StartWorkAlarmConfigs,
    ChangeReqInfo
)

START_WORK_ARLARM_CONFIG_KEY = "config:start_work_alarms"  # 开工配置
VEHICLE_FLEET_CONFIG_KEY_PREFIX = "vehicle_fleet_"
WEATHER_CONFIG_KEY_PREFIX = "weather_config"
FUNCTION_ENABLE_CONFIG_KEY = "function_enable_config"
REAL_MAP_SHIP_CONFIG_KEY = "configuration:real-map-ship"
GANTRY_CTRL_MODE_ENABLE_CONFIG_KEY = (
    "configuration:gantry_ctrl_mode"  # 龙门吊干预使能总开关
)
GANTRIES_CTRL_ENABLE_CONFIG_SET_KEY = (
    "configuration:gantries_ctrl_enable_set"  # 单个龙门吊干预使能集合开关
)
LOCK_OFFSET_CONFIG_KEY = (
    "configuration:lock_offset"  # 锁亭对位点的修正值，往泊位为正，往泊位为负
)
ODD_CONFIG_KEY = "configuration:odd"  # ODD配置，格式：{"global_config": {"yards": ["71", "72"]}, "fleet_configs":  {"1": {"yards": ["73", "74"]}}}
GANTRY_CROSS_CONFIG_KEY = "configuration:gantry_cross_config"  # 龙门吊过场配置，格式：{“enabled": true, "forbid_gantries": [100, 101], "extend_distance": 9.4}
VP2V_CONFIG_KEY = "configuration:vp2v_config"  # VP2V 配置，格式：{"data_process_enable": true, "result_send_enable": true}
MS_CHANGE_STATION_PUBLIC_KEY = "platform:publicKey"
MS_CHANGE_MSG_QUEUE_PREFIX = "ms-change-req-info"
I2V_CONFIG_KEY = "configuration:i2v_config"  # I2V 配置，格式：{"data_process_enable": true, "result_send_enable": true}


class ConfigCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()
        self._share_redis = ShareCacheClient()

    def get_weather_config(self):
        return self._redis.get_proto(WEATHER_CONFIG_KEY_PREFIX, WeatherConfiguration)

    def set_start_work_alarm_config(self, config):
        key = f"{START_WORK_ARLARM_CONFIG_KEY}"
        return self._redis.set_proto(key, config)

    def get_start_work_alarm_config(self):
        key = f"{START_WORK_ARLARM_CONFIG_KEY}"
        return self._redis.get_proto(key, StartWorkAlarmConfigs)

    def get_vessel_direction_by_fleet_id(self, fleet_id):
        """
        brief: 根据车队查找开工配置内对应桥吊的弦靠
        """
        work_alarm_config = self.get_start_work_alarm_config()
        for config in work_alarm_config.configs:
            if not config.enable:
                continue
            for vehicle_fleet in config.vehicle_config.vehicle_fleets:
                if int(vehicle_fleet) == fleet_id:
                    return config.crane_configs[0].vessel_direction

        logger.info(
            f"get_vessel_direction_by_fleet_id failed, fleet_id: {fleet_id}, config: {MessageToString(work_alarm_config, as_one_line=True)}"
        )
        return VesselDirection.LEFT

    def clear_start_work_alarm_config(self):
        key = f"{START_WORK_ARLARM_CONFIG_KEY}"
        return self._redis.delete(key)

    def set_odd_config(self, config):
        key = f"{ODD_CONFIG_KEY}"
        return self._redis.set_proto_for_json_str(key, config)

    def get_odd_config(self):
        key = f"{ODD_CONFIG_KEY}"
        return self._redis.get_proto_from_json_str(key, OddConfig)

    def set_vehicle_fleet(self, vehicle_name, fleet: str):
        key = f"{VEHICLE_FLEET_CONFIG_KEY_PREFIX}" + f"{vehicle_name}"
        return self._redis.set_value(key, fleet)

    def get_vehicle_fleet(self, vehicle_name):
        key = f"{VEHICLE_FLEET_CONFIG_KEY_PREFIX}" + f"{vehicle_name}"
        return self._redis.get_value(key, str)

    def get_vehicle_fleet_map(self, vehicle_names):
        keys = [
            f"{VEHICLE_FLEET_CONFIG_KEY_PREFIX}" + f"{vehicle_name}"
            for vehicle_name in vehicle_names
        ]
        return dict(zip(vehicle_names, self._redis.mget_str(keys)))

    #####################function enable#############################
    def get_function_enalbe_config(self):
        key = f"{FUNCTION_ENABLE_CONFIG_KEY}"
        config = self._redis.get_proto(key, FunctionEnableConfig)
        if config is None:
            config = FunctionEnableConfig()
        dict_config = MessageToDict(
            config,
            including_default_value_fields=True,
            preserving_proto_field_name=True,
        )
        dict_config = {key.upper(): value for key, value in dict_config.items()}
        return dict_config

    def set_function_enalbe_config(self, config, func_key=None):
        key = f"{FUNCTION_ENABLE_CONFIG_KEY}"
        if func_key:
            all_config = self._redis.get_proto(key, FunctionEnableConfig)
            if not all_config:
                all_config = FunctionEnableConfig()
            setattr(all_config, func_key, config)
            return self._redis.set_proto(key, all_config)
        else:
            return self._redis.set_proto(key, config)

    def get_real_map_ship_enable_config(self):
        key = f"{REAL_MAP_SHIP_CONFIG_KEY}"
        value = self._redis.get(key)
        # 默认使能
        if value is not None:
            return value.decode().upper() == "TRUE"
        return True

    # 龙门吊干预使能总开关
    def get_gantry_ctrl_mode_enable_config(self):
        key = f"{GANTRY_CTRL_MODE_ENABLE_CONFIG_KEY}"
        value = self._share_redis.get(key)
        # 默认不使能
        if value is not None:
            return value.decode().upper() == "TRUE"
        return False

    def set_gantry_ctrl_mode_enable_config(self, enable=False):
        key = f"{GANTRY_CTRL_MODE_ENABLE_CONFIG_KEY}"
        value = "True" if enable else "False"
        return self._share_redis.set(key, value)

    # 更新支持龙门吊干预服务的龙门吊号,enable_gantries为新增的,disable_gantries为要删除的
    def update_gantries_ctrl_enable_config(
        self, enable_gantries=[], disable_gantries=[]
    ):
        key = f"{GANTRIES_CTRL_ENABLE_CONFIG_SET_KEY}"
        new_config = GantryCtrlEnableConfig()
        old_gantries = self.get_gantries_ctrl_enable_config()
        if len(enable_gantries) > 0:
            if len(old_gantries) > 0:
                enable_gantries.extend(old_gantries)
            new_config.gantry_no.extend(sorted(set(enable_gantries)))
            # logger.info(f"enable_gantries:{enable_gantries},config:{old_gantries},new_config:{MessageToString(new_config, as_one_line=True)}")
            return self._share_redis.set_proto_for_json_str(key, new_config)
        if len(disable_gantries) > 0:
            new_gantries = []
            if len(old_gantries) > 0:
                new_gantries = sorted(set(old_gantries) - set(disable_gantries))
            # logger.info(f"disable_gantries:{enable_gantries},config:{old_gantries},new_config:{MessageToString(new_config, as_one_line=True)}")
            new_config.gantry_no.extend(new_gantries)
            return self._share_redis.set_proto_for_json_str(key, new_config)

    # 获取支持干预的龙门吊号
    def get_gantries_ctrl_enable_config(self):
        key = f"{GANTRIES_CTRL_ENABLE_CONFIG_SET_KEY}"
        if config := self._share_redis.get_proto_from_json_str(
            key, GantryCtrlEnableConfig
        ):
            # logger.info(f"get_gantries_ctrl_enable_config:{MessageToString(config, as_one_line=True)}")
            return config.gantry_no
        else:
            return []

    def get_lock_offset_config(self, lock_name):
        key = f"{LOCK_OFFSET_CONFIG_KEY}"
        value = self._redis.get(key)
        lock_config = None
        try:
            lock_config_dict = json.loads(value) if value else {}
            lock_config = lock_config_dict.get(lock_name)
            if lock_config:
                MAX_LOCK_OFFSET = 5.0
                assert abs(lock_config[0]) < MAX_LOCK_OFFSET
                assert abs(lock_config[1]) < MAX_LOCK_OFFSET
        except Exception as e:
            logger.error(f"get_lock_offset_config parse error: {lock_config}, {e}")
            lock_config = None
        return lock_config

    def get_gantry_cross_config(self):
        key = f"{GANTRY_CROSS_CONFIG_KEY}"
        value = self._redis.get(key)
        try:
            gantry_cross_config = json.loads(value)
        except Exception as e:
            logger.error(f"get_gantry_cross_config parse error: {value}, {e}")
            gantry_cross_config = {}
        return gantry_cross_config

    def get_vp2v_config(self):
        key = f"{VP2V_CONFIG_KEY}"
        value = self._redis.get(key)
        try:
            vp2v_config = json.loads(value)
        except Exception as e:
            logger.error(f"get_vp2v_config parse error: {value}, {e}")
            vp2v_config = {}
        return vp2v_config
    
    def set_vp2v_config(self, config):
        key = f"{VP2V_CONFIG_KEY}"
        return self._redis.set_value(key, config)

#####################ms change station#############################
    def store_platform_key(self, public_key_pem: str):
        key = f"{MS_CHANGE_STATION_PUBLIC_KEY}"
        self._redis.set(key, public_key_pem)
        logger.debug(f"successful to set ms change station public_key")

    def get_platform_public_key(self):
        key = f"{MS_CHANGE_STATION_PUBLIC_KEY}"
        public_key_pem = self._redis.get(key)
        if public_key_pem:
            return public_key_pem.decode()
        else:
            logger.error("get ms change station public_key error")
            return None

    def store_platform_aes_info(self, aes_key: str, aes_iv: str):
        key_prefix = "MS_CHANGE_STATION_AES"
        self._redis.set(f"{key_prefix}_KEY", aes_key)
        self._redis.set(f"{key_prefix}_IV", aes_iv)
        logger.debug("successful to set platform AES key and IV")

    def get_platform_aes_info(self):
        key_prefix = "MS_CHANGE_STATION_AES"
        aes_key = self._redis.get(f"{key_prefix}_KEY")
        aes_iv = self._redis.get(f"{key_prefix}_IV")

        if not aes_key or not aes_iv:
            logger.error("Get platform AES key/IV error: key or iv missing")
            return None, None

        try:
            key_bytes = bytes.fromhex(aes_key.decode())
            iv_bytes  = bytes.fromhex(aes_iv.decode())
        except ValueError as e:
            logger.error(f"Hex decode error: {e}")
            return None, None

        return key_bytes, iv_bytes

    def push_ms_change_msg(self, msg):
        key = f"{MS_CHANGE_MSG_QUEUE_PREFIX}"
        return self._redis.rpush_proto(key, msg)

    def clear_ms_change_msg(self,message_len=None):
        key = f"{MS_CHANGE_MSG_QUEUE_PREFIX}"
        if message_len is None:
            message_len = self._redis.llen(key)
        self._redis.ltrim(key, message_len, -1)

    def pop_all_ms_change_msg(self,auto_delete=True):
        key = f"{MS_CHANGE_MSG_QUEUE_PREFIX}"
        messages = self._redis.lrange_proto(key, 0, -1, ChangeReqInfo)
        if auto_delete and (message_len:=len(messages)) > 0:
            self._redis.ltrim(key, message_len, -1)
        messages = list(filter(lambda mesg: mesg!=None, messages))
        return messages

    def get_i2v_config(self):
        key = f"{I2V_CONFIG_KEY}"
        value = self._redis.get(key)
        try:
            i2v_config = json.loads(value)
        except Exception as e:
            logger.error(f"get_i2v_config parse error: {value}, {e}")
            i2v_config = {}
        return i2v_config

    def set_i2v_config(self, config):
        key = f"{I2V_CONFIG_KEY}"
        return self._redis.set_value(key, config)


if __name__ == "__main__":

    if len(sys.argv) < 2:
        print("1:get config")
        print("2:set config")
        print("3:clear config")
        print("4:get_real_map_ship_enable_config")
        print(
            "5:获取龙门吊干预使能总开关(get_gantry_ctrl_mode_enable_config):python cache/config_cahce.py 5"
        )
        print(
            "6:设置龙门吊干预使能总开关(set_gantry_ctrl_mode_enable_config):python cache/config_cahce.py 6 true/false"
        )
        print(
            "7:获取支持干预的龙门吊(get_gantries_ctrl_enable_config):python cache/config_cahce.py 7\n\n"
            + "8:添加或删除支持干预的龙门吊(update_gantries_ctrl_enable_config):python cache/config_cahce.py 8 add/del [gantry_no]\n\n"
            + "   如添加170,171龙门吊干预:python cache/config_cahce.py 8 add 170,171\n\n"
            + "   如删除170,171龙门吊干预:python cache/config_cahce.py 8 del 170,171\n\n"
        )
        print(
            "9:获取线上可以支持干预的所有龙门吊信息(STORE_GANTRY_IP_MAP):python cache/config_cahce.py 9"
        )
        exit(1)

    mask = 0
    if sys.argv[1].startswith("0x"):
        mask = int(sys.argv[1], 16)
    else:
        mask = int(sys.argv[1])

    if mask == 1:
        config = ConfigCache().get_start_work_alarm_config()
        print(f"get config:{config}")

    if mask == 2:
        from proto.antenna_pb2 import VesselDirection

        alarm_config = StartWorkAlarmConfigs()
        ###
        config = StartWorkAlarmConfig()
        config.enable = True
        config.vehicle_config.business_scenes.append("port_meishan")
        config.vehicle_config.business_scenes.append("port_meishan_driverless")
        config.vehicle_config.versions.append("3.0-rc/47977f7d")
        config.vehicle_config.versions.append("3.0-rc/47977f7c")  # ok
        """
        config.yard_config.yards.append("70")
        config.yard_config.yards.append("66")
        config.yard_config.min_bay = 40
        config.yard_config.max_bay = 40
        """

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("70")
        yard_config.yards.append("66")
        yard_config.min_bay = 40
        yard_config.max_bay = 40
        config.yard_configs.append(yard_config)

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("X1")
        yard_config.yards.append("Y2")
        yard_config.min_bay = 40
        yard_config.max_bay = 80
        config.yard_configs.append(yard_config)

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("91")
        yard_config.yards.append("92")
        config.yard_configs.append(yard_config)

        crane_config = StartWorkAlarmConfig.CraneConfig()
        crane_config.crane_no = 35
        config.crane_configs.append(crane_config)
        crane_config = StartWorkAlarmConfig.CraneConfig()
        crane_config.crane_no = 38
        # lane_ids
        # vessel_direction
        crane_config.lane_ids.append(2)
        crane_config.lane_ids.append(3)
        crane_config.vessel_direction = VesselDirection.RIGHT
        config.crane_configs.append(crane_config)
        config.vehicle_config.vehicle_fleets.append("车队")
        alarm_config.configs.append(config)
        ###
        config = StartWorkAlarmConfig()
        config.enable = True
        config.vehicle_config.business_scenes.append("port_meishan")
        config.vehicle_config.business_scenes.append("port_meishan_driverless")
        config.vehicle_config.versions.append("3.0-rc/47977f7d")
        config.vehicle_config.versions.append("3.0-rc/47977f7c")  # ok
        """
        config.yard_config.yards.append("70")
        config.yard_config.yards.append("66")
        config.yard_config.min_bay = 40
        config.yard_config.max_bay = 40
        """

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("70")
        yard_config.yards.append("66")
        yard_config.min_bay = 40
        yard_config.max_bay = 40
        config.yard_configs.append(yard_config)

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("X1")
        yard_config.yards.append("Y2")
        yard_config.min_bay = 40
        yard_config.max_bay = 80
        config.yard_configs.append(yard_config)

        yard_config = StartWorkAlarmConfig.YardConfig()
        yard_config.yards.append("91")
        yard_config.yards.append("92")
        config.yard_configs.append(yard_config)

        crane_config = StartWorkAlarmConfig.CraneConfig()
        crane_config.crane_no = 35
        config.crane_configs.append(crane_config)
        crane_config = StartWorkAlarmConfig.CraneConfig()
        crane_config.crane_no = 38
        # lane_ids
        # vessel_direction
        crane_config.lane_ids.append(2)
        crane_config.lane_ids.append(3)
        crane_config.vessel_direction = VesselDirection.RIGHT
        config.crane_configs.append(crane_config)
        config.vehicle_config.vehicle_fleets.append("1")
        config.vehicle_config.vehicle_fleets.append("123")  # ok
        config.vehicle_config.vehicle_fleets.append("fabu车队")  # ok
        alarm_config.configs.append(config)
        ####
        ConfigCache().set_start_work_alarm_config(alarm_config)
        ###
        ConfigCache().set_vehicle_fleet("AT801", "车队")
        ConfigCache().set_vehicle_fleet("AT800", "123")
        print(f"set config:{alarm_config}")

    if mask == 3:
        ConfigCache().clear_start_work_alarm_config()
        config = ConfigCache().get_start_work_alarm_config()
        print(f"clear config:{config}")

    if mask == 4:
        print(
            f"get_real_map_ship_enable_config:{ConfigCache().get_real_map_ship_enable_config()}"
        )

    if mask == 5:
        print(
            f"get_gantry_ctrl_mode_enable_config:{ConfigCache().get_gantry_ctrl_mode_enable_config()}"
        )

    if mask == 6:
        if len(sys.argv) < 2 or sys.argv[2] not in ["true", "false"]:
            logger.info(f"please input true or false")
            exit(1)
        enable = True if sys.argv[2] == "true" else False
        print(
            f"设置龙门吊干预全局使能({enable}):{ConfigCache().set_gantry_ctrl_mode_enable_config(enable)}"
        )

    if mask == 7:
        print(f"支持干预的龙门吊:{ConfigCache().get_gantries_ctrl_enable_config()}")

    if mask == 8:
        usage = "添加或删除支持干预的龙门吊: python cache/config_cahce.py 8 add/del [gantry_no]"
        if len(sys.argv) < 4:
            logger.info(f"{usage}")
            exit(0)
        add_or_del = sys.argv[2]
        if add_or_del not in ["add", "del"]:
            logger.info(f"{usage}")
            exit(0)
        gantries = [int(gantry_no) for gantry_no in sys.argv[3].split(",")]
        if (
            not input(f"{add_or_del}支持干预龙门吊{gantries}.若确认无误,请按'Y'继续:")
            == "Y"
        ):
            exit()
        add_gantries = []
        del_gantries = []
        if add_or_del == "add":
            add_gantries = gantries
        else:
            del_gantries = gantries
        print(
            f"{ConfigCache().update_gantries_ctrl_enable_config(add_gantries,del_gantries)}"
        )

    if mask == 9:
        from plc.model import STORE_GANTRY_IP_MAP

        print(f"获取线上可以支持干预的所有龙门吊信息:{STORE_GANTRY_IP_MAP}")
