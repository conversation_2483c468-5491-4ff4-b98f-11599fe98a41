from common.singleton import Singleton
from cache.client import CacheClient
from proto.antenna_pb2 import InterventionRecord, OtaAntiFatigueConfig


ANTI_FATIGUE_CONFIG_KEY = 'start_antifatigue'
INTERVENTION_RECORD_KEY = 'intervention-event-queue-key'


class CloudCache(metaclass=Singleton):
    def __init__(self):
        self._redis = CacheClient()

    def set_ota_anti_fatigue_config(self, vehicle_name_at, config):
        key = f"{ANTI_FATIGUE_CONFIG_KEY}:{vehicle_name_at}"
        return self._redis.set_proto(key, config)

    def get_ota_anti_fatigue_config(self, vehicle_name_at):
        key = f"{ANTI_FATIGUE_CONFIG_KEY}:{vehicle_name_at}"
        return self._redis.get_proto(key, OtaAntiFatigueConfig)

    def lpush_intervention_record(self, vehicle_name_at, record):
        key = f"{INTERVENTION_RECORD_KEY}:{vehicle_name_at}"
        return self._redis.lpush_proto(key, record)

    def rpop_intervention_record(self, vehicle_name_at):
        key = f"{INTERVENTION_RECORD_KEY}:{vehicle_name_at}"
        return self._redis.rpop_proto(key, InterventionRecord)


if __name__ == '__main__':
    # config = OtaAntiFatigueConfig()
    # config.is_initiate = True
    # config.start_time = 10
    # config.end_time = 22
    # print(CloudCache().set_ota_anti_fatigue_config('AT801', config))
    print(CloudCache().get_ota_anti_fatigue_config('AT801'))

    # record = InterventionRecord()
    # record.sequence_num = 1
    # record.vehicle = 'AT801'
    # record.operator_name = 'test'
    # record.start_timestamp_sec = 1
    # record.end_timestamp_sec = 1
    # record.reason_type = 1
    # record.reason = 'test'
    # record.module_name = 'test'
    # record.operation_type = 1
    # record.operation_result = 1
    # record.release_type = '1'
    # CloudCache().lpush_intervention_record('AT801', record)
    # print(CloudCache().rpop_intervention_record('AT801'))
